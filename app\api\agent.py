from fastapi import APIRouter
from app.models.schemas import AgentGenRequest, AgentGenResponse, TaskName
from app.core.state_manager import (
    task_manager, TaskStatus, create_stream_data
)
from app.services.question_cloner import clone_question
from app.utils.workflow_utils import extract_model_config_from_workflow
import uuid
import asyncio
import time
from typing import Dict, Any
from loguru import logger
# 调用智能命题服务
from app.services.intelligent_question_service import intelligent_question_service

router = APIRouter()


@router.post("/AgentGen", response_model=AgentGenResponse)
async def agent_gen(request: AgentGenRequest):
    """
    智能命题/克隆/模型交互主业务接口

    工作流程：
    1. 立即创建任务并存储任务ID，确保前端可以立即调用QueryStream查询
    2. 异步等待任务处理完成（不阻塞其他请求）
    3. 返回最终的任务结果

    特点：
    - 前端可以立即开始流式查询（QueryStream）
    - 接口会等待任务完成并返回结果
    - 使用异步等待，不阻塞服务器处理其他请求
    """
    # 生成唯一任务ID
    task_id = request.TaskId or str(uuid.uuid4())

    # 创建任务
    await task_manager.create_task(task_id)

    # 立即更新任务状态为处理中，确保前端可以立即查询到任务状态
    await task_manager.update_task_status(
        task_id,
        TaskStatus.PROCESSING,
        step_info={"step": "task_created", "progress": 0,
                   "detail": f"任务已创建，开始处理{request.TaskName}"}
    )

    try:
        # 根据任务类型同步处理
        if request.TaskName == "智能命题":
            result = await process_intelligent_question_sync(task_id, request)
            # 统一返回结构
            data = result
        elif request.TaskName == "试题克隆":
            result = await process_question_clone_sync(task_id, request)
            data = result
        elif request.TaskName == "模型交互":
            result = await process_model_interaction_sync(task_id, request)
            data = {"ChatContent": result.get("ChatContent", "")}
        else:
            raise ValueError(f"不支持的任务类型: {request.TaskName}")

        # 标记流式输出完成
        await task_manager.mark_stream_completed(task_id)

        # 返回处理结果
        return AgentGenResponse(
            Data=data,
            code=200,
            msg="success"
        )

    except Exception as e:
        # 更新任务状态为失败
        await task_manager.update_task_status(task_id, TaskStatus.FAILED, error_message=str(e))
        # 标记流式输出完成
        await task_manager.mark_stream_completed(task_id)

        return AgentGenResponse(
            Data={
                "TaskId": task_id,
                "State": "failed",
                "Msg": f"任务处理失败: {str(e)}",
                "Result": None
            },
            code=500,
            msg="error"
        )


async def process_intelligent_question_sync(task_id: str, request: AgentGenRequest):
    """
    同步处理智能命题任务，使用真实LLM服务
    """
    try:
        # 更新任务状态为处理中
        await task_manager.update_task_status(task_id, TaskStatus.PROCESSING)

        # 定义流式回调函数
        async def stream_thinking_callback(thinking_data: Dict[str, Any]):
            # 检查是否为完成标记
            if thinking_data is False:
                # 标记流式输出完成
                await task_manager.mark_stream_completed(task_id)
                return

            await task_manager.add_stream_data(task_id, create_stream_data(
                stream_type="THINKING",
                message=thinking_data.get("message", "思考中..."),
                data=thinking_data.get("data", {})
            ))
            # 移除延迟，确保数据能够及时推送到队列
            # await asyncio.sleep(0.01)

        # 定义状态更新回调函数
        async def status_update_callback(status: TaskStatus, message: str, data: Dict[str, Any]):
            # 更新任务状态，包含步骤信息
            await task_manager.update_task_status(
                task_id,
                status,
                result=data,
                step_info={
                    "step": data.get("step"),
                    "progress": data.get("progress", 0),
                    "message": message,
                    "detail": data.get("detail", "")
                }
            )
            # 注意：不再推送状态更新到流式队列，只通过状态回调更新任务状态

        # 解析试题结构
        if not request.QuesPost or not request.QuesPost.get("AiQuesTypePost"):
            raise ValueError("智能命题任务缺少QuesPost数据")

        # # 提取模型配置
        # workflow_model_config = extract_model_config_from_workflow(
        #     request.WorkflowPost) if request.WorkflowPost else None

        result = await intelligent_question_service.generate_questions(
            ques_post=request.QuesPost,
            workflow_config=request.WorkflowPost,
            stream_callback=stream_thinking_callback,
            status_callback=status_update_callback
        )

        # 更新任务状态为完成
        await task_manager.update_task_status(task_id, TaskStatus.COMPLETED, result=result)

        # 标记流式输出完成
        await task_manager.mark_stream_completed(task_id)

        # 返回QuesPostData结构
        return {
            "QuesPostData": {
                "AiQuesTypePost": result["AiQuesTypePost"],
                "ExamSubjectName": request.QuesPost.get("ExamSubjectName", ""),
                "ExamProjectName": request.QuesPost.get("ExamProjectName", "")
            }
        }

    except Exception as e:
        await task_manager.update_task_status(task_id, TaskStatus.FAILED, error_message=str(e))
        # 标记流式输出完成
        await task_manager.mark_stream_completed(task_id)
        raise e


# 删除模拟函数，使用真实LLM服务


async def process_question_clone_sync(task_id: str, request: AgentGenRequest):
    """
    同步处理试题克隆任务
    """
    try:
        # 更新任务状态为处理中
        await task_manager.update_task_status(task_id, TaskStatus.PROCESSING)

        # 定义状态更新回调函数
        async def status_update_callback(status: TaskStatus, message: str, data: Dict[str, Any]):
            # 更新任务状态，包含步骤信息
            await task_manager.update_task_status(
                task_id,
                status,
                result=data,
                step_info={
                    "step": data.get("step"),
                    "progress": data.get("progress", 0),
                    "message": message,
                    "detail": data.get("detail", "")
                }
            )

        # 调用试题克隆服务
        result = await clone_question(
            original_question=request.QuesPost.get("AiQuesTypePost", {}),
            strategy="full",
            status_callback=status_update_callback,
            exam_subject_name=request.QuesPost.get("ExamSubjectName", "语文"),
            workflow_model_config=extract_model_config_from_workflow(
                request.WorkflowPost) if request.WorkflowPost else None
        )

        # 更新任务状态为完成
        await task_manager.update_task_status(task_id, TaskStatus.COMPLETED, result=result)

        # 标记流式输出完成
        await task_manager.mark_stream_completed(task_id)

        # 返回QuesPostData结构
        return {
            "QuesPostData": {
                "AiQuesTypePost": result["AiQuesTypePost"],
                "ExamSubjectName": request.QuesPost.get("ExamSubjectName", ""),
                "ExamProjectName": request.QuesPost.get("ExamProjectName", "")
            }
        }

    except Exception as e:
        await task_manager.update_task_status(task_id, TaskStatus.FAILED, error_message=str(e))
        # 标记流式输出完成
        await task_manager.mark_stream_completed(task_id)
        raise e


async def process_model_interaction_sync(task_id: str, request: AgentGenRequest):
    """
    同步处理模型交互任务
    """
    try:
        # 更新任务状态为处理中
        await task_manager.update_task_status(task_id, TaskStatus.PROCESSING)

        # 定义流式回调函数
        async def stream_thinking_callback(thinking_data: Dict[str, Any]):
            # 检查是否为完成标记
            if thinking_data is False:
                # 标记流式输出完成
                await task_manager.mark_stream_completed(task_id)
                return

            await task_manager.add_stream_data(task_id, create_stream_data(
                stream_type="THINKING",
                message=thinking_data.get("message", "思考中..."),
                data=thinking_data.get("data", {})
            ))
            # 移除延迟，确保数据能够及时推送到队列
            # await asyncio.sleep(0.01)

        # 提取模型配置
        workflow_model_config = extract_model_config_from_workflow(
            request.WorkflowPost) if request.WorkflowPost else None

        # 调用LLM进行交互
        from app.services.llm_manager import generate_text_stream

        chat_content = request.AgentPost.get("ChatContent", "")
        if not chat_content:
            raise ValueError(
                "ChatContent cannot be empty for model interaction")

        result = ""
        if workflow_model_config:
            model_name = workflow_model_config.get("name", "deepseek-r1")
            api_key = workflow_model_config.get("api_key", "")
            api_base = workflow_model_config.get("api_base", "")
            additional_params = {
                "top_p": workflow_model_config.get("top_p", 0.5),
                "top_k": workflow_model_config.get("top_k", 5)
            }
            async for chunk in generate_text_stream(chat_content, model_name=model_name, stream_callback=stream_thinking_callback, api_key=api_key, api_base=api_base, **additional_params):
                result += chunk
        else:
            async for chunk in generate_text_stream(chat_content, stream_callback=stream_thinking_callback):
                result += chunk

        # 更新任务状态为完成
        await task_manager.update_task_status(task_id, TaskStatus.COMPLETED, result={"ChatContent": result})

        # 标记流式输出完成
        await task_manager.mark_stream_completed(task_id)

        # 返回ChatContent结构
        return {
            "ChatContent": result
        }

    except Exception as e:
        await task_manager.update_task_status(task_id, TaskStatus.FAILED, error_message=str(e))
        # 标记流式输出完成
        await task_manager.mark_stream_completed(task_id)
        raise e
