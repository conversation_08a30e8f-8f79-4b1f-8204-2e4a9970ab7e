"""
LLM模型管理器
支持多种模型的统一调用接口
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any, Optional, List, Callable
from loguru import logger
from langchain_openai import ChatOpenAI
from langchain_deepseek import ChatDeepSeek
from langchain_core.callbacks import BaseCallbackHandler
from app.utils.config_manager import config_manager, get_model_service_mode


class StreamingCallbackHandler(BaseCallbackHandler):
    """流式回调处理器"""

    def __init__(self):
        self.content = ""
        self.reasoning_content = ""

    def on_llm_new_token(self, token: str, **kwargs) -> None:
        """处理新的token"""
        self.content += token

    def on_llm_end(self, response, **kwargs) -> None:
        """处理LLM结束"""
        pass


class LLMManager:
    """LLM模型管理器"""

    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.default_model = config_manager.get(
            "models.default", "deepseek-r1")

    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()

    async def generate_text(self, prompt: str, model_name: Optional[str] = None,
                            api_key: Optional[str] = None, api_base: Optional[str] = None,
                            **kwargs) -> str:
        """
        生成文本

        Args:
            prompt: 提示词
            model_name: 模型名称
            **kwargs: 其他参数

        Returns:
            生成的文本内容
        """
        try:
            if not model_name:
                model_name = self.default_model

            # 优先使用传入的API配置，否则使用配置文件中的配置
            if api_key and api_base:
                # 使用workflow中的配置
                # 只提取支持的参数，避免传递不支持的参数
                supported_params = ['max_tokens', 'temperature']
                model_config = {
                    "name": model_name,
                    "api_key": api_key,
                    "api_base": api_base
                }
                # 只添加支持的参数
                for param in supported_params:
                    if param in kwargs:
                        model_config[param] = kwargs[param]
                    else:
                        # 设置默认值
                        if param == 'max_tokens':
                            model_config[param] = 4000
                        elif param == 'temperature':
                            model_config[param] = 0.7
            else:
                # 使用配置文件中的配置
                model_config = config_manager.get_model_config(model_name)
                if not model_config:
                    raise ValueError(f"未找到模型配置: {model_name}")

            # 根据模型服务的模式（在线/离线）和模型类型调用相应的API
            mode = config_manager.get("model_service.mode", "offline").lower()
            model_name_lower = model_name.lower()

            if mode == "offline":
                # 离线模式统一走DeepSeek（本地/私有化部署）
                return await self._call_deepseek_api(prompt, model_config, **kwargs)
            else:
                # 在线模式根据模型类型分发
                if "deepseek" in model_name_lower:
                    return await self._call_deepseek_api(prompt, model_config, **kwargs)
                elif "gpt" in model_name_lower or "openai" in model_name_lower:
                    return await self._call_openai_api(prompt, model_config, **kwargs)
                elif "qwen" in model_name_lower:
                    return await self._call_dashscope_api(prompt, model_config, **kwargs)
                else:
                    # 默认使用DeepSeek格式
                    return await self._call_deepseek_api(prompt, model_config, **kwargs)

        except Exception as e:
            logger.error(f"LLM调用失败: {e}")
            raise

    async def generate_text_with_stream(self, prompt: str, model_name: Optional[str] = None,
                                        stream_callback: Optional[Callable] = None,
                                        api_key: Optional[str] = None, api_base: Optional[str] = None,
                                        **kwargs):
        """
        流式生成文本（生成器），支持思考过程提取

        Args:
            prompt: 提示词
            model_name: 模型名称
            stream_callback: 流式回调函数，用于推送思考过程
            **kwargs: 其他参数

        Yields:
            生成的文本片段
        """
        try:
            if not model_name:
                model_name = self.default_model

            # 优先使用传入的API配置，否则使用配置文件中的配置
            if api_key and api_base:
                # 使用workflow中的配置
                # 只提取支持的参数，避免传递不支持的参数
                supported_params = ['max_tokens', 'temperature']
                model_config = {
                    "name": model_name,
                    "api_key": api_key,
                    "api_base": api_base
                }
                # 只添加支持的参数
                for param in supported_params:
                    if param in kwargs:
                        model_config[param] = kwargs[param]
                    else:
                        # 设置默认值
                        if param == 'max_tokens':
                            model_config[param] = 4000
                        elif param == 'temperature':
                            model_config[param] = 0.7
            else:
                # 使用配置文件中的配置
                model_config = config_manager.get_model_config(model_name)
                if not model_config:
                    raise ValueError(f"未找到模型配置: {model_name}")

            # 初始化状态变量
            reasoning_content = ""
            done_content = ""
            in_reasoning = False
            reasoning_buffer = ""

            # 根据模型类型选择不同的流式处理方式
            if "deepseek" in model_name.lower():
                # DeepSeek模型使用原有的推理过程处理
                async for chunk in self._call_deepseek_stream_api(prompt, model_config, stream_callback, **kwargs):
                    yield chunk
            else:
                # 其他模型使用通用的流式处理，但也要处理<think></think>标签
                async for chunk in self._call_generic_stream_api(prompt, model_config, stream_callback, reasoning_content, done_content, in_reasoning, reasoning_buffer, **kwargs):
                    yield chunk

        except Exception as e:
            logger.error(f"LLM流式调用失败: {e}")
            raise

    async def _call_deepseek_api(self, prompt: str, model_config: Dict[str, Any],
                                 **kwargs) -> str:
        """调用DeepSeek API"""
        if not self.session:
            raise RuntimeError("LLM管理器未初始化，请使用异步上下文管理器")

        url = f"{model_config['api_base']}/chat/completions"
        headers = {
            "Authorization": f"Bearer {model_config['api_key']}",
            "Content-Type": "application/json"
        }

        data = {
            "model": model_config["name"],
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": kwargs.get("max_tokens", model_config.get("max_tokens", 4000)),
            "temperature": kwargs.get("temperature", model_config.get("temperature", 0.7)),
            "stream": False
        }

        async with self.session.post(url, headers=headers, json=data) as response:
            if response.status != 200:
                error_text = await response.text()
                raise Exception(
                    f"DeepSeek API调用失败: {response.status} - {error_text}")

            result = await response.json()
            return result["choices"][0]["message"]["content"]

    async def _call_openai_api(self, prompt: str, model_config: Dict[str, Any],
                               **kwargs) -> str:
        """调用OpenAI API"""
        if not self.session:
            raise RuntimeError("LLM管理器未初始化，请使用异步上下文管理器")

        url = f"{model_config['api_base']}/chat/completions"
        headers = {
            "Authorization": f"Bearer {model_config['api_key']}",
            "Content-Type": "application/json"
        }

        data = {
            "model": model_config["name"],
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": kwargs.get("max_tokens", model_config.get("max_tokens", 4000)),
            "temperature": kwargs.get("temperature", model_config.get("temperature", 0.7)),
            "stream": False
        }

        async with self.session.post(url, headers=headers, json=data) as response:
            if response.status != 200:
                error_text = await response.text()
                raise Exception(
                    f"OpenAI API调用失败: {response.status} - {error_text}")

            result = await response.json()
            return result["choices"][0]["message"]["content"]

    async def _call_dashscope_api(self, prompt: str, model_config: Dict[str, Any],
                                  **kwargs) -> str:
        """调用DashScope API"""
        if not self.session:
            raise RuntimeError("LLM管理器未初始化，请使用异步上下文管理器")

        url = f"{model_config['api_base']}/services/aigc/text-generation/generation"
        headers = {
            "Authorization": f"Bearer {model_config['api_key']}",
            "Content-Type": "application/json"
        }

        data = {
            "model": model_config["name"],
            "input": {
                "messages": [{"role": "user", "content": prompt}]
            },
            "parameters": {
                "max_tokens": kwargs.get("max_tokens", model_config.get("max_tokens", 4000)),
                "temperature": kwargs.get("temperature", model_config.get("temperature", 0.7))
            }
        }

        async with self.session.post(url, headers=headers, json=data) as response:
            if response.status != 200:
                error_text = await response.text()
                raise Exception(
                    f"DashScope API调用失败: {response.status} - {error_text}")

            result = await response.json()
            return result["output"]["text"]

    async def _call_deepseek_stream_api(self, prompt: str, model_config: Dict[str, Any],
                                        stream_callback: Optional[Callable] = None, **kwargs):
        """调用DeepSeek流式API"""
        if not self.session:
            raise RuntimeError("LLM管理器未初始化，请使用异步上下文管理器")

        url = f"{model_config['api_base']}/chat/completions"
        headers = {
            "Authorization": f"Bearer {model_config['api_key']}",
            "Content-Type": "application/json"
        }

        data = {
            "model": model_config["name"],
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": kwargs.get("max_tokens", model_config.get("max_tokens", 4000)),
            "temperature": kwargs.get("temperature", model_config.get("temperature", 0.7)),
            "stream": True
        }

        accumulated_content = ""
        reasoning_content = ""
        done_content = ""
        in_reasoning = False
        reasoning_buffer = ""

        # 减少初始等待时间，提高响应速度
        logger.info("开始DeepSeek流式API调用")
        # 移除不必要的等待时间

        async with self.session.post(url, headers=headers, json=data) as response:
            if response.status != 200:
                error_text = await response.text()
                raise Exception(
                    f"DeepSeek流式API调用失败: {response.status} - {error_text}")

            logger.info("DeepSeek流式连接建立成功，开始接收数据...")
            async for line in response.content:
                line = line.decode('utf-8').strip()
                if line.startswith('data: '):
                    data_str = line[6:]
                    if data_str == '[DONE]':
                        logger.info("收到DeepSeek流式完成标记")
                        break

                    try:
                        chunk_data = json.loads(data_str)
                        if 'choices' in chunk_data and chunk_data['choices']:
                            delta = chunk_data['choices'][0].get('delta', {})
                            if 'content' in delta:
                                content_chunk = delta['content']
                                accumulated_content += content_chunk

                                # 处理推理内容（DeepSeek特有）
                                if hasattr(chunk_data, 'additional_kwargs') and 'reasoning_content' in chunk_data.get('additional_kwargs', {}):
                                    reasoning_delta = chunk_data['additional_kwargs'].get(
                                        'reasoning_content', '')
                                    if reasoning_delta:
                                        reasoning_content += reasoning_delta
                                        if stream_callback:
                                            # 立即推送，不等待
                                            await stream_callback({
                                                "type": "thinking",
                                                "content": reasoning_delta,
                                                "step": "reasoning",
                                                "accumulated": reasoning_content,
                                                "timestamp": asyncio.get_event_loop().time()
                                            })
                                            # 减少等待时间，提高响应速度
                                            await asyncio.sleep(0.005)
                                        yield reasoning_delta
                                        continue

                                # 处理<think></think>标签
                                if "<think>" in content_chunk:
                                    in_reasoning = True
                                    content_parts = content_chunk.split(
                                        "<think>")
                                    if len(content_parts) > 1:
                                        done_content += content_parts[0]
                                        reasoning_buffer = content_parts[1]
                                    continue

                                if "</think>" in content_chunk and in_reasoning:
                                    in_reasoning = False
                                    content_parts = content_chunk.split(
                                        "</think>")
                                    reasoning_buffer += content_parts[0]
                                    if reasoning_buffer:
                                        reasoning_content += reasoning_buffer
                                        if stream_callback:
                                            # 立即推送，不等待
                                            await stream_callback({
                                                "type": "thinking",
                                                "content": reasoning_buffer,
                                                "step": "thinking_complete",
                                                "accumulated": reasoning_content,
                                                "timestamp": asyncio.get_event_loop().time()
                                            })
                                            # 减少等待时间，提高响应速度
                                            await asyncio.sleep(0.005)
                                        yield reasoning_buffer
                                        reasoning_buffer = ""
                                    if len(content_parts) > 1:
                                        done_content += content_parts[1]
                                    continue

                                if in_reasoning:
                                    reasoning_buffer += content_chunk
                                    if stream_callback:
                                        # 立即推送，不等待
                                        await stream_callback({
                                            "type": "thinking",
                                            "content": content_chunk,
                                            "step": "thinking",
                                            "accumulated": reasoning_content + reasoning_buffer,
                                            "timestamp": asyncio.get_event_loop().time()
                                        })
                                        # 减少等待时间，提高响应速度
                                        await asyncio.sleep(0.005)
                                    yield content_chunk
                                    continue

                                # 处理普通内容
                                done_content += content_chunk
                                yield content_chunk

                    except json.JSONDecodeError:
                        continue

            # 确保所有推理内容都被推送
            if reasoning_buffer and stream_callback:
                logger.info("推送剩余的推理内容...")
                # await asyncio.sleep(0.1)  # 等待一小段时间
                await stream_callback({
                    "type": "thinking",
                    "content": reasoning_buffer,
                    "step": "thinking_complete",
                    "accumulated": reasoning_content + reasoning_buffer,
                    "timestamp": asyncio.get_event_loop().time()
                })
                logger.info("剩余推理内容推送完成")

    async def _call_generic_stream_api(self, prompt: str, model_config: Dict[str, Any],
                                       stream_callback: Optional[Callable], reasoning_content: str,
                                       done_content: str, in_reasoning: bool, reasoning_buffer: str, **kwargs):
        """
        通用模型的流式API调用，支持思考过程提取
        """
        try:
            # 使用LangChain的流式调用
            llm = self._create_llm_instance(model_config, **kwargs)

            # 设置流式回调处理器
            streaming_handler = StreamingCallbackHandler()
            original_callbacks = []
            if hasattr(llm, 'callbacks'):
                original_callbacks = llm.callbacks
            llm.callbacks = [streaming_handler]

            # 准备消息
            messages = [{"role": "user", "content": prompt}]

            try:
                response_gen = llm.stream(messages)

                # 使用普通for循环，因为llm.stream返回的是普通生成器
                for chunk in response_gen:
                    # print("origin_chunk", chunk)
                    # 处理推理内容（DeepSeek特有）
                    if hasattr(chunk, 'additional_kwargs') and 'reasoning_content' in chunk.additional_kwargs:
                        reasoning_delta = chunk.additional_kwargs.get(
                            'reasoning_content', '')
                        if reasoning_delta:
                            reasoning_content += reasoning_delta
                            if stream_callback:
                                # 立即推送，不等待
                                await stream_callback({
                                    "type": "thinking",
                                    "content": reasoning_delta,
                                    "step": "reasoning",
                                    "accumulated": reasoning_content,
                                    "timestamp": asyncio.get_event_loop().time()
                                })
                            yield reasoning_delta
                            continue

                    # 处理Qwen3推理内容
                    if hasattr(chunk, 'additional_kwargs') and 'thinking' in chunk.additional_kwargs:
                        thinking_delta = chunk.additional_kwargs.get(
                            'thinking', '')
                        if thinking_delta:
                            reasoning_content += thinking_delta
                            if stream_callback:
                                # 立即推送，不等待
                                await stream_callback({
                                    "type": "thinking",
                                    "content": thinking_delta,
                                    "step": "qwen_thinking",
                                    "accumulated": reasoning_content,
                                    "timestamp": asyncio.get_event_loop().time()
                                })
                            yield thinking_delta
                            continue

                    # 获取内容
                    content = chunk.content if hasattr(
                        chunk, 'content') else str(chunk)
                    if not content:
                        continue

                    # 处理<think></think>标签
                    if "<think>" in content:
                        in_reasoning = True
                        content_parts = content.split("<think>")
                        if len(content_parts) > 1:
                            done_content += content_parts[0]
                            reasoning_buffer = content_parts[1]
                        continue

                    if "</think>" in content and in_reasoning:
                        in_reasoning = False
                        content_parts = content.split("</think>")
                        reasoning_buffer += content_parts[0]
                        if reasoning_buffer:
                            reasoning_content += reasoning_buffer
                            if stream_callback:
                                # 立即推送，不等待
                                await stream_callback({
                                    "type": "thinking",
                                    "content": reasoning_buffer,
                                    "step": "thinking_complete",
                                    "accumulated": reasoning_content,
                                    "timestamp": asyncio.get_event_loop().time()
                                })
                            yield reasoning_buffer
                            reasoning_buffer = ""
                        if len(content_parts) > 1:
                            done_content += content_parts[1]
                        continue

                    if in_reasoning:
                        reasoning_buffer += content
                        if stream_callback:
                            # 立即推送，不等待
                            await stream_callback({
                                "type": "thinking",
                                "content": content,
                                "step": "thinking",
                                "accumulated": reasoning_content + reasoning_buffer,
                                "timestamp": asyncio.get_event_loop().time()
                            })
                        yield content
                        continue

                    # 处理普通内容
                    done_content += content
                    # 对于普通内容，不推送到流式回调，只yield
                    yield content

                # 确保所有推理内容都被推送
                if reasoning_buffer and stream_callback:
                    await stream_callback({
                        "type": "thinking",
                        "content": reasoning_buffer,
                        "step": "thinking_complete",
                        "accumulated": reasoning_content + reasoning_buffer,
                        "timestamp": asyncio.get_event_loop().time()
                    })

            finally:
                # 恢复原始callbacks
                try:
                    llm.callbacks = original_callbacks
                except Exception as e:
                    logger.warning(f"恢复callbacks时出错：{str(e)}")

        except Exception as e:
            logger.error(f"通用流式API调用失败: {e}")
            raise

    def _create_llm_instance(self, model_config: Dict[str, Any], **kwargs) -> Any:
        """
        创建LLM实例
        """
        # 过滤掉可能冲突的参数，避免重复传递
        # 包括模型特定参数和可能不被支持的参数
        excluded_params = [
            'temperature', 'max_tokens', 'model', 'api_key', 'base_url',
            'top_k', 'top_p', 'frequency_penalty', 'presence_penalty',
            'stop', 'n', 'logit_bias', 'user', "enable_thinking"
        ]
        filtered_kwargs = {k: v for k,
                           v in kwargs.items() if k not in excluded_params}

        # 根据配置文件判断是否为离线模式
        mode = get_model_service_mode()
        model_name = model_config.get("name", "").lower()

        if mode == "offline":
            # 离线模式一律用ChatDeepSeek
            return ChatDeepSeek(
                model=model_config.get("model_name", "deepseek-chat"),
                temperature=model_config.get("temperature", 0.7),
                max_tokens=model_config.get("max_tokens", 2048),
                api_key=model_config.get("api_key"),
                api_base=model_config.get("api_base"),
                **filtered_kwargs
            )

        # 在线模式下按原有逻辑
        if "gpt" in model_name or "openai" in model_name:
            return ChatOpenAI(
                model=model_config.get("model_name", "gpt-3.5-turbo"),
                temperature=model_config.get("temperature", 0.7),
                max_tokens=model_config.get("max_tokens", 2048),
                api_key=model_config.get("api_key"),
                base_url=model_config.get("api_base"),
                **filtered_kwargs
            )
        elif "qwen3" in model_name:
            # 检查是否启用推理功能
            enable_thinking = kwargs.get("enable_thinking", False)

            # 构建extra_body参数
            extra_body = {}
            if enable_thinking:
                extra_body = {"chat_template_kwargs": {
                    "enable_thinking": True}}
            else:
                extra_body = {"chat_template_kwargs": {
                    "enable_thinking": False}}

            return ChatDeepSeek(
                model=model_config.get("model_name", "qwen-turbo"),
                temperature=model_config.get("temperature", 0.7),
                max_tokens=model_config.get("max_tokens", 2048),
                api_key=model_config.get("api_key"),
                extra_body=extra_body,
                api_base=model_config.get("api_base"),
                **filtered_kwargs
            )
        else:
            # 默认使用ChatDeepSeek
            return ChatDeepSeek(
                model=model_config.get("model_name", "gpt-3.5-turbo"),
                temperature=model_config.get("temperature", 0.7),
                max_tokens=model_config.get("max_tokens", 2048),
                api_key=model_config.get("api_key"),
                api_base=model_config.get("api_base"),
                **filtered_kwargs
            )


# 便捷函数
async def generate_text(prompt: str, model_name: Optional[str] = None,
                        api_key: Optional[str] = None, api_base: Optional[str] = None, **kwargs) -> str:
    """便捷函数：生成文本"""
    async with LLMManager() as llm:
        return await llm.generate_text(prompt, model_name, api_key, api_base, **kwargs)


async def generate_text_stream(prompt: str, model_name: Optional[str] = None,
                               stream_callback: Optional[Callable] = None,
                               api_key: Optional[str] = None, api_base: Optional[str] = None, **kwargs):
    """便捷函数：流式生成文本"""
    async with LLMManager() as llm:
        async for chunk in llm.generate_text_with_stream(prompt, model_name, stream_callback, api_key, api_base, **kwargs):
            # print(chunk)
            yield chunk
