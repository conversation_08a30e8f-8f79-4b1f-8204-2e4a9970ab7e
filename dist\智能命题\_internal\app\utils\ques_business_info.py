from typing import List, Dict, Any


class QuesBusinessInfo:
    """
    统一存放和处理ques业务信息的基础类
    """

    def __init__(self, ques_type_post: Dict[str, Any]):
        """存放用于命题的主要信息"""
        self.base_name = ques_type_post.get("BaseName", "")
        self.ques_type_name = ques_type_post.get("QuesTypeName", "")
        self.choice_count = ques_type_post.get("ChoiceCount", 0)
        self.ques_count = ques_type_post.get("QuesCount", 1)  # 命制试题的整体数量
        self.qprops = ques_type_post.get("QuesProps", [])  # 命题的试题属性，用于提取命题的考核点
        self.children = ques_type_post.get("Childs", [])  # 子题结构
        self.assessment_points = []
        self.assessment_materials = []
        for prop in self.qprops:
            prop_name = prop.get("QuesPropName", "")
            if "知识点" in prop_name or "考核点" in prop_name:
                self.assessment_points.append(
                    prop.get("SelectQuesPropText", ""))
                if prop.get("SelectQuesPropRemark"):
                    self.assessment_materials.append(
                        prop.get("SelectQuesPropRemark", ""))
        if self.children != []:  # 组合题情况
            self.children_count = len(self.children)
            # 子题类型（健壮性处理）
            self.children_type = self.children[0].get(
                "QuesTypeName", "") if self.children and isinstance(self.children[0], dict) else ""

            # 组合题考核点和命题素材

            # 子题结构
            self.sub_questions = []
            for idx, child in enumerate(self.children, 1):
                sub_info = {
                    "index": idx,
                    "ques_type_name": child.get("QuesTypeName", ""),
                    "ques_props": []
                }
                for prop in child.get("QuesProps", []):
                    sub_info["ques_props"].append({
                        "name": prop.get("QuesPropName", ""),
                        "value": prop.get("SelectQuesPropText", ""),
                        "remark": prop.get("SelectQuesPropRemark", "")
                    })
                self.sub_questions.append(sub_info)

    def assessment_points_str(self):
        return "，".join([p for p in self.assessment_points if p]) or "无"

    def assessment_materials_str(self):
        return "；".join([m for m in self.assessment_materials if m]) or "无"

    def sub_questions_str(self):
        lines = []
        for sub in self.sub_questions:
            props_str = "；".join(
                [f"{p['name']}:{p['value']}" +
                    (f"（{p['remark']}）" if p['remark'] else "") for p in sub["ques_props"]]
            )
            lines.append(
                f"子题{sub['index']}（{sub['ques_type_name']}）：{props_str}")
        return "\n".join(lines) if lines else "无子题"

    def summary(self):
        return {
            "base_name": self.base_name,
            "ques_type_name": self.ques_type_name,
            "choice_count": self.choice_count,
            "ques_count": self.ques_count,
            "assessment_points": self.assessment_points,
            "assessment_materials": self.assessment_materials,
            "sub_questions": self.sub_questions,
            "children_type": self.children_type
        }
