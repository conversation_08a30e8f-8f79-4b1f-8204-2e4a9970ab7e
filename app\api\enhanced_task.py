from fastapi import APIRouter, HTTPException
from app.models.schemas import StandardResponse, ErrorResponse
from app.services.enhanced_task_service import enhanced_task_service
from app.core.state_manager import task_manager, TaskStatus
import uuid
from datetime import datetime
from loguru import logger

router = APIRouter()


@router.get("/GetTaskDetails/{task_id}")
async def get_task_details(task_id: str):
    """
    获取任务详细信息
    
    功能：
    - 获取任务的完整状态信息
    - 包含执行步骤和进度详情
    - 提供性能指标和统计数据
    """
    try:
        result = await enhanced_task_service.get_task_details(task_id)
        
        if not result:
            raise HTTPException(
                status_code=404,
                detail=f"任务不存在: {task_id}"
            )
        
        return StandardResponse(
            code=200,
            msg="获取成功",
            data=result
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务详情失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取任务详情失败: {str(e)}"
        )


@router.get("/ListTasks")
async def list_tasks(
    status: str = None,
    task_type: str = None,
    page: int = 1,
    page_size: int = 20
):
    """
    列出任务列表
    
    功能：
    - 分页列出所有任务
    - 按状态和类型筛选
    - 支持排序和搜索
    """
    try:
        result = await enhanced_task_service.list_tasks(
            status=status,
            task_type=task_type,
            page=page,
            page_size=page_size
        )
        
        return StandardResponse(
            code=200,
            msg="获取成功",
            data=result
        )
        
    except Exception as e:
        logger.error(f"获取任务列表失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取任务列表失败: {str(e)}"
        )


@router.post("/MonitorTasks")
async def monitor_tasks(task_ids: list = None):
    """
    监控任务状态
    
    功能：
    - 实时监控多个任务状态
    - 提供任务执行统计
    - 检测异常和超时任务
    """
    try:
        result = await enhanced_task_service.monitor_tasks(task_ids)
        
        return StandardResponse(
            code=200,
            msg="监控完成",
            data=result
        )
        
    except Exception as e:
        logger.error(f"任务监控失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"任务监控失败: {str(e)}"
        )


@router.post("/RetryTask/{task_id}")
async def retry_task(task_id: str):
    """
    重试失败的任务
    
    功能：
    - 重新执行失败的任务
    - 保留原始参数和配置
    - 记录重试历史
    """
    try:
        result = await enhanced_task_service.retry_task(task_id)
        
        return StandardResponse(
            code=200,
            msg="重试成功",
            data=result
        )
        
    except Exception as e:
        logger.error(f"任务重试失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"任务重试失败: {str(e)}"
        )


@router.post("/CancelTask/{task_id}")
async def cancel_task(task_id: str, reason: str = "用户取消"):
    """
    取消任务
    
    功能：
    - 取消正在执行的任务
    - 记录取消原因
    - 清理相关资源
    """
    try:
        result = await enhanced_task_service.cancel_task(task_id, reason)
        
        return StandardResponse(
            code=200,
            msg="取消成功",
            data=result
        )
        
    except Exception as e:
        logger.error(f"任务取消失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"任务取消失败: {str(e)}"
        )


@router.get("/GetTaskStatistics")
async def get_task_statistics(
    start_date: str = None,
    end_date: str = None
):
    """
    获取任务统计信息
    
    功能：
    - 统计任务执行情况
    - 分析成功率和性能指标
    - 生成统计报告
    """
    try:
        result = await enhanced_task_service.get_task_statistics(
            start_date=start_date,
            end_date=end_date
        )
        
        return StandardResponse(
            code=200,
            msg="统计完成",
            data=result
        )
        
    except Exception as e:
        logger.error(f"获取任务统计失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取任务统计失败: {str(e)}"
        )


@router.post("/ScheduleTask")
async def schedule_task(
    task_config: dict,
    schedule_time: str = None,
    recurring: bool = False
):
    """
    调度任务
    
    功能：
    - 定时执行任务
    - 支持周期性任务
    - 管理任务队列
    """
    try:
        result = await enhanced_task_service.schedule_task(
            task_config=task_config,
            schedule_time=schedule_time,
            recurring=recurring
        )
        
        return StandardResponse(
            code=200,
            msg="调度成功",
            data=result
        )
        
    except Exception as e:
        logger.error(f"任务调度失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"任务调度失败: {str(e)}"
        )


@router.get("/GetTaskLogs/{task_id}")
async def get_task_logs(
    task_id: str,
    log_level: str = "INFO",
    limit: int = 100
):
    """
    获取任务日志
    
    功能：
    - 获取任务执行日志
    - 按级别筛选日志
    - 支持分页查看
    """
    try:
        result = await enhanced_task_service.get_task_logs(
            task_id=task_id,
            log_level=log_level,
            limit=limit
        )
        
        return StandardResponse(
            code=200,
            msg="获取成功",
            data=result
        )
        
    except Exception as e:
        logger.error(f"获取任务日志失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取任务日志失败: {str(e)}"
        )


@router.post("/SetTaskPriority/{task_id}")
async def set_task_priority(task_id: str, priority: int):
    """
    设置任务优先级
    
    功能：
    - 调整任务执行优先级
    - 影响任务队列排序
    - 支持动态调整
    """
    try:
        result = await enhanced_task_service.set_task_priority(
            task_id=task_id,
            priority=priority
        )
        
        return StandardResponse(
            code=200,
            msg="设置成功",
            data=result
        )
        
    except Exception as e:
        logger.error(f"设置任务优先级失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"设置任务优先级失败: {str(e)}"
        )


@router.post("/BatchOperateTasks")
async def batch_operate_tasks(
    task_ids: list,
    operation: str,
    parameters: dict = None
):
    """
    批量操作任务
    
    功能：
    - 批量取消、重试、删除任务
    - 批量修改任务属性
    - 提供操作结果统计
    """
    try:
        result = await enhanced_task_service.batch_operate_tasks(
            task_ids=task_ids,
            operation=operation,
            parameters=parameters or {}
        )
        
        return StandardResponse(
            code=200,
            msg="批量操作完成",
            data=result
        )
        
    except Exception as e:
        logger.error(f"批量操作失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"批量操作失败: {str(e)}"
        )
