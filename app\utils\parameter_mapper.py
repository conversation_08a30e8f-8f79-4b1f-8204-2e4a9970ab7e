"""
参数映射系统
实现试题结构参数与业务参数的映射转换
"""

from typing import Dict, Any, List, Optional
from loguru import logger


class ParameterMapper:
    """参数映射器"""

    # 参数映射表
    PARAMETER_MAPPING = {
        "langMaterial": "【试题材料】",
        "quesSubject": "【试题题干】",
        "choice": "",
        "answer": "\n【答案】",
        "resolev": "\n【解析】",
        "langAndSubject": "【试题描述】",
        "mulChoice": "多选试题选项"
    }

    def __init__(self):
        self.logger = logger

    def map_question_structure_to_prompt(self, ques_type_post: Dict[str, Any], is_clone: bool = False) -> str | tuple[str, str]:
        """
        将试题结构映射为命题提示词

        Args:
            ques_type_post: AiQuesTypePost结构数据
            is_clone: 是否为克隆模式，克隆模式下返回(原题信息, 结构信息)元组

        Returns:
            映射后的命题提示词，克隆模式下返回(原题信息, 结构信息)元组
        """
        try:
            choice_count = ques_type_post.get("ChoiceCount", None)
            if is_clone:
                # 克隆模式：分别生成原题信息和结构信息
                original_content = []
                structure_content = []

                # 处理Elements
                elements = ques_type_post.get("Elements", [])
                for element in elements:
                    element_type = element.get("ElementType", "")
                    element_texts = element.get("ElementText", [])

                    if element_type in self.PARAMETER_MAPPING:
                        mapped_label = self.PARAMETER_MAPPING[element_type]
                        if element_texts:
                            # 原题内容
                            if element_type == "choice":
                                # 特殊处理选项，确保以A、B、C、D开头
                                content = self._format_choices(element_texts)
                                if choice_count:
                                    original_content.append(
                                        f"{mapped_label} {content}")
                                    element_texts = [
                                        "[待生成]" for i in range(choice_count)]
                                    content = self._format_choices(
                                        element_texts)
                                    structure_content.append(f"{content}\n")
                                    continue
                            else:
                                content = "\n".join(element_texts)
                            original_content.append(
                                f"{mapped_label} {content}")
                            # 结构要求（与智能命题一致）
                            structure_content.append(f"{mapped_label}\n[待生成]")
                        else:
                            # 如果没有内容，结构要求
                            structure_content.append(f"{mapped_label}\n[待生成]")

                # 处理子题目
                children = ques_type_post.get("Childs", [])
                for i, child in enumerate(children, 1):
                    child_original, child_structure = self.map_question_structure_to_prompt(
                        child, is_clone=True)
                    original_content.append(f"\n子题{i}:\n{child_original}")
                    structure_content.append(f"\n子题{i}:\n{child_structure}")

                return ("\n".join(original_content), "\n".join(structure_content))
            else:
                # 智能命题模式：只返回结构信息
                mapped_content = []

                # 处理Elements
                elements = ques_type_post.get("Elements", [])

                for element in elements:
                    element_type = element.get("ElementType", "")
                    element_texts = element.get("ElementText", [])

                    if element_type in self.PARAMETER_MAPPING:
                        mapped_label = self.PARAMETER_MAPPING[element_type]
                        # 如果有原试题内容，用于克隆
                        if element_type == "choice":
                            # 特殊处理选项，确保以A、B、C、D开头
                            if choice_count:
                                element_texts = [
                                    "[待生成]" for i in range(choice_count)]
                            content = self._format_choices(element_texts)
                            mapped_content.append(f"{content}\n")
                        else:
                            # 如果没有内容，用于智能命题
                            mapped_content.append(f"{mapped_label}\n[待生成]")

                # 处理QuesStr（如果有的话）
                ques_str = ques_type_post.get("QuesStr", "")
                if ques_str:
                    mapped_content.append(f"[试题题干]\n{ques_str}")

                # 处理子题目
                children = ques_type_post.get("Childs", [])
                for i, child in enumerate(children, 1):
                    child_mapped = self.map_question_structure_to_prompt(
                        child, is_clone=False)
                    mapped_content.append(f"\n子题{i}:\n{child_mapped}")

                return "\n".join(mapped_content)

        except Exception as e:
            self.logger.error(f"参数映射失败: {e}")
            raise ValueError(f"参数映射错误: {str(e)}")

    def _format_choices(self, choices: List[str]) -> str:
        """
        格式化选项，确保以A、B、C、D等字母开头

        Args:
            choices: 选项列表

        Returns:
            格式化后的选项字符串
        """
        try:
            formatted_choices = []
            choice_letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J']

            for i, choice in enumerate(choices):
                if i >= len(choice_letters):
                    # 如果选项超过10个，使用数字
                    prefix = f"{i+1}."
                else:
                    prefix = f"{choice_letters[i]}."

                # 清理选项内容，移除可能已有的前缀
                cleaned_choice = choice.strip()

                # 检查是否已经有字母前缀
                if cleaned_choice and cleaned_choice[0].upper() in choice_letters and cleaned_choice[1] in ['.', '、', '：', ':']:
                    # 如果已经有正确的前缀，直接使用
                    formatted_choices.append(cleaned_choice)
                else:
                    # 添加前缀
                    formatted_choices.append(f"{prefix} {cleaned_choice}")

            return "\n".join(formatted_choices)

        except Exception as e:
            self.logger.error(f"格式化选项失败: {e}")
            # 如果格式化失败，返回原始内容
            return "\n".join(choices)

    def _build_generation_prompt(self,
                                 mapped_structure: str,
                                 base_name: str,
                                 ques_type_name: str,
                                 exam_subject: str,
                                 question_count: int,
                                 properties: Dict[str, Any]) -> str:
        """构建智能命题提示词"""

        # 构建属性描述
        property_descriptions = []
        for prop_name, prop_value in properties.items():
            property_descriptions.append(f"- {prop_name}: {prop_value}")

        properties_text = "\n".join(
            property_descriptions) if property_descriptions else "无特殊要求"

        prompt = f"""
你是一个专业的{exam_subject}试题命题专家。请根据以下要求生成{question_count}道高质量的试题。

题目类型：{base_name} ({ques_type_name})
考试科目：{exam_subject}
题目数量：{question_count}道

试题属性要求：
{properties_text}

试题结构要求：
{mapped_structure}

命题要求：
1. 严格按照给定的试题结构生成试题
2. 确保试题质量高，符合{exam_subject}学科特点
3. 试题内容要准确、完整、规范
4. 如果是组合题，要确保材料与子题目逻辑一致
5. 生成的试题要符合给定的属性要求

请生成{question_count}道完整的试题，每道试题之间用"{'-'*10}试题分割线{'-'*10}\n"分隔。
"""
        return prompt.strip()

    def _build_clone_prompt(self,
                            mapped_structure: str,
                            base_name: str,
                            ques_type_name: str,
                            exam_subject: str,
                            question_count: int,
                            properties: Dict[str, Any]) -> str:
        """构建试题克隆提示词"""

        # 构建属性描述
        property_descriptions = []
        for prop_name, prop_value in properties.items():
            property_descriptions.append(f"- {prop_name}: {prop_value}")

        properties_text = "\n".join(
            property_descriptions) if property_descriptions else "无特殊要求"

        prompt = f"""
你是一个专业的{exam_subject}试题克隆专家。请基于以下原题生成{question_count}道新的试题。

原题信息：
题目类型：{base_name} ({ques_type_name})
考试科目：{exam_subject}
题目数量：{question_count}道

原题结构：
{mapped_structure}

克隆要求：
{properties_text}

克隆原则：
1. 保持相同的题型结构和难度
2. 使用不同的具体内容（如不同的材料、不同的题目描述）
3. 保持相同的知识点要求
4. 确保试题质量不低于原题
5. 生成的内容要符合{exam_subject}学科的特点

请生成{question_count}道新的试题，每道试题之间用"{'-'*10}试题分割线{'-'*10}\n"分隔。
"""
        return prompt.strip()

    def parse_generated_questions(self, generated_content: str) -> List[str]:
        """
        解析生成的试题内容

        Args:
            generated_content: 生成的试题内容

        Returns:
            解析后的试题列表
        """
        try:
            # 使用分割线分割试题
            separator = f"{'-'*10}试题分割线{'-'*10}"
            questions = generated_content.split(separator)

            # 过滤空内容并清理
            cleaned_questions = []
            for question in questions:
                question = question.strip()
                if question and not question.isspace():
                    cleaned_questions.append(question)

            return cleaned_questions

        except Exception as e:
            self.logger.error(f"解析生成试题失败: {e}")
            return [generated_content.strip()]


# 全局实例
parameter_mapper = ParameterMapper()
