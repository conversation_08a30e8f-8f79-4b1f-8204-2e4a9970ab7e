"""
数据模型模块
导出所有Pydantic模型供其他模块使用
"""

# 原有模型
from .schemas import (
    # 枚举类型
    TaskName,
    ElementType,
    ResponseState,
    
    # 原有数据模型
    Element,
    QuesPropSource,
    QuesProp,
    QuesTypePost,
    QuesPost,
    ModelArgs,
    NodeModel,
    NodePrompt,
    NodeContent,
    WorkflowNode,
    WorkflowPost,
    AgentPost,
    AgentGenRequest,
    StreamRequest,
    StreamResponse,
    AgentGenResponse,
    TaskStateRequest,
    TaskStateResponse,
    
    # 增强的枚举类型
    WorkflowType,
    TaskStatus,
    WorkflowStep,
    FileType,
    DifficultyLevel,
    StorageStrategy,
    
    # 主题命题相关模型
    TopicMaterialRequest,
    TopicMaterialResponse,
    
    # 材料命题相关模型
    MaterialProcessRequest,
    MaterialProcessResponse,
    
    # 任务规划相关模型
    StructuralElement,
    QuestionTaskPlan,
    TaskPlanRequest,
    TaskPlanResponse,
    
    # 试题执行相关模型
    QuestionExecutionRequest,
    GeneratedQuestion,
    QuestionExecutionResponse,
    
    # 试题优化相关模型
    OptimizationRequest,
    OptimizationSuggestion,
    OptimizationResponse,
    
    # 知识库管理相关模型
    KnowledgeUploadRequest,
    KnowledgeDocument,
    KnowledgeSegment,
    KnowledgeSearchRequest,
    KnowledgeSearchResult,
    KnowledgeSearchResponse,
    KnowledgeUploadResponse,
    
    # 增强的任务管理模型
    EnhancedTaskInfo,
    EnhancedTaskStateRequest,
    EnhancedTaskStateResponse,
    
    # 文件处理相关模型
    FileValidationResult,
    FileProcessingResult,
    ContentSegmentationRequest,
    ContentSegmentationResponse,
    
    # 通用响应模型
    StandardResponse,
    ErrorResponse,
    
    # 批量操作模型
    BatchOperationRequest,
    BatchOperationResult,
)

__all__ = [
    # 原有枚举类型
    "TaskName",
    "ElementType", 
    "ResponseState",
    
    # 原有数据模型
    "Element",
    "QuesPropSource",
    "QuesProp",
    "QuesTypePost",
    "QuesPost",
    "ModelArgs",
    "NodeModel",
    "NodePrompt",
    "NodeContent",
    "WorkflowNode",
    "WorkflowPost",
    "AgentPost",
    "AgentGenRequest",
    "StreamRequest",
    "StreamResponse",
    "AgentGenResponse",
    "TaskStateRequest",
    "TaskStateResponse",
    
    # 增强的枚举类型
    "WorkflowType",
    "TaskStatus",
    "WorkflowStep",
    "FileType",
    "DifficultyLevel",
    "StorageStrategy",
    
    # 主题命题相关模型
    "TopicMaterialRequest",
    "TopicMaterialResponse",
    
    # 材料命题相关模型
    "MaterialProcessRequest",
    "MaterialProcessResponse",
    
    # 任务规划相关模型
    "StructuralElement",
    "QuestionTaskPlan",
    "TaskPlanRequest",
    "TaskPlanResponse",
    
    # 试题执行相关模型
    "QuestionExecutionRequest",
    "GeneratedQuestion",
    "QuestionExecutionResponse",
    
    # 试题优化相关模型
    "OptimizationRequest",
    "OptimizationSuggestion",
    "OptimizationResponse",
    
    # 知识库管理相关模型
    "KnowledgeUploadRequest",
    "KnowledgeDocument",
    "KnowledgeSegment",
    "KnowledgeSearchRequest",
    "KnowledgeSearchResult",
    "KnowledgeSearchResponse",
    "KnowledgeUploadResponse",
    
    # 增强的任务管理模型
    "EnhancedTaskInfo",
    "EnhancedTaskStateRequest",
    "EnhancedTaskStateResponse",
    
    # 文件处理相关模型
    "FileValidationResult",
    "FileProcessingResult",
    "ContentSegmentationRequest",
    "ContentSegmentationResponse",
    
    # 通用响应模型
    "StandardResponse",
    "ErrorResponse",
    
    # 批量操作模型
    "BatchOperationRequest",
    "BatchOperationResult",
]