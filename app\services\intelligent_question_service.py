"""
智能命题服务
实现基于LLM的智能试题生成功能
"""

import asyncio
import re
import json
from typing import Dict, Any, List, Optional, Callable
from loguru import logger
from app.utils.parameter_mapper import parameter_mapper
from app.utils.question_parser import parse_question_structure
from app.utils.ques_props_extractor import ques_props_extractor
from app.services.llm_manager import generate_text_stream
from app.core.state_manager import create_stream_data, TaskStatus
import asyncio
import time
from app.utils.ques_business_info import QuesBusinessInfo


class IntelligentQuestionService:
    """智能命题服务"""

    def __init__(self):
        self.logger = logger
        self._model_config_cache = {}  # 模型配置缓存

    async def generate_questions(self,
                                 ques_post: Dict[str, Any],
                                 workflow_config: Optional[Dict[str, Any]] = None,
                                 stream_callback: Optional[Callable] = None,
                                 status_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        生成智能试题
        Returns:
            生成的试题结果
        """
        try:
            self.logger.info("开始智能命题")  # 保留关键业务日志

            # 更新状态：开始命题
            if status_callback:
                await status_callback(TaskStatus.PROCESSING, "开始智能命题", {"step": "start", "progress": 0})

            # 解析试题结构
            ques_type_post = ques_post.get("AiQuesTypePost", {})
            exam_subject = ques_post.get("ExamSubjectName", "语文")
            exam_project = ques_post.get("ExamProjectName", "")
            question_count = ques_type_post.get("QuesCount", 1)

            # 更新状态：分析题目要求
            if status_callback:
                await status_callback(TaskStatus.PROCESSING, "分析题目要求", {"step": "analyzing", "progress": 5})

            # 判断是否为组合题
            is_composite = self._is_composite_question(ques_type_post)

            result_list = []
            if is_composite and question_count > 1:
                # 组合题且数量大于1，异步并发生成多道组合题
                async def generate_single_composite(i):
                    try:
                        # 可根据需要为每道题设置不同参数（如有）
                        result = await self._generate_composite_question(
                            ques_type_post, exam_subject, workflow_config, stream_callback, status_callback
                        )
                        return result
                    except Exception as e:
                        self.logger.error(f"组合题{i+1}生成失败: {e}")
                        # 返回错误信息，不中断整个流程
                        return {"error": f"组合题{i+1}生成失败: {str(e)}"}

                # 创建异步任务列表
                tasks = [generate_single_composite(
                    i) for i in range(question_count)]
                result_list = await asyncio.gather(*tasks)
            else:
                if is_composite:
                    result = await self._generate_composite_question(
                        ques_type_post, exam_subject, workflow_config, stream_callback, status_callback
                    )
                else:
                    result = await self._generate_basic_question(
                        ques_type_post, exam_subject, question_count, workflow_config, stream_callback, status_callback
                    )
                result_list.append(result)

            # 更新状态：命题完成
            if status_callback:
                await status_callback(TaskStatus.COMPLETED, "智能命题完成", {"step": "completed", "progress": 100})

            self.logger.info("智能命题完成")  # 保留关键业务日志
            # 返回QuesPostData结构，AiQuesTypePost始终为数组
            return {
                "AiQuesTypePost": result_list,
                "ExamSubjectName": exam_subject,
                "ExamProjectName": exam_project
            }

        except Exception as e:
            self.logger.error(f"智能命题失败: {e}")

            # 更新状态：命题失败
            if status_callback:
                await status_callback(TaskStatus.FAILED, f"智能命题失败: {str(e)}", {"step": "failed", "progress": 0})

            raise

    def _get_cached_model_config(self, workflow_config: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """
        获取缓存的模型配置，避免重复提取
        """
        if not workflow_config:
            return None

        # 使用workflow_config的hash作为缓存key
        config_key = str(hash(str(workflow_config)))

        if config_key not in self._model_config_cache:
            from app.utils.workflow_utils import extract_model_config_from_workflow
            self._model_config_cache[config_key] = extract_model_config_from_workflow(
                workflow_config)

        return self._model_config_cache[config_key]

    async def _generate_basic_question(self,
                                       ques_type_post: Dict[str, Any],
                                       exam_subject: str,
                                       question_count: int,
                                       workflow_config: Optional[Dict[str, Any]] = None,
                                       stream_callback: Optional[Callable] = None,
                                       status_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        生成基础题型试题
        """
        try:
            # 统一业务信息提取
            info = QuesBusinessInfo(ques_type_post)
            # 拼接原始素材
            knowledge_points = []
            for point, remark in zip(info.assessment_points, info.assessment_materials):
                knowledge_remark = f"{point}\n{remark}"
                knowledge_points.append(knowledge_remark)
            material_context = "\n\n".join(knowledge_points)
            material_context = f'<命题素材>{material_context}</命题素材>'
            # 自动压缩
            material_context = await self.compress_material_if_needed(material_context, workflow_config, max_length=7000)
            question_type_context = f"{info.ques_count}道{info.ques_type_name}"
            # 第一步：生成命题规划（使用推理模型，流式返回）
            if status_callback:
                await status_callback(TaskStatus.PROCESSING, "生成命题规划", {"step": "命题规划中", "progress": 10})

            # 构建命题规划提示词，集成业务信息
            planning_prompt = f"""
## 角色与任务

你是一个专业的**{exam_subject}命题专家**。请结合命题素材生成详细的命题规划。

## 命题素材

```
{material_context}
```

## 命题要求

**题型信息**：{question_type_context}
**考试科目**：{exam_subject}

## 具体工作要求

1. **策略制定**：制订好关于每一道试题的命制策略
2. **结构化输出**：每一道试题应当是一个单独的JSON对象，包含试题的类型、难度、知识点、命题要求等

## 试题命制策略

1. **素材关联**：试题的命制要与命题素材紧密相关，并符合命题要求
2. **学科特色**：试题的命制要符合{exam_subject}学科特点

## 输出要求

1. **格式严格**：严格按照JSON格式输出
2. **策略合理**：确保试题的命制策略合理可行
3. **学科特色**：所有策略要符合{exam_subject}学科特点

### JSON输出格式

```json
{{
    "question_tasks": [
        {{
            "subquestion_index": 1,
            "type": "试题类型",
            "difficulty": "难度要求",
            "knowledge_points": "知识点要求",
            "strategy": "命制策略",
            "requirements": "具体命制要求"
        }}
    ]
}}
```

---

**输出：**
"""
            # 使用带重试机制的命题规划生成
            planning_tasks = await self._generate_planning_with_retry(
                planning_prompt, workflow_config, stream_callback, status_callback, max_retries=3, is_composite=False
            )

            # 补充：命题规划完成后，更新任务状态
            if status_callback:
                await status_callback(TaskStatus.PROCESSING, "命题规划完成", {"step": "planning_completed", "progress": 30})
            # 移除不必要的等待时间
            # await asyncio.sleep(0.1)
            # 第二步：异步生成试题（使用对话模型，非流式）
            if status_callback:
                await status_callback(TaskStatus.PROCESSING, "生成试题", {"step": "generation", "progress": 35})

            # 异步生成试题
            generated_questions = await self._generate_questions_async(
                ques_type_post, exam_subject, planning_tasks, workflow_config, None, status_callback
            )

            # 第三步：组装完整试题
            if status_callback:
                await status_callback(TaskStatus.PROCESSING, "组装试题", {"step": "assembling", "progress": 90})

            # 组装完整试题
            final_result = self._assemble_basic_question_with_separator(
                ques_type_post, generated_questions
            )

            # 更新状态：试题生成完成
            if status_callback:
                await status_callback(TaskStatus.PROCESSING, "试题生成完成", {"step": "completed", "progress": 100})

            return final_result

        except Exception as e:
            self.logger.error(f"基础题型生成失败: {e}")

            # 更新状态：试题生成失败
            if status_callback:
                await status_callback(TaskStatus.FAILED, f"基础题型生成失败: {str(e)}", {"step": "failed", "progress": 0})

                raise

            raise

    async def _generate_composite_question(self,
                                           ques_type_post: Dict[str, Any],
                                           exam_subject: str,
                                           workflow_config: Optional[Dict[str, Any]] = None,
                                           stream_callback: Optional[Callable] = None,
                                           status_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        生成组合题试题
        - 复杂题型：（组合题）
        - 命题整体工作流逻辑：
        - 1. 生成命题规划：获取组合题的所有子题的试题属性、试题结构、知识点、难度、知识点补充信息（SelectQuesPropRemark字段中）等prompt的上下文信息，
        与LLM模型生成一个命题规划，该规划包含如何命制材料、如何命制子试题，应当输出一个列表，列表中存放着这些任务，其中子题的命制任务可以使用异步请求模型来完成，ps： 该请求使用推理模型流式返回数据，需要将思考过程中的streamdata推送到/QueryStream接口；
        - 2. 生成试题材料：使用上一步骤中获取的试题命制规划列表中用于材料命制的任务，生成一段试题材料，ps：该请求使用对话模型非流式返回数据，不需要将思考过程中的streamdata推送到/QueryStream接口；
        - 3. 子题生成：将已经命好的试题材料添加到子任务的prompt的上下问信息中，同时获取子列表中的（题型、难度、知识点、命题数量）补充到prompt的上下文信息中，进行子题的命制，使用异步来请求模型，提高命题效率，ps：该请求使用对话模型非流式返回数据，不需要将思考过程中的streamdata推送到/QueryStream接口；
        - 4. 将所有试（包含最先生成的试题材料）按照  f"{'-'*10}试题分割线{'-'*10}" 格式拼接，将其放在AiQuesTypePost放在最外层的QuesStr字段中，按照AiQuesTypePost 的 value 完成结构返回。
        """
        try:
            # 更新状态：开始分析组合题
            if status_callback:
                await status_callback(TaskStatus.PROCESSING, "分析组合题结构", {"step": "analyzing", "progress": 5})

            # 第一步：生成命题规划（使用推理模型，流式返回）
            if status_callback:
                await status_callback(TaskStatus.PROCESSING, "生成命题规划", {"step": "planning", "progress": 10})

            # 构建命题规划提示词
            planning_prompt = await self._build_composite_planning_prompt_async(ques_type_post, exam_subject, workflow_config)

            # 使用带重试机制的命题规划生成
            planning_tasks = await self._generate_planning_with_retry(
                planning_prompt, workflow_config, stream_callback, status_callback, max_retries=3, is_composite=True
            )

            # 补充：命题规划完成后，更新任务状态
            if status_callback:
                await status_callback(TaskStatus.PROCESSING, "命题规划完成", {"step": "planning_completed", "progress": 30})

            # 第二步：生成试题材料（使用对话模型，非流式）
            if status_callback:
                await status_callback(TaskStatus.PROCESSING, "生成试题材料", {"step": "material_generation", "progress": 35})

            # 获取材料生成任务
            # material_task = planning_tasks.get("material_task", {})
            material_prompt = self._build_material_generation_prompt_with_plan(
                ques_type_post, exam_subject, planning_tasks
            )

            # 使用对话模型生成材料（非流式）
            generated_material = await self._call_llm_non_stream(
                material_prompt, workflow_config, "material_generation"
            )

            # 补充：材料生成完成后，更新任务状态
            if status_callback:
                await status_callback(TaskStatus.PROCESSING, "组合题材料生成完成", {"step": "克隆材料", "progress": 50, "detail": "组合题材料生成完成"})

            # 第三步：异步生成子题（使用对话模型，非流式）
            if status_callback:
                await status_callback(TaskStatus.PROCESSING, "生成子题目", {"step": "subquestion_generation", "progress": 60})

            # 获取子题生成任务
            subquestion_tasks = planning_tasks.get("subquestion_tasks", [])

            # 异步生成子题
            generated_subquestions = await self._generate_subquestions_async(
                ques_type_post, exam_subject, generated_material, subquestion_tasks,
                workflow_config, None, status_callback
            )

            # 补充：子题生成完成后，更新任务状态
            if status_callback:
                await status_callback(TaskStatus.PROCESSING, "组合题子题目生成完成", {"step": "克隆子题目", "progress": 80, "detail": "组合题子题目生成完成"})

            # 第四步：组装完整试题
            if status_callback:
                await status_callback(TaskStatus.PROCESSING, "组装组合题", {"step": "assembling", "progress": 90})

            # 组装完整试题
            final_result = self._assemble_composite_question_with_separator(
                ques_type_post, generated_material, generated_subquestions
            )

            # 补充：组装完成后，更新任务状态
            if status_callback:
                await status_callback(TaskStatus.PROCESSING, "组合题组装完成", {"step": "组装组合题", "progress": 90, "detail": "组合题组装完成"})

            # 更新状态：组合题完成
            if status_callback:
                await status_callback(TaskStatus.PROCESSING, "组合题生成完成", {"step": "completed", "progress": 100})

            return final_result

        except Exception as e:
            self.logger.error(f"组合题生成失败: {e}")

            # 更新状态：组合题生成失败
            if status_callback:
                await status_callback(TaskStatus.FAILED, f"组合题生成失败: {str(e)}", {"step": "failed", "progress": 0})

            raise

    def _is_composite_question(self, ques_type_post: Dict[str, Any]) -> bool:
        """判断是否为组合题"""
        # 检查是否有子题目
        if "Childs" in ques_type_post and ques_type_post["Childs"]:
            return True

        # 检查Elements中是否有材料，严格使用参数映射表
        if "Elements" in ques_type_post:
            for element in ques_type_post["Elements"]:
                element_type = element.get("ElementType", "")
                if element_type in parameter_mapper.PARAMETER_MAPPING:
                    mapped_label = parameter_mapper.PARAMETER_MAPPING[element_type]
                    if mapped_label == "【试题材料】":  # 使用映射表中的标准标签
                        return True

        return False

    def _build_subquestion_generation_prompt(self,
                                             ques_type_post: Dict[str, Any],
                                             exam_subject: str) -> str:
        """构建子题目生成提示词"""

        base_name = ques_type_post.get("BaseName", "")
        children = ques_type_post.get("Childs", [])
        ques_count = ques_type_post.get("QuesCount", 1)
        prompt = f"""
## 角色与任务

你是一个专业的**{exam_subject}子题目生成专家**。请基于给定的材料生成子题目。

## 题目信息

- **题目类型**：{base_name}
- **考试科目**：{exam_subject}
- **子题目数量**：{ques_count}道

## 生成要求

1. **材料关联**：子题目与材料内容紧密相关
2. **逻辑清晰**：各子题目之间逻辑清晰
3. **属性符合**：符合给定的题型和属性要求
4. **难度递进**：难度递进合理

---

**请生成子题目内容：**
"""
        return prompt.strip()

    def _update_question_with_material(self,
                                       ques_type_post: Dict[str, Any],
                                       generated_material: str) -> Dict[str, Any]:
        """更新试题结构，添加生成的材料"""

        updated_question = ques_type_post.copy()

        # 更新Elements中的材料
        if "Elements" in updated_question:
            for element in updated_question["Elements"]:
                if element.get("ElementType") == "langMaterial":
                    element["ElementText"] = [generated_material]

        return updated_question

    def _assemble_composite_question(self,
                                     ques_type_post: Dict[str, Any],
                                     material: str,
                                     subquestions: str) -> Dict[str, Any]:
        """组装组合题"""

        # 组装完整试题内容
        assembled_content = f"{material}\n\n{subquestions}"

        result = ques_type_post.copy()
        result["QuesStr"] = assembled_content

        return result

    async def _call_llm_with_stream(self,
                                    prompt: str,
                                    workflow_config: Optional[Dict[str, Any]] = None,
                                    stream_callback: Optional[Callable] = None,
                                    step_name: str = "thinking",
                                    status_callback: Optional[Callable] = None,
                                    start_progress: int = 0,
                                    end_progress: int = 100) -> str:
        """
        调用LLM进行流式生成（仅用于规划阶段）- 优化版本
        """
        try:
            # 提取模型配置（缓存优化）
            workflow_model_config = self._get_cached_model_config(
                workflow_config)

            # 定义流式回调（减少等待时间）
            async def thinking_callback(thinking_data: Dict[str, Any]):
                if stream_callback:
                    # 计算当前进度
                    current_progress = start_progress + int(
                        (thinking_data.get('progress', 0) / 100) *
                        (end_progress - start_progress)
                    )

                    # 处理所有思考过程数据，包括reasoning和thinking类型
                    thinking_type = thinking_data.get('type', '')
                    step_type = thinking_data.get('step', '')

                    # 检查是否为思考过程数据
                    is_thinking = (thinking_type in ['thinking', 'reasoning'] or
                                   'thinking' in thinking_type or
                                   step_type in ['reasoning', 'thinking', 'thinking_complete'])

                    if is_thinking:
                        await stream_callback(create_stream_data(
                            stream_type="THINKING",
                            message=thinking_data.get('content', ''),
                            data={
                                "step": step_name,
                                "thinking": thinking_data.get('content', ''),
                                "progress": current_progress,
                                "detail": thinking_data.get('content', ''),
                                "thinking_type": step_type
                            }
                        ))

                        # 定期更新状态（减少频率）
                        if thinking_data.get('progress', 0) % 25 == 0:  # 每25%更新一次状态
                            if status_callback:
                                await status_callback(
                                    TaskStatus.PROCESSING,
                                    f"{step_name}进行中",
                                    {"step": step_name, "progress": current_progress}
                                )

                        # 减少等待时间，提高响应速度
                        await asyncio.sleep(0.01)

            # 减少初始等待时间
            self.logger.debug(f"开始{step_name}步骤")  # 改为DEBUG级别
            # 移除不必要的等待时间

            # 调用LLM
            result = ""
            if workflow_model_config:
                model_name = workflow_model_config.get("name", "deepseek-r1")
                api_key = workflow_model_config.get("api_key", "")
                api_base = workflow_model_config.get("api_base", "")
                # 只传递非冲突的参数
                additional_params = {
                    "top_p": workflow_model_config.get("top_p", 0.5),
                    "top_k": workflow_model_config.get("top_k", 5)
                }

                # 为Qwen3模型在命题规划步骤启用推理功能
                if "qwen3" in model_name.lower():
                    additional_params["enable_thinking"] = True
                    self.logger.debug(
                        f"为Qwen3模型启用推理功能: {model_name}")  # 改为DEBUG级别

                self.logger.debug(f"开始调用LLM模型: {model_name}")  # 改为DEBUG级别
                async for chunk in generate_text_stream(prompt, model_name=model_name, stream_callback=thinking_callback, api_key=api_key, api_base=api_base, **additional_params):
                    result += chunk
            else:
                self.logger.debug("使用默认模型调用LLM")  # 改为DEBUG级别
                async for chunk in generate_text_stream(prompt, stream_callback=thinking_callback):
                    result += chunk

            # 规划完成后，快速发送完成标记
            if stream_callback:
                # 减少等待时间
                self.logger.debug(f"{step_name}步骤完成")  # 改为DEBUG级别
                await asyncio.sleep(0.05)  # 减少等待时间
                # 发送完成标记
                await stream_callback(False)
                if status_callback:
                    await status_callback(
                        TaskStatus.PROCESSING,
                        f"{step_name}流式推理已完成",
                        {"step": f"{step_name}_stream_completed",
                            "progress": end_progress}
                    )
                # 减少等待时间
                await asyncio.sleep(0.02)
                self.logger.debug(f"{step_name}步骤完成标记已发送")  # 改为DEBUG级别

            return result

        except Exception as e:
            self.logger.error(f"LLM流式调用失败: {e}")
            # 即使出错也要发送完成标记
            if stream_callback:
                try:
                    await asyncio.sleep(0.02)  # 减少等待时间
                    await stream_callback(False)
                    self.logger.debug("错误情况下完成标记已发送")  # 改为DEBUG级别
                except Exception as callback_error:
                    self.logger.error(f"发送完成标记失败: {callback_error}")
            raise

    async def compress_material_if_needed(self, material: str, workflow_config, max_length=2000) -> str:
        """
        如果素材超长则用模型压缩，否则原样返回
        """
        if len(material) <= max_length:
            return material
        else:
            return material[:6000]

#         prompt = f"""任务：请你将以下内容进行总结，总结后的内容不能丢失任何信息，压缩后的内容不能超过{max_length}字：
# {material}
# """
#         return await self._call_llm_non_stream(prompt, workflow_config, "material_generation")

    async def _build_composite_planning_prompt_async(self, ques_type_post: Dict[str, Any], exam_subject: str, workflow_config=None) -> str:
        """
        构建组合题命题规划提示词，集成结构化属性上下文，支持素材自动压缩
        """
        info = QuesBusinessInfo(ques_type_post)
        # 拼接原始素材
        knowledge_points = []
        for item in info.sub_questions:
            knowledge_remark = [i["value"] + i["remark"]
                                for i in item["ques_props"] if "考核" in i["name"] or "知识" in i["name"]]
            knowledge_points.append(" ".join(knowledge_remark))
        material_context = "\n\n".join(knowledge_points)
        material_context = f'<命题素材>{material_context}</命题素材>'
        # 自动压缩
        material_context = await self.compress_material_if_needed(material_context, workflow_config, max_length=7000)

        question_type_context = f"{info.ques_count}道{info.ques_type_name}，其中包括{info.children_count}道子题，子题题型为：{info.children_type}"

        prompt = f"""
## 角色与任务

你是一个专业的**{exam_subject}组合题命题专家**。请结合组合题命题素材，为该组合题制定详细的命题规划。

## 组合题命题素材

```
{material_context}
```

## 组合题信息

**题型信息**：{question_type_context}
**考试科目**：{exam_subject}

## 具体工作要求

请制定详细的组合题命题规划，包括：

### 1. 材料生成策略

- **通用性**：如何生成适合所有子题的通用材料
- **结构要求**：材料的内容结构和长度要求
- **知识覆盖**：材料的难度和知识点覆盖

### 2. 子题命制策略

- **结构化对象**：每一个子题的命制策略应当是一个单独的对象，包含试题的类型、难度、知识点、命题要求等
- **逻辑关系**：各子题之间的逻辑关系设计
- **难度递进**：子题难度递进安排
- **知识分布**：知识点分布和覆盖策略

### 3. 具体的任务列表和完成标准

- **材料任务**：材料生成任务的具体要求
- **子题标准**：每个子题的命制要求和标准
- **质量保证**：整体质量保证措施

## 输出要求

1. **格式严格**：严格按照JSON格式输出
2. **策略合理**：确保材料任务和子题任务的策略合理可行
3. **质量有效**：质量保证措施要切实有效
4. **学科特色**：所有策略要符合{exam_subject}学科特点

请以JSON格式输出规划结果：
{{
    "material_task": ""//材料命制规划,
    "subquestion_tasks": [
        {{
            "subquestion_index": 1,
            "type": "子题类型",
            "difficulty": "难度要求",
            "knowledge_points": "知识点要求",
            "strategy": "命制策略",
            "requirements": "具体命制要求"
        }}
    ]
}}

# 输出要求
1. 严格按照JSON格式输出；
2. 确保材料任务和子题任务的策略合理可行；
3. 质量保证措施要切实有效；
4. 所有策略要符合{exam_subject}学科特点。

输出：
"""
        return prompt.strip()

    def _parse_planning_result(self, planning_result: str) -> Dict[str, Any]:
        """解析命题规划结果"""
        try:
            import json
            # 尝试提取JSON部分
            start_idx = planning_result.find('{')
            end_idx = planning_result.rfind('}') + 1
            if start_idx != -1 and end_idx != 0:
                json_str = planning_result[start_idx:end_idx]
                parsed_result = json.loads(json_str)

                # 检查是否为组合题规划结果
                if "material_task" in parsed_result and "subquestion_tasks" in parsed_result:
                    # 组合题规划结果
                    return parsed_result
                elif "question_tasks" in parsed_result:
                    # 基础题规划结果，转换为组合题格式以便统一处理
                    return {
                        "material_task": {"strategy": "基础题无需材料", "requirements": "直接生成试题"},
                        "subquestion_tasks": parsed_result.get("question_tasks", []),
                        "planning_type": "basic"
                    }
                else:
                    # 未知格式，返回默认结构
                    return {
                        "material_task": {"strategy": "生成通用材料", "requirements": "适合所有子题"},
                        "subquestion_tasks": [],
                        "planning_type": "unknown"
                    }
            else:
                # 如果无法解析JSON，返回默认结构
                return {
                    "material_task": {"strategy": "生成通用材料", "requirements": "适合所有子题"},
                    "subquestion_tasks": [],
                    "planning_type": "default"
                }
        except Exception as e:
            self.logger.warning(f"解析命题规划失败: {e}")
            return {
                "material_task": {"strategy": "生成通用材料", "requirements": "适合所有子题"},
                "subquestion_tasks": [],
                "planning_type": "error"
            }

    def _build_material_generation_prompt_with_plan(self, ques_type_post: Dict[str, Any],
                                                    exam_subject: str, planning_tasks: Dict[str, Any]) -> str:
        """基于规划构建材料生成提示词"""
        base_name = ques_type_post.get("BaseName", "")
        material_task = planning_tasks.get("material_task", {})

        prompt = f"""
## 角色与任务

你是一个专业的**{exam_subject}试题材料生成专家**。请基于以下规划生成高质量的试题材料。

## 题目信息

- **题目类型**：{base_name}
- **考试科目**：{exam_subject}

## 材料生成策略

```
{material_task}
```

## 具体工作要求

请生成符合要求的试题材料，材料应该：

1. **策略符合**：符合生成策略的要求
2. **内容丰富**：内容丰富，适合作为组合题的基础
3. **学科特色**：符合{exam_subject}学科特点
4. **支撑性强**：能够支撑多个子题目的生成

## 输出要求
1. 不要输出任何与材料无关的内容，注意只需要输出材料，不要输出试题材料等文字内容；
2. 确保材料符合材料生成策略的要求；
3. 所有策略要符合{exam_subject}学科特点；
4. 禁止使用markdown格式输出。

输出：
"""
        return prompt.strip()

    async def _call_llm_non_stream(self, prompt: str, workflow_config: Optional[Dict[str, Any]] = None,
                                   step_name: str = "non_stream") -> str:
        """调用LLM进行非流式生成（对话模型）- 优化版本"""
        try:
            # 使用缓存的模型配置
            workflow_model_config = self._get_cached_model_config(
                workflow_config)

            # 调用LLM（非流式）
            from app.services.llm_manager import generate_text
            if workflow_model_config:
                model_name = workflow_model_config.get("name", "deepseek-r1")
                api_key = workflow_model_config.get("api_key", "")
                api_base = workflow_model_config.get("api_base", "")
                additional_params = {
                    "top_p": workflow_model_config.get("top_p", 0.5),
                    "top_k": workflow_model_config.get("top_k", 5)
                }
                result = await generate_text(prompt, model_name=model_name, api_key=api_key,
                                             api_base=api_base, **additional_params)
            else:
                result = await generate_text(prompt)

            return result

        except Exception as e:
            self.logger.error(f"LLM非流式调用失败: {e}")
            raise

    def _call_llm_non_stream_sync(self, prompt: str, workflow_config: Optional[Dict[str, Any]] = None,
                                  step_name: str = "non_stream") -> str:
        """调用LLM进行非流式生成（同步版本，用于ThreadPoolExecutor）- 优化版本"""
        try:
            # 使用缓存的模型配置
            workflow_model_config = self._get_cached_model_config(
                workflow_config)

            # 调用LLM（非流式，同步版本）
            from app.services.llm_manager import LLMManager
            import asyncio

            # 创建新的事件循环用于同步调用
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                # 如果没有事件循环，创建一个新的
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            async def async_call():
                async with LLMManager() as llm_manager:
                    if workflow_model_config:
                        model_name = workflow_model_config.get(
                            "name", "deepseek-r1")
                        api_key = workflow_model_config.get("api_key", "")
                        api_base = workflow_model_config.get("api_base", "")
                        additional_params = {
                            "top_p": workflow_model_config.get("top_p", 0.5),
                            "top_k": workflow_model_config.get("top_k", 5)
                        }
                        result = await llm_manager.generate_text(
                            prompt, model_name=model_name, api_key=api_key,
                            api_base=api_base, **additional_params
                        )
                    else:
                        result = await llm_manager.generate_text(prompt)
                    return result

            # 在事件循环中运行异步调用
            if loop.is_running():
                # 如果当前线程已有运行的事件循环，使用asyncio.run_coroutine_threadsafe
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = asyncio.run_coroutine_threadsafe(
                        async_call(), loop)
                    result = future.result(timeout=300)  # 5分钟超时
            else:
                # 否则直接运行
                result = loop.run_until_complete(async_call())

            return result

        except Exception as e:
            self.logger.error(f"LLM非流式调用失败（同步）: {e}")
            raise

    async def _generate_subquestions_async(self, ques_type_post: Dict[str, Any], exam_subject: str,
                                           generated_material: str, subquestion_tasks: List[Dict[str, Any]],
                                           workflow_config: Optional[Dict[str, Any]] = None,
                                           stream_callback: Optional[Callable] = None,
                                           status_callback: Optional[Callable] = None) -> List[str]:
        """真正的异步生成子题（使用asyncio.gather）"""
        try:
            children = ques_type_post.get("Childs", [])
            if not children:
                return []

            # 使用真正的异步并发处理
            self.logger.debug(f"开始异步生成{len(children)}个子题")
            total = len(children)

            # 创建异步任务列表
            async_tasks = []
            for i, (child, task) in enumerate(zip(children, subquestion_tasks)):
                async_task = self._generate_single_subquestion_with_retry_async(
                    child, exam_subject, generated_material, task, workflow_config, i + 1, total
                )
                async_tasks.append(async_task)

            # 使用asyncio.gather进行真正的异步并发执行
            # 设置return_exceptions=True以确保即使某些任务失败也能继续
            results = await asyncio.gather(*async_tasks, return_exceptions=True)

            # 处理结果，将异常转换为错误消息，并更新进度
            generated_subquestions = []
            for idx, result in enumerate(results):
                if isinstance(result, Exception):
                    self.logger.error(f"子题{idx + 1}生成最终失败: {result}")
                    generated_subquestions.append(
                        f"子题{idx + 1}生成失败: {str(result)}")
                else:
                    generated_subquestions.append(result)

                # 细化进度：每处理一个子题结果，更新任务状态
                if status_callback:
                    progress = 60 + int(20 * (idx + 1) / total)  # 60~80%
                    await status_callback(
                        TaskStatus.PROCESSING,
                        f"已完成第{idx + 1}个子题目，共{total}",
                        {"step": "subquestion_generation", "progress": progress,
                            "detail": f"已完成第{idx + 1}个子题目，共{total}"}
                    )

            self.logger.debug(
                f"异步生成子题完成，成功生成{len([r for r in results if not isinstance(r, Exception)])}个子题")
            return generated_subquestions

        except Exception as e:
            self.logger.error(f"异步生成子题失败: {e}")
            raise

    async def _generate_single_subquestion_with_retry_async(self, child: Dict[str, Any], exam_subject: str,
                                                            generated_material: str, task: Dict[str, Any],
                                                            workflow_config: Optional[Dict[str, Any]] = None,
                                                            index: int = 1, total: int = 1) -> str:
        """异步生成单个子题（带重试机制）"""
        max_retries = 3
        retry_delay = 0.5  # 重试间隔0.5秒

        for attempt in range(max_retries):
            try:
                # 构建子题生成提示词
                prompt = self._build_single_subquestion_prompt(
                    child, exam_subject, generated_material, task, index, total
                )

                # 使用异步方式调用LLM（非流式）
                result = await self._call_llm_non_stream(
                    prompt, workflow_config, f"subquestion_{index}")

                self.logger.debug(  # 改为DEBUG级别
                    f"子题{index}生成成功（尝试{attempt + 1}/{max_retries}）")
                return result

            except Exception as e:
                self.logger.warning(
                    f"子题{index}生成失败（尝试{attempt + 1}/{max_retries}）: {e}")

                if attempt < max_retries - 1:
                    # 不是最后一次尝试，等待后重试
                    await asyncio.sleep(retry_delay)
                    retry_delay = min(retry_delay * 1.5, 2.0)  # 更温和的指数退避，最大2秒
                else:
                    # 最后一次尝试失败，抛出异常
                    self.logger.error(f"子题{index}生成最终失败，已重试{max_retries}次")
                    raise e

    def _generate_single_subquestion_with_retry(self, child: Dict[str, Any], exam_subject: str,
                                                generated_material: str, task: Dict[str, Any],
                                                workflow_config: Optional[Dict[str, Any]] = None,
                                                index: int = 1, total: int = 1) -> str:
        """生成单个子题（带重试机制，同步方法用于ThreadPoolExecutor）- 优化版本"""
        max_retries = 3
        retry_delay = 0.5  # 减少重试间隔到0.5秒

        for attempt in range(max_retries):
            try:
                # 构建子题生成提示词
                prompt = self._build_single_subquestion_prompt(
                    child, exam_subject, generated_material, task, index, total
                )

                # 使用同步方式调用LLM（非流式）
                result = self._call_llm_non_stream_sync(
                    prompt, workflow_config, f"subquestion_{index}")

                self.logger.debug(  # 改为DEBUG级别
                    f"子题{index}生成成功（尝试{attempt + 1}/{max_retries}）")
                return result

            except Exception as e:
                self.logger.warning(
                    f"子题{index}生成失败（尝试{attempt + 1}/{max_retries}）: {e}")

                if attempt < max_retries - 1:
                    # 不是最后一次尝试，等待后重试（减少延迟）
                    time.sleep(retry_delay)
                    retry_delay = min(retry_delay * 1.5, 2.0)  # 更温和的指数退避，最大2秒
                else:
                    # 最后一次尝试失败，抛出异常
                    self.logger.error(f"子题{index}生成最终失败，已重试{max_retries}次")
                    raise e

    async def _generate_single_subquestion_async(self, child: Dict[str, Any], exam_subject: str,
                                                 generated_material: str, task: Dict[str, Any],
                                                 workflow_config: Optional[Dict[str, Any]] = None,
                                                 index: int = 0, total: int = 1) -> str:
        """生成单个子题（异步方法，保留用于兼容性）"""
        try:
            # 构建子题生成提示词
            prompt = self._build_single_subquestion_prompt(
                child, exam_subject, generated_material, task, index + 1, total
            )

            # 使用对话模型生成子题（非流式）
            result = await self._call_llm_non_stream(prompt, workflow_config, f"subquestion_{index+1}")

            return result

        except Exception as e:
            self.logger.error(f"生成子题{index+1}失败: {e}")
            raise

    def _build_single_subquestion_prompt(self, child: Dict[str, Any], exam_subject: str,
                                         generated_material: str, task: Dict[str, Any],
                                         index: int, total: int) -> str:
        """构建单个子题生成提示词"""
        info = QuesBusinessInfo(child)

        child_type = info.ques_type_name
        difficulty = task.get("difficulty", "中等")
        knowledge_points = task.get("knowledge_points", "")
        count = info.ques_count
        strategy = task.get("strategy", "基于材料生成子题")
        ques_requirements = task.get("requirement", "基于材料生成子题")
        structure_content = parameter_mapper.map_question_structure_to_prompt(
            child, is_clone=False)
        prompt = f"""
## 角色与任务

你是一个专业的**{exam_subject}试题命制专家**。请基于给定的试题材料生成子题。

## 试题材料

```
{generated_material}
```

## 子题信息

- **子题类型**：{child_type}
- **难度要求**：{difficulty}
- **知识点**：{knowledge_points}
- **题目数量**：{count}道
- **命制策略**：{strategy}

## 命题具体要求

```
{ques_requirements}
```

## 具体工作要求

请基于上述材料生成符合要求的子题，确保：

1. **材料关联**：子题与材料内容紧密相关，并与子题的知识点紧密相关
2. **属性符合**：符合给定的题型和属性要求
3. 难度符合要求
4. 知识点覆盖准确

【输出要求】
1. 不要输出任何与子题无关的内容，注意只需要输出子题，不要输出试题子题等文字内容；
2. 确保子题符合命题要求。
3. 质量保证措施要切实有效；
4. 所有策略要符合{exam_subject}学科特点。
5. 注意严格按照输出试题结构的命制试题，不要将结构的内容作为子题内容。
6. 注意不要使用markdown格式输出。

【输出试题结构】
{structure_content}

输出：
"""
        return prompt.strip()

    def _assemble_basic_question_with_separator(self, ques_type_post: Dict[str, Any],
                                                questions: List[str]) -> Dict[str, Any]:
        """使用分割线组装基础题型试题"""
        # 组装完整试题内容
        separator = f"{'-'*10}试题分割线{'-'*10}"

        # 拼接各道试题
        if len(questions) == 1:
            # 只有一道试题时，不需要分割线
            assembled_content = questions[0]  # .replace("【试题描述】", "")
        else:
            # 多道试题时，使用分割线拼接
            assembled_parts = []
            for i, question in enumerate(questions):
                if i > 0:
                    assembled_parts.append(separator)
                assembled_parts.append(question)  # .replace("【试题描述】", "")
            assembled_content = "".join(assembled_parts)

        result = ques_type_post.copy()
        result["QuesStr"] = assembled_content

        return result

    def _assemble_composite_question_with_separator(self, ques_type_post: Dict[str, Any],
                                                    material: str, subquestions: List[str]) -> Dict[str, Any]:
        """组装组合题：材料放在最外层QuesStr，子题放在Childs第一个元素的QuesStr中"""
        result = ques_type_post.copy()

        # 材料放在最外层QuesStr
        result["QuesStr"] = material

        # 子题内容放在Childs第一个元素的QuesStr中，删除其他元素
        if "Childs" in result and result["Childs"]:
            # 使用分割线拼接所有子题
            separator = f"{'-'*10}试题分割线{'-'*10}"
            assembled_subquestions = []
            for subquestion in subquestions:
                assembled_subquestions.append(subquestion)

            # 将所有子题内容放在第一个子题的QuesStr中
            if assembled_subquestions:
                result["Childs"][0]["QuesStr"] = separator.join(
                    assembled_subquestions)  # .replace("【试题描述】", "")

            # 删除其他Childs元素，只保留第一个
            result["Childs"] = [result["Childs"][0]]

        return result

    def _build_basic_planning_prompt(self, ques_type_post: Dict[str, Any], exam_subject: str, question_count: int) -> str:
        """构建基础题型命题规划提示词"""
        base_name = ques_type_post.get("BaseName", "")
        ques_type_name = ques_type_post.get("QuesTypeName", "")

        # 使用新的属性提取器获取详细属性信息
        extracted_props = ques_props_extractor.extract_ques_props(
            ques_type_post)
        planning_context = extracted_props["planning_context"]
        detailed_props = extracted_props["detailed_props"]

        # 获取知识点补充信息
        knowledge_remark = ""
        if ques_props_extractor.has_property(detailed_props, "必备知识"):
            knowledge_remark = ques_props_extractor.get_property_remark(
                detailed_props, "必备知识") or ""

        prompt = f"""
## 角色与任务

你是一个专业的**{exam_subject}基础题型命题专家**。请为以下基础题型制定详细的命题规划。

## 命题任务内容

- **题目类型**：{base_name}({ques_type_name})
- **考试科目**：{exam_subject}
- **题目数量**：{question_count}道
- **命题素材**：{knowledge_remark}

## 试题属性要求

```
{planning_context}
```

## 详细命题规划

请制定详细的基础题型命题规划，包括：

### 1. 整体命题策略

- **难度分布**：各道试题的难度分布策略
- **知识覆盖**：知识点覆盖和分布安排
- **类型多样**：试题类型的多样性考虑

### 2. 单题命制策略

- **具体要求**：每道试题的具体命制要求
- 难度和知识点的精准控制
- 内容质量和创新性要求

# 3. 具体的任务列表和完成标准
- 每道试题的命制任务分解
- 各任务的完成标准和质量要求
- 整体协调和一致性保证

# 4. 质量保证措施
- 试题质量检查标准
- 难度和知识点平衡验证
- 整体命题质量评估

请以JSON格式输出规划结果：
{{
    "question_tasks": [
        {{
            "question_index": 1,
            "type": "题目类型",
            "difficulty": "难度要求",
            "knowledge_points": "知识点要求",
            "strategy": "命制策略",
            "requirements": "具体命制要求",
            "quality_standards": "质量标准"
        }}
    ],
    "overall_strategy": {{
        "difficulty_distribution": "难度分布策略",
        "knowledge_coverage": "知识点覆盖策略",
        "diversity_consideration": "多样性考虑"
    }},
    "quality_assurance": {{
        "individual_quality": "单题质量检查",
        "overall_balance": "整体平衡验证",
        "consistency_check": "一致性检查"
    }}
}}

# 输出要求
1. 严格按照JSON格式输出；
2. 确保每道试题的命制策略合理可行；
3. 质量保证措施要切实有效；
4. 所有策略要符合{exam_subject}学科特点；
5. 试题数量要与要求的{question_count}道保持一致。

输出：
"""
        return prompt.strip()

    def _parse_basic_planning_result(self, planning_result: str) -> Dict[str, Any]:
        """解析基础题型命题规划结果"""
        try:
            import json
            # 尝试提取JSON部分
            start_idx = planning_result.find('{')
            end_idx = planning_result.rfind('}') + 1
            if start_idx != -1 and end_idx != 0:
                json_str = planning_result[start_idx:end_idx]
                return json.loads(json_str)
            else:
                # 如果无法解析JSON，返回默认结构
                return {
                    "question_tasks": []
                }
        except Exception as e:
            self.logger.warning(f"解析基础题型命题规划失败: {e}")
            return {
                "question_tasks": []
            }

    async def _generate_planning_with_retry(self,
                                            prompt: str,
                                            workflow_config: Optional[Dict[str, Any]] = None,
                                            stream_callback: Optional[Callable] = None,
                                            status_callback: Optional[Callable] = None,
                                            max_retries: int = 3,
                                            is_composite: bool = False) -> Dict[str, Any]:
        """
        带重试机制的命题规划生成

        Args:
            prompt: 命题规划提示词
            workflow_config: 工作流配置
            stream_callback: 流式回调
            status_callback: 状态回调
            max_retries: 最大重试次数
            is_composite: 是否为组合题

        Returns:
            解析后的命题规划结果
        """
        last_error = None

        for attempt in range(max_retries):
            try:
                self.logger.debug(
                    f"命题规划尝试 {attempt + 1}/{max_retries}")  # 改为DEBUG级别

                # 更新状态
                if status_callback:
                    await status_callback(
                        TaskStatus.PROCESSING,
                        f"生成命题规划 (尝试 {attempt + 1}/{max_retries})",
                        {"step": "planning", "progress": 10 +
                            attempt * 5, "retry": attempt + 1}
                    )

                # 调用LLM生成命题规划
                planning_result = await self._call_llm_with_stream(
                    prompt, workflow_config, stream_callback, "命题规划", status_callback, 10, 30
                )

                # 解析命题规划结果
                if is_composite:
                    parsed_result = self._parse_planning_result_robust(
                        planning_result)
                else:
                    parsed_result = self._parse_basic_planning_result_robust(
                        planning_result)

                # 验证解析结果的有效性
                if self._validate_planning_result(parsed_result, is_composite):
                    self.logger.debug(
                        f"命题规划成功 (尝试 {attempt + 1})")  # 改为DEBUG级别
                    return parsed_result
                else:
                    raise ValueError("解析结果验证失败")

            except Exception as e:
                last_error = e
                self.logger.warning(f"命题规划尝试 {attempt + 1} 失败: {e}")

                if attempt < max_retries - 1:
                    # 不是最后一次尝试，等待后重试（减少延迟）
                    # 更温和的指数退避，最大2秒
                    await asyncio.sleep(min(0.5 * (1.5 ** attempt), 2.0))

                    # 更新状态
                    if status_callback:
                        await status_callback(
                            TaskStatus.PROCESSING,
                            f"命题规划重试中... (尝试 {attempt + 2}/{max_retries})",
                            {"step": "planning_retry", "progress": 15 +
                                attempt * 5, "retry": attempt + 2}
                        )
                else:
                    # 最后一次尝试失败
                    self.logger.error(f"命题规划最终失败，已重试 {max_retries} 次")
                    break

        # 所有重试都失败，返回默认结果
        self.logger.error(f"命题规划重试失败，使用默认规划: {last_error}")
        return self._get_default_planning_result(is_composite)

    def _parse_planning_result_robust(self, planning_result: str) -> Dict[str, Any]:
        """
        鲁棒的组合题命题规划结果解析
        """
        try:
            import json
            import re

            # 清理和预处理结果
            cleaned_result = self._clean_planning_result(planning_result)

            # 多种JSON提取策略
            json_str = self._extract_json_robust(cleaned_result)

            if not json_str:
                raise ValueError("无法提取JSON内容")

            # 解析JSON
            parsed_result = json.loads(json_str)

            # 标准化结果结构
            return self._normalize_composite_planning_result(parsed_result)

        except Exception as e:
            self.logger.warning(f"鲁棒解析组合题命题规划失败: {e}")
            return self._get_default_planning_result(is_composite=True)

    def _parse_basic_planning_result_robust(self, planning_result: str) -> Dict[str, Any]:
        """
        鲁棒的基础题命题规划结果解析
        """
        try:
            import json
            import re

            # 清理和预处理结果
            cleaned_result = self._clean_planning_result(planning_result)

            # 多种JSON提取策略
            json_str = self._extract_json_robust(cleaned_result)

            if not json_str:
                raise ValueError("无法提取JSON内容")

            # 解析JSON
            parsed_result = json.loads(json_str)

            # 标准化结果结构
            return self._normalize_basic_planning_result(parsed_result)

        except Exception as e:
            self.logger.warning(f"鲁棒解析基础题命题规划失败: {e}")
            return self._get_default_planning_result(is_composite=False)

    def _clean_planning_result(self, planning_result: str) -> str:
        """
        清理命题规划结果，移除无关内容
        """
        if not planning_result:
            return ""

        # 移除markdown代码块标记
        cleaned = re.sub(r'```json\s*', '', planning_result)
        cleaned = re.sub(r'```\s*$', '', cleaned)

        # 移除多余的空行和空格
        cleaned = re.sub(r'\n\s*\n', '\n', cleaned)
        cleaned = cleaned.strip()

        return cleaned

    def _extract_json_robust(self, text: str) -> str:
        """
        鲁棒的JSON提取，支持多种格式
        """
        if not text:
            return ""

        # 策略1: 查找完整的JSON对象
        json_patterns = [
            r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}',  # 嵌套JSON
            r'\{[^{}]*\}',  # 简单JSON
        ]

        for pattern in json_patterns:
            matches = re.findall(pattern, text, re.DOTALL)
            for match in matches:
                try:
                    json.loads(match)  # 验证JSON有效性
                    return match
                except:
                    continue

        # 策略2: 查找JSON开始和结束位置
        start_idx = text.find('{')
        end_idx = text.rfind('}')

        if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
            json_str = text[start_idx:end_idx + 1]
            try:
                json.loads(json_str)  # 验证JSON有效性
                return json_str
            except:
                pass

        # 策略3: 尝试修复常见的JSON格式问题
        fixed_json = self._fix_common_json_issues(text)
        if fixed_json:
            try:
                json.loads(fixed_json)
                return fixed_json
            except:
                pass

        return ""

    def _fix_common_json_issues(self, text: str) -> str:
        """
        修复常见的JSON格式问题
        """
        if not text:
            return ""

        # 修复单引号为双引号
        fixed = re.sub(r"'([^']*)'", r'"\1"', text)

        # 修复缺少引号的键名
        fixed = re.sub(r'(\w+):', r'"\1":', fixed)

        # 修复末尾的逗号
        fixed = re.sub(r',(\s*[}\]])', r'\1', fixed)

        # 修复注释
        fixed = re.sub(r'//.*$', '', fixed, flags=re.MULTILINE)
        fixed = re.sub(r'/\*.*?\*/', '', fixed, flags=re.DOTALL)

        return fixed

    def _normalize_composite_planning_result(self, parsed_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        标准化组合题命题规划结果
        """
        normalized = {
            "material_task": {},
            "subquestion_tasks": [],
            "planning_type": "composite"
        }

        # 处理材料任务
        if "material_task" in parsed_result:
            material_task = parsed_result["material_task"]
            if isinstance(material_task, str):
                normalized["material_task"] = {
                    "strategy": material_task,
                    "requirements": "基于策略生成材料"
                }
            elif isinstance(material_task, dict):
                normalized["material_task"] = {
                    "strategy": material_task.get("strategy", "生成通用材料"),
                    "requirements": material_task.get("requirements", "适合所有子题")
                }
        else:
            normalized["material_task"] = {
                "strategy": "生成通用材料",
                "requirements": "适合所有子题"
            }

        # 处理子题任务
        subquestion_tasks = []
        if "subquestion_tasks" in parsed_result:
            tasks = parsed_result["subquestion_tasks"]
            if isinstance(tasks, list):
                for i, task in enumerate(tasks):
                    if isinstance(task, dict):
                        normalized_task = {
                            "subquestion_index": task.get("subquestion_index", i + 1),
                            "type": task.get("type", "子题"),
                            "difficulty": task.get("difficulty", "中等"),
                            "knowledge_points": task.get("knowledge_points", ""),
                            "strategy": task.get("strategy", "基于材料生成子题"),
                            "requirements": task.get("requirements", "符合要求")
                        }
                        subquestion_tasks.append(normalized_task)

        normalized["subquestion_tasks"] = subquestion_tasks
        return normalized

    def _normalize_basic_planning_result(self, parsed_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        标准化基础题命题规划结果
        """
        normalized = {
            "material_task": {"strategy": "基础题无需材料", "requirements": "直接生成试题"},
            "subquestion_tasks": [],
            "planning_type": "basic"
        }

        # 处理试题任务
        question_tasks = []
        if "question_tasks" in parsed_result:
            tasks = parsed_result["question_tasks"]
            if isinstance(tasks, list):
                for i, task in enumerate(tasks):
                    if isinstance(task, dict):
                        normalized_task = {
                            "subquestion_index": task.get("question_index", task.get("subquestion_index", i + 1)),
                            "type": task.get("type", "试题"),
                            "difficulty": task.get("difficulty", "中等"),
                            "knowledge_points": task.get("knowledge_points", ""),
                            "strategy": task.get("strategy", "生成试题"),
                            "requirements": task.get("requirements", "符合要求")
                        }
                        question_tasks.append(normalized_task)

        normalized["subquestion_tasks"] = question_tasks
        return normalized

    def _validate_planning_result(self, planning_result: Dict[str, Any], is_composite: bool) -> bool:
        """
        验证命题规划结果的有效性
        """
        try:
            if not isinstance(planning_result, dict):
                return False

            # 检查必需字段
            if "material_task" not in planning_result:
                return False

            if "subquestion_tasks" not in planning_result:
                return False

            # 检查子题任务
            subquestion_tasks = planning_result["subquestion_tasks"]
            if not isinstance(subquestion_tasks, list):
                return False

            # 至少应该有一个子题任务
            if len(subquestion_tasks) == 0:
                return False

            # 检查每个子题任务的结构
            for task in subquestion_tasks:
                if not isinstance(task, dict):
                    return False

                # 检查必需字段
                required_fields = ["subquestion_index",
                                   "type", "difficulty", "strategy"]
                for field in required_fields:
                    if field not in task:
                        return False

            return True

        except Exception as e:
            self.logger.warning(f"验证命题规划结果失败: {e}")
            return False

    def _get_default_planning_result(self, is_composite: bool) -> Dict[str, Any]:
        """
        获取默认的命题规划结果
        """
        if is_composite:
            return {
                "material_task": {
                    "strategy": "生成通用材料",
                    "requirements": "适合所有子题"
                },
                "subquestion_tasks": [
                    {
                        "subquestion_index": 1,
                        "type": "子题",
                        "difficulty": "中等",
                        "knowledge_points": "",
                        "strategy": "基于材料生成子题",
                        "requirements": "符合要求"
                    }
                ],
                "planning_type": "composite_default"
            }
        else:
            return {
                "material_task": {
                    "strategy": "基础题无需材料",
                    "requirements": "直接生成试题"
                },
                "subquestion_tasks": [
                    {
                        "subquestion_index": 1,
                        "type": "试题",
                        "difficulty": "中等",
                        "knowledge_points": "",
                        "strategy": "生成试题",
                        "requirements": "符合要求"
                    }
                ],
                "planning_type": "basic_default"
            }

    async def _generate_questions_async(self, ques_type_post: Dict[str, Any], exam_subject: str,
                                        planning_tasks: Dict[str, Any], workflow_config: Optional[Dict[str, Any]] = None,
                                        stream_callback: Optional[Callable] = None,
                                        status_callback: Optional[Callable] = None) -> List[str]:
        """真正的异步生成基础题型试题（使用asyncio.gather）"""
        try:
            # 支持新的统一规划结构
            question_tasks = planning_tasks.get("subquestion_tasks", [])
            planning_type = planning_tasks.get("planning_type", "basic")
            question_count = ques_type_post.get("QuesCount", 1)

            if not question_tasks:
                # 如果没有规划任务，创建默认任务
                question_tasks = [{"question_index": i + 1}
                                  for i in range(question_count)]

            # 使用真正的异步并发处理
            self.logger.debug(f"开始异步生成{len(question_tasks)}个试题")

            # 创建异步任务列表
            async_tasks = []
            for i, task in enumerate(question_tasks):
                async_task = self._generate_single_question_with_retry_async(
                    ques_type_post, exam_subject, task, workflow_config, i +
                    1, len(question_tasks)
                )
                async_tasks.append(async_task)

            # 使用asyncio.gather进行真正的异步并发执行
            # 设置return_exceptions=True以确保即使某些任务失败也能继续
            results = await asyncio.gather(*async_tasks, return_exceptions=True)

            # 处理结果，将异常转换为错误消息
            generated_questions = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    self.logger.error(f"试题{i + 1}生成最终失败: {result}")
                    generated_questions.append(f"试题{i + 1}生成失败: {str(result)}")
                else:
                    generated_questions.append(result)

            self.logger.debug(
                f"异步生成试题完成，成功生成{len([r for r in results if not isinstance(r, Exception)])}个试题")
            return generated_questions

        except Exception as e:
            self.logger.error(f"异步生成试题失败: {e}")
            raise

    async def _generate_single_question_with_retry_async(self, ques_type_post: Dict[str, Any], exam_subject: str,
                                                         task: Dict[str, Any], workflow_config: Optional[Dict[str, Any]] = None,
                                                         index: int = 1, total: int = 1) -> str:
        """异步生成单个试题（带重试机制）"""
        max_retries = 3
        retry_delay = 0.5  # 重试间隔0.5秒

        for attempt in range(max_retries):
            try:
                # 构建试题生成提示词
                prompt = self._build_single_question_prompt(
                    ques_type_post, exam_subject, task, index, total
                )

                # 使用异步方式调用LLM（非流式）
                result = await self._call_llm_non_stream(
                    prompt, workflow_config, f"question_{index}")

                self.logger.debug(  # 改为DEBUG级别
                    f"试题{index}生成成功（尝试{attempt + 1}/{max_retries}）")
                return result

            except Exception as e:
                self.logger.warning(
                    f"试题{index}生成失败（尝试{attempt + 1}/{max_retries}）: {e}")

                if attempt < max_retries - 1:
                    # 不是最后一次尝试，等待后重试
                    await asyncio.sleep(retry_delay)
                    retry_delay = min(retry_delay * 1.5, 2.0)  # 更温和的指数退避，最大2秒
                else:
                    # 最后一次尝试失败，抛出异常
                    self.logger.error(f"试题{index}生成最终失败，已重试{max_retries}次")
                    raise e

    def _generate_single_question_with_retry(self, ques_type_post: Dict[str, Any], exam_subject: str,
                                             task: Dict[str, Any], workflow_config: Optional[Dict[str, Any]] = None,
                                             index: int = 1, total: int = 1) -> str:
        """生成单个试题（带重试机制，同步方法用于ThreadPoolExecutor）- 优化版本"""
        max_retries = 3
        retry_delay = 0.5  # 减少重试间隔到0.5秒

        for attempt in range(max_retries):
            try:
                # 构建试题生成提示词
                prompt = self._build_single_question_prompt(
                    ques_type_post, exam_subject, task, index, total
                )

                # 使用同步方式调用LLM（非流式）
                result = self._call_llm_non_stream_sync(
                    prompt, workflow_config, f"question_{index}")

                self.logger.debug(  # 改为DEBUG级别
                    f"试题{index}生成成功（尝试{attempt + 1}/{max_retries}）")
                return result

            except Exception as e:
                self.logger.warning(
                    f"试题{index}生成失败（尝试{attempt + 1}/{max_retries}）: {e}")

                if attempt < max_retries - 1:
                    # 不是最后一次尝试，等待后重试（减少延迟）
                    time.sleep(retry_delay)
                    retry_delay = min(retry_delay * 1.5, 2.0)  # 更温和的指数退避，最大2秒
                else:
                    # 最后一次尝试失败，抛出异常
                    self.logger.error(f"试题{index}生成最终失败，已重试{max_retries}次")
                    raise e

    def _build_single_question_prompt(self, ques_type_post: Dict[str, Any], exam_subject: str,
                                      task: Dict[str, Any], index: int, total: int) -> str:
        """构建单个试题生成提示词"""
        info = QuesBusinessInfo(ques_type_post)
        base_name = info.base_name
        ques_type_name = info.ques_type_name
        difficulty = task.get("difficulty", "中等")
        knowledge_points = task.get("knowledge_points", "")
        strategy = task.get("strategy", "基于材料生成子题")
        ques_requirements = task.get("requirements", "")

        structure_content = parameter_mapper.map_question_structure_to_prompt(
            ques_type_post, is_clone=False)
        prompt = f"""
## 角色与任务

你是一个专业的**{exam_subject}试题命制专家**。请基于给定的试题材料生成试题。

## 试题信息

- **试题类型**：{ques_type_name}
- **难度要求**：{difficulty}
- **知识点**：{knowledge_points}
- **命制策略**：{strategy}

## 命题具体要求

```
{ques_requirements}
```

## 具体工作要求

请基于上述材料生成符合要求的试题，确保：

1. **属性符合**：符合给定的题型和属性要求
2. **难度准确**：难度符合要求
3. **知识覆盖**：知识点覆盖准确

## 输出要求

1. **内容纯净**：不要输出任何与试题无关的内容，注意只需要输出试题，不要输出试题等文字内容
2. 确保试题符合命题要求。
3. 注意严格按照输出试题结构的命制试题，不要将结构的内容作为试题内容。
4. 注意不要使用markdown格式输出。

【输出试题结构】
{structure_content}

输出：
"""
        return prompt.strip()


# 全局实例
intelligent_question_service = IntelligentQuestionService()
