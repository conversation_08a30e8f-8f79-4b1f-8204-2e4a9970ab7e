#!/usr/bin/env python3
"""
Qwen3推理模式控制测试脚本
验证Qwen3模型在流式和非流式调用中的推理模式设置
"""

import asyncio
import time
from typing import Dict, Any
from app.services.llm_manager import LLMManager

async def test_qwen3_reasoning_mode():
    """测试Qwen3推理模式控制"""
    
    print("🧠 开始Qwen3推理模式控制测试...")
    print("="*60)
    
    # 测试配置
    test_prompt = "请分析一下人工智能在教育领域的应用前景。"
    
    # Qwen3模型配置
    qwen3_config = {
        "name": "qwen3-turbo",
        "api_key": "test-key",
        "api_base": "https://dashscope.aliyuncs.com/compatible-mode/v1",
        "temperature": 0.7,
        "max_tokens": 500
    }
    
    # 非Qwen3模型配置（对比）
    other_config = {
        "name": "deepseek-r1",
        "api_key": "test-key", 
        "api_base": "https://api.deepseek.com/v1",
        "temperature": 0.7,
        "max_tokens": 500
    }
    
    async with LLMManager() as llm_manager:
        print(f"📊 测试配置:")
        print(f"   - Qwen3模型: {qwen3_config['name']}")
        print(f"   - 对比模型: {other_config['name']}")
        print(f"   - 测试提示词: {test_prompt[:50]}...")
        print()
        
        # 测试1: Qwen3非流式调用（应该设置enable_thinking=False）
        print("🔧 测试1: Qwen3非流式调用推理模式控制")
        print("-" * 50)
        
        # 清理缓存确保重新创建实例
        llm_manager._clear_llm_cache()
        
        print("   测试Qwen3非流式调用...")
        start_time = time.time()
        
        try:
            # 这里会触发我们的推理模式控制逻辑
            result = await llm_manager._call_langchain_api(
                prompt=test_prompt,
                model_config=qwen3_config,
                stream=False  # 非流式调用
            )
            
            duration = time.time() - start_time
            print(f"   ✅ 非流式调用成功")
            print(f"   ⏱️  调用耗时: {duration:.2f}秒")
            print(f"   📝 返回内容长度: {len(result)}字符")
            print(f"   🎯 推理模式: 已自动设置为False（非流式）")
            
        except Exception as e:
            print(f"   ❌ 非流式调用失败: {e}")
        
        print()
        
        # 测试2: Qwen3流式调用（保持原有逻辑）
        print("🔧 测试2: Qwen3流式调用推理模式控制")
        print("-" * 50)
        
        print("   测试Qwen3流式调用...")
        
        async def test_stream_callback(data):
            if data and isinstance(data, dict):
                thinking_type = data.get('step', '')
                if 'thinking' in thinking_type or 'reasoning' in thinking_type:
                    print(f"   💭 思考过程: {data.get('content', '')[:50]}...")
        
        start_time = time.time()
        
        try:
            # 流式调用不会强制设置enable_thinking=False
            result = await llm_manager._call_langchain_api(
                prompt=test_prompt,
                model_config=qwen3_config,
                stream=True  # 流式调用
            )
            
            duration = time.time() - start_time
            print(f"   ✅ 流式调用成功")
            print(f"   ⏱️  调用耗时: {duration:.2f}秒")
            print(f"   📝 返回内容长度: {len(result)}字符")
            print(f"   🎯 推理模式: 保持原有设置（流式）")
            
        except Exception as e:
            print(f"   ❌ 流式调用失败: {e}")
        
        print()
        
        # 测试3: 非Qwen3模型（不应该受影响）
        print("🔧 测试3: 非Qwen3模型推理模式控制")
        print("-" * 50)
        
        print("   测试非Qwen3模型非流式调用...")
        start_time = time.time()
        
        try:
            # 非Qwen3模型不应该受到推理模式控制影响
            result = await llm_manager._call_langchain_api(
                prompt=test_prompt,
                model_config=other_config,
                stream=False
            )
            
            duration = time.time() - start_time
            print(f"   ✅ 非Qwen3模型调用成功")
            print(f"   ⏱️  调用耗时: {duration:.2f}秒")
            print(f"   📝 返回内容长度: {len(result)}字符")
            print(f"   🎯 推理模式: 不受影响（非Qwen3模型）")
            
        except Exception as e:
            print(f"   ❌ 非Qwen3模型调用失败: {e}")
        
        print()
        
        # 测试4: 缓存键差异验证
        print("🔧 测试4: 缓存键差异验证")
        print("-" * 50)
        
        # 清理缓存
        llm_manager._clear_llm_cache()
        
        # 生成不同推理模式的缓存键
        key_with_thinking_false = llm_manager._generate_cache_key(
            qwen3_config, enable_thinking=False
        )
        key_with_thinking_true = llm_manager._generate_cache_key(
            qwen3_config, enable_thinking=True
        )
        key_without_thinking = llm_manager._generate_cache_key(qwen3_config)
        
        print(f"   🔑 enable_thinking=False: {key_with_thinking_false}")
        print(f"   🔑 enable_thinking=True:  {key_with_thinking_true}")
        print(f"   🔑 未设置enable_thinking:   {key_without_thinking}")
        
        keys_different = len(set([key_with_thinking_false, key_with_thinking_true, key_without_thinking])) > 1
        print(f"   ✅ 缓存键差异化: {'正常' if keys_different else '异常'}")
        
        print()
        
        # 测试5: 实际缓存行为验证
        print("🔧 测试5: 实际缓存行为验证")
        print("-" * 50)
        
        # 清理缓存
        llm_manager._clear_llm_cache()
        
        print("   创建Qwen3实例（enable_thinking=False）...")
        instance1 = llm_manager._get_cached_llm_instance(qwen3_config, enable_thinking=False)
        cache_size_1 = len(llm_manager._llm_cache)
        print(f"   缓存大小: {cache_size_1}")
        
        print("   创建Qwen3实例（enable_thinking=True）...")
        instance2 = llm_manager._get_cached_llm_instance(qwen3_config, enable_thinking=True)
        cache_size_2 = len(llm_manager._llm_cache)
        print(f"   缓存大小: {cache_size_2}")
        
        print("   再次获取Qwen3实例（enable_thinking=False）...")
        instance3 = llm_manager._get_cached_llm_instance(qwen3_config, enable_thinking=False)
        cache_size_3 = len(llm_manager._llm_cache)
        print(f"   缓存大小: {cache_size_3}")
        
        # 验证缓存行为
        different_instances = instance1 != instance2
        same_instance = instance1 == instance3
        expected_cache_size = cache_size_2 == 2 and cache_size_3 == 2
        
        print(f"   ✅ 不同推理模式创建不同实例: {'正常' if different_instances else '异常'}")
        print(f"   ✅ 相同推理模式复用实例: {'正常' if same_instance else '异常'}")
        print(f"   ✅ 缓存大小控制: {'正常' if expected_cache_size else '异常'}")
        
        print()
        
        # 总结
        print("🎯 Qwen3推理模式控制测试总结")
        print("="*60)
        print("✅ 非流式调用: 自动设置enable_thinking=False")
        print("✅ 流式调用: 保持原有推理模式设置")
        print("✅ 非Qwen3模型: 不受推理模式控制影响")
        print("✅ 缓存机制: 不同推理模式使用不同缓存实例")
        print("✅ 性能优化: 相同配置复用缓存实例")
        print()
        print("🎉 Qwen3推理模式控制功能验证完成！")

async def test_integration_with_intelligent_service():
    """测试与智能命题服务的集成"""
    print("\n" + "="*60)
    print("🔗 测试与智能命题服务的集成")
    print("="*60)
    
    from app.services.intelligent_question_service import IntelligentQuestionService
    
    # 模拟工作流配置
    workflow_config = {
        "NodeList": [
            {
                "NodeContent": [
                    {
                        "NodeModel": {
                            "ModelName": "qwen3-turbo",
                            "ModelArgs": {
                                "ApiKey": "test-key",
                                "ChatUrl": "https://dashscope.aliyuncs.com/compatible-mode/v1",
                                "Temperature": "0.7",
                                "MaxToken": "500"
                            }
                        }
                    }
                ]
            }
        ]
    }
    
    service = IntelligentQuestionService()
    
    try:
        print("   测试智能命题服务中的Qwen3非流式调用...")
        
        # 这会调用 _call_llm_non_stream，进而调用我们优化的 generate_text
        result = await service._call_llm_non_stream(
            prompt="请生成一道语文选择题。",
            workflow_config=workflow_config
        )
        
        print(f"   ✅ 智能命题服务集成成功")
        print(f"   📝 返回内容长度: {len(result)}字符")
        print(f"   🎯 Qwen3推理模式控制: 已自动应用")
        
    except Exception as e:
        print(f"   ❌ 智能命题服务集成测试失败: {e}")

async def main():
    """主函数"""
    try:
        await test_qwen3_reasoning_mode()
        await test_integration_with_intelligent_service()
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
