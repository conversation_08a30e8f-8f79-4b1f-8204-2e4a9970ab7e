"""
配置管理器
用于读取和管理系统配置
"""

import os
import yaml
from typing import Dict, Any, Optional
from loguru import logger


class ConfigManager:
    """配置管理器"""

    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = config_path
        self.config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if not os.path.exists(self.config_path):
                logger.warning(f"配置文件 {self.config_path} 不存在，使用默认配置")
                return self._get_default_config()

            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)

            # 处理环境变量
            config = self._process_env_vars(config)

            logger.info(f"成功加载配置文件: {self.config_path}")
            return config

        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return self._get_default_config()

    def _process_env_vars(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """处理环境变量"""
        def replace_env_vars(obj):
            if isinstance(obj, dict):
                return {k: replace_env_vars(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [replace_env_vars(item) for item in obj]
            elif isinstance(obj, str) and obj.startswith("${") and obj.endswith("}"):
                env_var = obj[2:-1]
                return os.getenv(env_var, obj)
            else:
                return obj

        return replace_env_vars(config)

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "models": {
                "default": "deepseek-r1",
                "available": [
                    {
                        "name": "deepseek-r1",
                        "api_base": "https://api.deepseek.com/v1",
                        "api_key": "",
                        "max_tokens": 4000,
                        "temperature": 0.7
                    }
                ]
            },
            "question_clone": {
                "strategies": {
                    "full": "完全克隆：保持原题结构，只变更试题内容",
                    "content_only": "仅克隆内容：保持所有属性，只改变QuesStr",
                    "structure_only": "仅克隆结构：保持题型和属性，清空具体内容"
                },
                "composite_steps": [
                    "分析组合题结构",
                    "克隆材料部分",
                    "克隆题干结构",
                    "生成子题目内容",
                    "组装完整试题"
                ],
                "basic_steps": [
                    "分析题目结构",
                    "生成新试题内容",
                    "验证克隆质量"
                ]
            },
            "prompts": {
                "basic_clone": "你是一个专业的试题克隆专家...",
                "composite_material_clone": "你是一个专业的试题材料克隆专家...",
                "composite_subquestion_clone": "你是一个专业的子题目克隆专家..."
            },
            "system": {
                "log_level": "INFO",
                "task_timeout": 300,
                "stream_interval": 1,
                "max_concurrent_tasks": 10
            }
        }

    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        keys = key.split('.')
        value = self.config

        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default

        return value

    def get_model_config(self, model_name: Optional[str] = None) -> Dict[str, Any]:
        """获取模型配置"""
        if not model_name:
            model_name = self.get("models.default", "deepseek-r1")

        available_models = self.get("models.available", [])
        for model in available_models:
            if model.get("name") == model_name:
                return model

        # 返回默认模型配置
        return available_models[0] if available_models else {}

    def get_prompt_template(self, prompt_name: str) -> str:
        """获取提示词模板"""
        return self.get(f"prompts.{prompt_name}", "")

    def get_clone_steps(self, is_composite: bool = False) -> list:
        """获取克隆步骤"""
        if is_composite:
            return self.get("question_clone.composite_steps", [])
        else:
            return self.get("question_clone.basic_steps", [])

    def get_strategy_description(self, strategy: str) -> str:
        """获取克隆策略描述"""
        strategies = self.get("question_clone.strategies", {})
        return strategies.get(strategy, "")


# 全局配置管理器实例
config_manager = ConfigManager()


def get_model_service_mode():
    try:
        with open("config.yaml", "r", encoding="utf-8") as f:
            config = yaml.safe_load(f)
        return config.get("model_service", {}).get("mode", "online")
    except Exception:
        return "online"
