# 智能命题系统前端接口参数定义

## 概述

本文档定义了智能命题系统新增功能的前端接口参数格式，为前端开发提供参考。

## 目录
1. [主题命题功能](#1-主题命题功能)
2. [材料处理功能](#2-材料处理功能)
3. [任务规划功能](#3-任务规划功能)
4. [试题执行功能](#4-试题执行功能)
5. [试题优化功能](#5-试题优化功能)
6. [知识库管理功能](#6-知识库管理功能)
7. [增强任务管理功能](#7-增强任务管理功能)
8. [文件处理功能](#8-文件处理功能)
9. [批量操作功能](#9-批量操作功能)

## 1. 主题命题功能

### 1.1 生成主题材料 `/GenerateTopicMaterial`

**请求参数：**
```json
{
    "topic": "项脊轩志",
    "subject": "语文",
    "task_id": "optional_task_id"
}
```

**响应格式：**
```json
{
    "material": "生成的命题材料内容...",
    "topic": "项脊轩志",
    "subject": "语文",
    "task_id": "uuid",
    "generated_at": "2025-07-29T10:00:00Z"
}
```

### 1.2 查询生成状态 `/GetTopicMaterialStatus/{task_id}`

**响应格式：**
```json
{
    "code": 200,
    "msg": "查询成功",
    "data": {
        "task_id": "uuid",
        "status": "COMPLETED",
        "created_at": 1722240000.0,
        "updated_at": 1722240060.0,
        "result": {
            "material": "生成的材料内容..."
        },
        "error_message": null
    }
}
```

## 2. 材料处理功能

### 2.1 处理材料内容 `/ProcessMaterial`

**请求参数（文本内容）：**
```json
{
    "content": "要处理的文本内容...",
    "file_data": null,
    "file_type": null,
    "filename": null,
    "task_id": "optional_task_id"
}
```

**请求参数（文件上传）：**
```json
{
    "content": null,
    "file_data": "base64编码的文件数据",
    "file_type": "pdf",
    "filename": "document.pdf",
    "task_id": "optional_task_id"
}
```

**响应格式：**
```json
{
    "extracted_content": "提取的文本内容...",
    "original_filename": "document.pdf",
    "file_type": "pdf",
    "content_length": 1500,
    "task_id": "uuid",
    "processed_at": "2025-07-29T10:00:00Z"
}
```

### 2.2 文件上传 `/UploadFile`

**请求参数（multipart/form-data）：**
- `file`: 上传的文件
- `task_id`: 可选的任务ID

## 3. 任务规划功能

### 3.1 创建任务规划 `/CreateTaskPlan`

**请求参数：**
```json
{
    "material": "命题材料内容...",
    "question_tasks": [
        {
            "question_type": "单选题",
            "quantity": 5,
            "difficulty": "中",
            "structural_elements": {},
            "knowledge_points": ["知识点1", "知识点2"],
            "special_requirements": "特殊要求说明"
        },
        {
            "question_type": "简答题",
            "quantity": 2,
            "difficulty": "难",
            "structural_elements": {},
            "knowledge_points": ["知识点3"],
            "special_requirements": null
        }
    ],
    "task_id": "optional_task_id"
}
```

**响应格式：**
```json
{
    "task_plan": [
        {
            "task_id": "task_1",
            "question_type": "单选题",
            "quantity": 5,
            "difficulty": "中",
            "knowledge_points": ["知识点1", "知识点2"],
            "estimated_time_per_question": 2,
            "total_estimated_time": 10,
            "priority": 7,
            "dependencies": [],
            "resources_needed": ["LLM模型"],
            "quality_criteria": {
                "accuracy": "内容准确无误",
                "clarity": "表达清晰明确"
            }
        }
    ],
    "total_questions": 7,
    "estimated_time": 26,
    "task_id": "uuid",
    "created_at": "2025-07-29T10:00:00Z"
}
```

### 3.2 优化任务规划 `/OptimizeTaskPlan`

**请求参数：**
```json
{
    "task_plan": [...],
    "optimization_goals": ["提高效率", "降低难度"]
}
```

## 4. 试题执行功能

### 4.1 执行试题生成 `/ExecuteQuestions`

**请求参数：**
```json
{
    "task_plan": [
        {
            "task_id": "task_1",
            "question_type": "单选题",
            "quantity": 5,
            "difficulty": "中",
            "knowledge_points": ["知识点1"],
            "special_requirements": ""
        }
    ],
    "task_id": "optional_task_id",
    "stream_response": true
}
```

**响应格式：**
```json
{
    "questions": [
        {
            "question_id": "uuid",
            "question_type": "单选题",
            "question_content": "题目内容...",
            "options": ["A. 选项1", "B. 选项2", "C. 选项3", "D. 选项4"],
            "answer": "A",
            "explanation": "答案解析...",
            "difficulty": "中",
            "knowledge_points": ["知识点1"],
            "metadata": {
                "generation_time": 1722240000.0,
                "question_index": 1
            }
        }
    ],
    "execution_summary": {
        "total_tasks": 1,
        "completed_tasks": 1,
        "failed_tasks": 0,
        "total_questions": 5,
        "total_execution_time": 15.5,
        "execution_details": [...]
    },
    "task_id": "uuid",
    "completed_at": "2025-07-29T10:00:00Z"
}
```

### 4.2 批量执行 `/BatchExecuteQuestions`

**请求参数：**
```json
{
    "task_plans": [
        [...],  // 任务规划1
        [...]   // 任务规划2
    ],
    "batch_size": 5,
    "parallel_processing": true
}
```

### 4.3 预览试题 `/PreviewQuestions`

**请求参数：**
```json
{
    "task_plan": {
        "question_type": "单选题",
        "quantity": 10,
        "difficulty": "中",
        "knowledge_points": ["知识点1"]
    },
    "preview_count": 3
}
```

## 5. 通用状态查询

### 5.1 任务状态枚举

- `PENDING`: 等待中
- `PROCESSING`: 处理中
- `COMPLETED`: 已完成
- `FAILED`: 失败
- `CANCELLED`: 已取消

### 5.2 难度等级枚举

- `易`: 简单
- `中`: 中等
- `难`: 困难

### 5.3 文件类型枚举

- `pdf`: PDF文件
- `doc`: Word文档（旧格式）
- `docx`: Word文档（新格式）
- `txt`: 文本文件

## 6. 错误处理

### 6.1 标准错误响应格式

```json
{
    "detail": "错误描述信息"
}
```

### 6.2 常见错误代码

- `400`: 请求参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误

## 7. 使用示例

### 7.1 完整的命题流程

1. **生成主题材料**
```javascript
const materialResponse = await fetch('/GenerateTopicMaterial', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        topic: "项脊轩志",
        subject: "语文"
    })
});
```

2. **创建任务规划**
```javascript
const planResponse = await fetch('/CreateTaskPlan', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        material: materialResponse.material,
        question_tasks: [
            {
                question_type: "单选题",
                quantity: 5,
                difficulty: "中",
                knowledge_points: ["项脊轩志"]
            }
        ]
    })
});
```

3. **执行试题生成**
```javascript
const executionResponse = await fetch('/ExecuteQuestions', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        task_plan: planResponse.task_plan,
        stream_response: true
    })
});
```

## 8. 试题优化功能

### 8.1 分析试题质量 `/AnalyzeQuestionQuality`

**请求参数：**
```json
{
    "questions": [
        {
            "question_id": "uuid",
            "question_type": "单选题",
            "question_content": "题目内容...",
            "options": ["A. 选项1", "B. 选项2"],
            "answer": "A",
            "explanation": "答案解析..."
        }
    ],
    "analysis_criteria": ["难度适中", "表述清晰", "答案准确"],
    "task_id": "optional_task_id"
}
```

### 8.2 优化试题 `/OptimizeQuestions`

**请求参数：**
```json
{
    "questions": [...],
    "optimization_goals": ["提高清晰度", "调整难度"],
    "target_difficulty": "中"
}
```

---

## 9. 知识库管理功能

### 9.1 上传知识内容 `/UploadKnowledge`

**请求参数：**
```json
{
    "content": "这是一段关于数学的知识内容...",
    "file_data": "base64编码的文件数据",
    "knowledge_type": "文本",
    "subject": "数学",
    "tags": ["高中数学", "函数", "基础知识"],
    "task_id": "optional_task_id"
}
```

### 9.2 搜索知识库 `/SearchKnowledge`

**请求参数：**
```json
{
    "query": "函数的性质",
    "subject": "数学",
    "knowledge_type": "文本",
    "limit": 10
}
```

---

## 10. 增强任务管理功能

### 10.1 获取任务详情 `/GetTaskDetails/{task_id}`

**URL参数：**
- `task_id`: 任务ID

### 10.2 监控任务状态 `/MonitorTasks`

**请求参数：**
```json
{
    "task_ids": ["task_1", "task_2", "task_3"]
}
```

### 10.3 批量操作任务 `/BatchOperateTasks`

**请求参数：**
```json
{
    "task_ids": ["task_1", "task_2"],
    "operation": "cancel",
    "parameters": {
        "reason": "用户取消"
    }
}
```

---

## 11. 文件处理功能

### 11.1 验证文件 `/ValidateFile`

**请求参数：**
```json
{
    "file_data": "base64编码的文件数据",
    "filename": "document.pdf",
    "max_size_mb": 50
}
```

### 11.2 处理文件 `/ProcessFile`

**请求参数：**
```json
{
    "file_data": "base64编码的文件数据",
    "filename": "document.pdf",
    "processing_options": {
        "clean_whitespace": true,
        "remove_special_chars": false
    }
}
```

### 11.3 分段内容 `/SegmentContent`

**请求参数：**
```json
{
    "content": "这是一段很长的文本内容...",
    "segment_type": "paragraph",
    "max_segment_length": 1000
}
```

---

## 12. 批量操作功能

### 12.1 批量生成试题 `/BatchGenerateQuestions`

**请求参数：**
```json
{
    "batch_config": {
        "topics": ["函数", "导数", "积分"],
        "subjects": ["数学"],
        "question_types": ["选择题", "填空题"]
    },
    "concurrent_limit": 5
}
```

### 12.2 批量处理材料 `/BatchProcessMaterials`

**请求参数：**
```json
{
    "materials": [
        {
            "content": "材料内容1",
            "file_data": "base64数据1"
        },
        {
            "content": "材料内容2",
            "file_data": "base64数据2"
        }
    ],
    "processing_config": {
        "processing_type": "extract",
        "clean_whitespace": true
    }
}
```

### 12.3 查询批量状态 `/GetBatchStatus/{batch_id}`

**URL参数：**
- `batch_id`: 批量操作ID

---

## 注意事项

1. 所有接口都支持异步处理，返回task_id用于查询进度
2. 文件数据需要使用Base64编码
3. 所有时间字段使用ISO 8601格式
4. 错误响应统一使用ErrorResponse格式
5. 建议前端实现重试机制处理网络异常
6. 批量操作建议设置合理的并发限制
7. 大文件处理时注意超时设置
8. 知识库搜索支持模糊匹配和语义搜索
