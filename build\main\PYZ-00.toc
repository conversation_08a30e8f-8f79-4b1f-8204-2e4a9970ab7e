('C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\build\\main\\PYZ-00.pyz',
 [('__future__',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_osx_support',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\_osx_support.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\Miniconda\\envs\\give_tag\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pytest',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\__init__.py',
   'PYMODULE'),
  ('_pytest._argcomplete',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\_argcomplete.py',
   'PYMODULE'),
  ('_pytest._code',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\_code\\__init__.py',
   'PYMODULE'),
  ('_pytest._code.code',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\_code\\code.py',
   'PYMODULE'),
  ('_pytest._code.source',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\_code\\source.py',
   'PYMODULE'),
  ('_pytest._io',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\_io\\__init__.py',
   'PYMODULE'),
  ('_pytest._io.saferepr',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\_io\\saferepr.py',
   'PYMODULE'),
  ('_pytest._io.terminalwriter',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\_io\\terminalwriter.py',
   'PYMODULE'),
  ('_pytest._io.wcwidth',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\_io\\wcwidth.py',
   'PYMODULE'),
  ('_pytest._py',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\_py\\__init__.py',
   'PYMODULE'),
  ('_pytest._py.error',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\_py\\error.py',
   'PYMODULE'),
  ('_pytest._py.path',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\_py\\path.py',
   'PYMODULE'),
  ('_pytest._version',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\_version.py',
   'PYMODULE'),
  ('_pytest.assertion',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\assertion\\__init__.py',
   'PYMODULE'),
  ('_pytest.assertion.rewrite',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\assertion\\rewrite.py',
   'PYMODULE'),
  ('_pytest.assertion.truncate',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\assertion\\truncate.py',
   'PYMODULE'),
  ('_pytest.assertion.util',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\assertion\\util.py',
   'PYMODULE'),
  ('_pytest.cacheprovider',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\cacheprovider.py',
   'PYMODULE'),
  ('_pytest.capture',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\capture.py',
   'PYMODULE'),
  ('_pytest.compat',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\compat.py',
   'PYMODULE'),
  ('_pytest.config',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\config\\__init__.py',
   'PYMODULE'),
  ('_pytest.config.argparsing',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\config\\argparsing.py',
   'PYMODULE'),
  ('_pytest.config.compat',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\config\\compat.py',
   'PYMODULE'),
  ('_pytest.config.exceptions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\config\\exceptions.py',
   'PYMODULE'),
  ('_pytest.config.findpaths',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\config\\findpaths.py',
   'PYMODULE'),
  ('_pytest.debugging',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\debugging.py',
   'PYMODULE'),
  ('_pytest.deprecated',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\deprecated.py',
   'PYMODULE'),
  ('_pytest.doctest',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\doctest.py',
   'PYMODULE'),
  ('_pytest.faulthandler',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\faulthandler.py',
   'PYMODULE'),
  ('_pytest.fixtures',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\fixtures.py',
   'PYMODULE'),
  ('_pytest.freeze_support',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\freeze_support.py',
   'PYMODULE'),
  ('_pytest.helpconfig',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\helpconfig.py',
   'PYMODULE'),
  ('_pytest.hookspec',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\hookspec.py',
   'PYMODULE'),
  ('_pytest.junitxml',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\junitxml.py',
   'PYMODULE'),
  ('_pytest.legacypath',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\legacypath.py',
   'PYMODULE'),
  ('_pytest.logging',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\logging.py',
   'PYMODULE'),
  ('_pytest.main',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\main.py',
   'PYMODULE'),
  ('_pytest.mark',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\mark\\__init__.py',
   'PYMODULE'),
  ('_pytest.mark.expression',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\mark\\expression.py',
   'PYMODULE'),
  ('_pytest.mark.structures',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\mark\\structures.py',
   'PYMODULE'),
  ('_pytest.monkeypatch',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\monkeypatch.py',
   'PYMODULE'),
  ('_pytest.nodes',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\nodes.py',
   'PYMODULE'),
  ('_pytest.nose',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\nose.py',
   'PYMODULE'),
  ('_pytest.outcomes',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\outcomes.py',
   'PYMODULE'),
  ('_pytest.pastebin',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\pastebin.py',
   'PYMODULE'),
  ('_pytest.pathlib',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\pathlib.py',
   'PYMODULE'),
  ('_pytest.pytester',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\pytester.py',
   'PYMODULE'),
  ('_pytest.pytester_assertions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\pytester_assertions.py',
   'PYMODULE'),
  ('_pytest.python',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\python.py',
   'PYMODULE'),
  ('_pytest.python_api',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\python_api.py',
   'PYMODULE'),
  ('_pytest.python_path',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\python_path.py',
   'PYMODULE'),
  ('_pytest.recwarn',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\recwarn.py',
   'PYMODULE'),
  ('_pytest.reports',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\reports.py',
   'PYMODULE'),
  ('_pytest.runner',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\runner.py',
   'PYMODULE'),
  ('_pytest.scope',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\scope.py',
   'PYMODULE'),
  ('_pytest.setuponly',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\setuponly.py',
   'PYMODULE'),
  ('_pytest.setupplan',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\setupplan.py',
   'PYMODULE'),
  ('_pytest.skipping',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\skipping.py',
   'PYMODULE'),
  ('_pytest.stash',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\stash.py',
   'PYMODULE'),
  ('_pytest.stepwise',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\stepwise.py',
   'PYMODULE'),
  ('_pytest.terminal',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\terminal.py',
   'PYMODULE'),
  ('_pytest.threadexception',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\threadexception.py',
   'PYMODULE'),
  ('_pytest.timing',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\timing.py',
   'PYMODULE'),
  ('_pytest.tmpdir',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\tmpdir.py',
   'PYMODULE'),
  ('_pytest.unittest',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\unittest.py',
   'PYMODULE'),
  ('_pytest.unraisableexception',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\unraisableexception.py',
   'PYMODULE'),
  ('_pytest.warning_types',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\warning_types.py',
   'PYMODULE'),
  ('_pytest.warnings',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\_pytest\\warnings.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime', 'D:\\Miniconda\\envs\\give_tag\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('aiohappyeyeballs',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohappyeyeballs\\__init__.py',
   'PYMODULE'),
  ('aiohappyeyeballs._staggered',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohappyeyeballs\\_staggered.py',
   'PYMODULE'),
  ('aiohappyeyeballs.impl',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohappyeyeballs\\impl.py',
   'PYMODULE'),
  ('aiohappyeyeballs.types',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohappyeyeballs\\types.py',
   'PYMODULE'),
  ('aiohappyeyeballs.utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohappyeyeballs\\utils.py',
   'PYMODULE'),
  ('aiohttp',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\__init__.py',
   'PYMODULE'),
  ('aiohttp._cookie_helpers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\_cookie_helpers.py',
   'PYMODULE'),
  ('aiohttp._websocket',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\_websocket\\__init__.py',
   'PYMODULE'),
  ('aiohttp._websocket.helpers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\_websocket\\helpers.py',
   'PYMODULE'),
  ('aiohttp._websocket.models',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\_websocket\\models.py',
   'PYMODULE'),
  ('aiohttp._websocket.reader',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\_websocket\\reader.py',
   'PYMODULE'),
  ('aiohttp._websocket.reader_py',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\_websocket\\reader_py.py',
   'PYMODULE'),
  ('aiohttp._websocket.writer',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\_websocket\\writer.py',
   'PYMODULE'),
  ('aiohttp.abc',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\abc.py',
   'PYMODULE'),
  ('aiohttp.base_protocol',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\base_protocol.py',
   'PYMODULE'),
  ('aiohttp.client',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\client.py',
   'PYMODULE'),
  ('aiohttp.client_exceptions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\client_exceptions.py',
   'PYMODULE'),
  ('aiohttp.client_middleware_digest_auth',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\client_middleware_digest_auth.py',
   'PYMODULE'),
  ('aiohttp.client_middlewares',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\client_middlewares.py',
   'PYMODULE'),
  ('aiohttp.client_proto',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\client_proto.py',
   'PYMODULE'),
  ('aiohttp.client_reqrep',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\client_reqrep.py',
   'PYMODULE'),
  ('aiohttp.client_ws',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\client_ws.py',
   'PYMODULE'),
  ('aiohttp.compression_utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\compression_utils.py',
   'PYMODULE'),
  ('aiohttp.connector',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\connector.py',
   'PYMODULE'),
  ('aiohttp.cookiejar',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\cookiejar.py',
   'PYMODULE'),
  ('aiohttp.formdata',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\formdata.py',
   'PYMODULE'),
  ('aiohttp.hdrs',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\hdrs.py',
   'PYMODULE'),
  ('aiohttp.helpers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\helpers.py',
   'PYMODULE'),
  ('aiohttp.http',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\http.py',
   'PYMODULE'),
  ('aiohttp.http_exceptions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\http_exceptions.py',
   'PYMODULE'),
  ('aiohttp.http_parser',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\http_parser.py',
   'PYMODULE'),
  ('aiohttp.http_websocket',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\http_websocket.py',
   'PYMODULE'),
  ('aiohttp.http_writer',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\http_writer.py',
   'PYMODULE'),
  ('aiohttp.log',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\log.py',
   'PYMODULE'),
  ('aiohttp.multipart',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\multipart.py',
   'PYMODULE'),
  ('aiohttp.payload',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\payload.py',
   'PYMODULE'),
  ('aiohttp.payload_streamer',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\payload_streamer.py',
   'PYMODULE'),
  ('aiohttp.resolver',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\resolver.py',
   'PYMODULE'),
  ('aiohttp.streams',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\streams.py',
   'PYMODULE'),
  ('aiohttp.tcp_helpers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\tcp_helpers.py',
   'PYMODULE'),
  ('aiohttp.tracing',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\tracing.py',
   'PYMODULE'),
  ('aiohttp.typedefs',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\typedefs.py',
   'PYMODULE'),
  ('aiohttp.web',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\web.py',
   'PYMODULE'),
  ('aiohttp.web_app',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\web_app.py',
   'PYMODULE'),
  ('aiohttp.web_exceptions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\web_exceptions.py',
   'PYMODULE'),
  ('aiohttp.web_fileresponse',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\web_fileresponse.py',
   'PYMODULE'),
  ('aiohttp.web_log',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\web_log.py',
   'PYMODULE'),
  ('aiohttp.web_middlewares',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\web_middlewares.py',
   'PYMODULE'),
  ('aiohttp.web_protocol',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\web_protocol.py',
   'PYMODULE'),
  ('aiohttp.web_request',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\web_request.py',
   'PYMODULE'),
  ('aiohttp.web_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\web_response.py',
   'PYMODULE'),
  ('aiohttp.web_routedef',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\web_routedef.py',
   'PYMODULE'),
  ('aiohttp.web_runner',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\web_runner.py',
   'PYMODULE'),
  ('aiohttp.web_server',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\web_server.py',
   'PYMODULE'),
  ('aiohttp.web_urldispatcher',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\web_urldispatcher.py',
   'PYMODULE'),
  ('aiohttp.web_ws',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\web_ws.py',
   'PYMODULE'),
  ('aiohttp.worker',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\worker.py',
   'PYMODULE'),
  ('aiosignal',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiosignal\\__init__.py',
   'PYMODULE'),
  ('annotated_types',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\annotated_types\\__init__.py',
   'PYMODULE'),
  ('anyio',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\__init__.py',
   'PYMODULE'),
  ('anyio._backends',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\_backends\\__init__.py',
   'PYMODULE'),
  ('anyio._backends._asyncio',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\_backends\\_asyncio.py',
   'PYMODULE'),
  ('anyio._backends._trio',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\_backends\\_trio.py',
   'PYMODULE'),
  ('anyio._core',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\_core\\__init__.py',
   'PYMODULE'),
  ('anyio._core._compat',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\_core\\_compat.py',
   'PYMODULE'),
  ('anyio._core._eventloop',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\_core\\_eventloop.py',
   'PYMODULE'),
  ('anyio._core._exceptions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\_core\\_exceptions.py',
   'PYMODULE'),
  ('anyio._core._fileio',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\_core\\_fileio.py',
   'PYMODULE'),
  ('anyio._core._resources',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\_core\\_resources.py',
   'PYMODULE'),
  ('anyio._core._signals',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\_core\\_signals.py',
   'PYMODULE'),
  ('anyio._core._sockets',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\_core\\_sockets.py',
   'PYMODULE'),
  ('anyio._core._streams',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\_core\\_streams.py',
   'PYMODULE'),
  ('anyio._core._subprocesses',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\_core\\_subprocesses.py',
   'PYMODULE'),
  ('anyio._core._synchronization',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\_core\\_synchronization.py',
   'PYMODULE'),
  ('anyio._core._tasks',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\_core\\_tasks.py',
   'PYMODULE'),
  ('anyio._core._testing',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\_core\\_testing.py',
   'PYMODULE'),
  ('anyio._core._typedattr',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\_core\\_typedattr.py',
   'PYMODULE'),
  ('anyio.abc',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\abc\\__init__.py',
   'PYMODULE'),
  ('anyio.abc._resources',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\abc\\_resources.py',
   'PYMODULE'),
  ('anyio.abc._sockets',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\abc\\_sockets.py',
   'PYMODULE'),
  ('anyio.abc._streams',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\abc\\_streams.py',
   'PYMODULE'),
  ('anyio.abc._subprocesses',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\abc\\_subprocesses.py',
   'PYMODULE'),
  ('anyio.abc._tasks',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\abc\\_tasks.py',
   'PYMODULE'),
  ('anyio.abc._testing',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\abc\\_testing.py',
   'PYMODULE'),
  ('anyio.from_thread',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\from_thread.py',
   'PYMODULE'),
  ('anyio.lowlevel',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\lowlevel.py',
   'PYMODULE'),
  ('anyio.streams',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\streams\\__init__.py',
   'PYMODULE'),
  ('anyio.streams.memory',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\streams\\memory.py',
   'PYMODULE'),
  ('anyio.streams.stapled',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\streams\\stapled.py',
   'PYMODULE'),
  ('anyio.streams.tls',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\streams\\tls.py',
   'PYMODULE'),
  ('anyio.to_thread',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\anyio\\to_thread.py',
   'PYMODULE'),
  ('app', 'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\__init__.py', 'PYMODULE'),
  ('app.api',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\api\\__init__.py',
   'PYMODULE'),
  ('app.api.agent',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\api\\agent.py',
   'PYMODULE'),
  ('app.api.stream',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\api\\stream.py',
   'PYMODULE'),
  ('app.api.task',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\api\\task.py',
   'PYMODULE'),
  ('app.core',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\core\\__init__.py',
   'PYMODULE'),
  ('app.core.state_manager',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\core\\state_manager.py',
   'PYMODULE'),
  ('app.models',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\models\\__init__.py',
   'PYMODULE'),
  ('app.models.schemas',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\models\\schemas.py',
   'PYMODULE'),
  ('app.services',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\services\\__init__.py',
   'PYMODULE'),
  ('app.services.intelligent_question_service',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\services\\intelligent_question_service.py',
   'PYMODULE'),
  ('app.services.llm_manager',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\services\\llm_manager.py',
   'PYMODULE'),
  ('app.services.question_cloner',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\services\\question_cloner.py',
   'PYMODULE'),
  ('app.utils',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\utils\\__init__.py',
   'PYMODULE'),
  ('app.utils.config_manager',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\utils\\config_manager.py',
   'PYMODULE'),
  ('app.utils.parameter_mapper',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\utils\\parameter_mapper.py',
   'PYMODULE'),
  ('app.utils.ques_business_info',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\utils\\ques_business_info.py',
   'PYMODULE'),
  ('app.utils.ques_props_extractor',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\utils\\ques_props_extractor.py',
   'PYMODULE'),
  ('app.utils.question_parser',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\utils\\question_parser.py',
   'PYMODULE'),
  ('app.utils.workflow_utils',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\utils\\workflow_utils.py',
   'PYMODULE'),
  ('argparse', 'D:\\Miniconda\\envs\\give_tag\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\Miniconda\\envs\\give_tag\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('attr',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\attr\\__init__.py',
   'PYMODULE'),
  ('attr._cmp',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\attr\\_cmp.py',
   'PYMODULE'),
  ('attr._compat',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\attr\\_compat.py',
   'PYMODULE'),
  ('attr._config',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\attr\\_config.py',
   'PYMODULE'),
  ('attr._funcs',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\attr\\_funcs.py',
   'PYMODULE'),
  ('attr._make',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\attr\\_make.py',
   'PYMODULE'),
  ('attr._next_gen',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\attr\\_next_gen.py',
   'PYMODULE'),
  ('attr._version_info',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\attr\\_version_info.py',
   'PYMODULE'),
  ('attr.converters',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\attr\\converters.py',
   'PYMODULE'),
  ('attr.exceptions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\attr\\exceptions.py',
   'PYMODULE'),
  ('attr.filters',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\attr\\filters.py',
   'PYMODULE'),
  ('attr.setters',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\attr\\setters.py',
   'PYMODULE'),
  ('attr.validators',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\attr\\validators.py',
   'PYMODULE'),
  ('backports',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'D:\\Miniconda\\envs\\give_tag\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'D:\\Miniconda\\envs\\give_tag\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'D:\\Miniconda\\envs\\give_tag\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\Miniconda\\envs\\give_tag\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\Miniconda\\envs\\give_tag\\Lib\\calendar.py', 'PYMODULE'),
  ('certifi',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cgi', 'D:\\Miniconda\\envs\\give_tag\\Lib\\cgi.py', 'PYMODULE'),
  ('charset_normalizer',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('click',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click._compat',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._termui_impl',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click._textwrap',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click._winconsole',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.core',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.decorators',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.exceptions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.formatting',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click.globals',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.parser',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.termui',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click.types',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click.utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('cmd', 'D:\\Miniconda\\envs\\give_tag\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\Miniconda\\envs\\give_tag\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\Miniconda\\envs\\give_tag\\Lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys', 'D:\\Miniconda\\envs\\give_tag\\Lib\\colorsys.py', 'PYMODULE'),
  ('concurrent',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'D:\\Miniconda\\envs\\give_tag\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\Miniconda\\envs\\give_tag\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime', 'D:\\Miniconda\\envs\\give_tag\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\Miniconda\\envs\\give_tag\\Lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'D:\\Miniconda\\envs\\give_tag\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\Miniconda\\envs\\give_tag\\Lib\\dis.py', 'PYMODULE'),
  ('distro',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\distro\\__init__.py',
   'PYMODULE'),
  ('distro.distro',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\distro\\distro.py',
   'PYMODULE'),
  ('distutils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.cmd',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.command',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('distutils.command.build',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.config',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\distutils\\config.py',
   'PYMODULE'),
  ('distutils.core',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.debug',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.dist',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\distutils\\dist.py',
   'PYMODULE'),
  ('distutils.errors',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.extension',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.file_util',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.log',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils.spawn',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.util',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.version',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('doctest', 'D:\\Miniconda\\envs\\give_tag\\Lib\\doctest.py', 'PYMODULE'),
  ('dotenv',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.parser',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('dotenv.variables',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('email',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('fastapi',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\__init__.py',
   'PYMODULE'),
  ('fastapi._compat',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\_compat.py',
   'PYMODULE'),
  ('fastapi.applications',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\applications.py',
   'PYMODULE'),
  ('fastapi.background',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\background.py',
   'PYMODULE'),
  ('fastapi.concurrency',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\concurrency.py',
   'PYMODULE'),
  ('fastapi.datastructures',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\datastructures.py',
   'PYMODULE'),
  ('fastapi.dependencies',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\dependencies\\__init__.py',
   'PYMODULE'),
  ('fastapi.dependencies.models',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\dependencies\\models.py',
   'PYMODULE'),
  ('fastapi.dependencies.utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\dependencies\\utils.py',
   'PYMODULE'),
  ('fastapi.encoders',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\encoders.py',
   'PYMODULE'),
  ('fastapi.exception_handlers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\exception_handlers.py',
   'PYMODULE'),
  ('fastapi.exceptions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\exceptions.py',
   'PYMODULE'),
  ('fastapi.logger',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\logger.py',
   'PYMODULE'),
  ('fastapi.middleware',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\middleware\\__init__.py',
   'PYMODULE'),
  ('fastapi.middleware.asyncexitstack',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\middleware\\asyncexitstack.py',
   'PYMODULE'),
  ('fastapi.middleware.cors',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\middleware\\cors.py',
   'PYMODULE'),
  ('fastapi.openapi',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\openapi\\__init__.py',
   'PYMODULE'),
  ('fastapi.openapi.constants',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\openapi\\constants.py',
   'PYMODULE'),
  ('fastapi.openapi.docs',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\openapi\\docs.py',
   'PYMODULE'),
  ('fastapi.openapi.models',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\openapi\\models.py',
   'PYMODULE'),
  ('fastapi.openapi.utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\openapi\\utils.py',
   'PYMODULE'),
  ('fastapi.param_functions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\param_functions.py',
   'PYMODULE'),
  ('fastapi.params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\params.py',
   'PYMODULE'),
  ('fastapi.requests',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\requests.py',
   'PYMODULE'),
  ('fastapi.responses',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\responses.py',
   'PYMODULE'),
  ('fastapi.routing',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\routing.py',
   'PYMODULE'),
  ('fastapi.security',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\security\\__init__.py',
   'PYMODULE'),
  ('fastapi.security.api_key',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\security\\api_key.py',
   'PYMODULE'),
  ('fastapi.security.base',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\security\\base.py',
   'PYMODULE'),
  ('fastapi.security.http',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\security\\http.py',
   'PYMODULE'),
  ('fastapi.security.oauth2',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\security\\oauth2.py',
   'PYMODULE'),
  ('fastapi.security.open_id_connect_url',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\security\\open_id_connect_url.py',
   'PYMODULE'),
  ('fastapi.security.utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\security\\utils.py',
   'PYMODULE'),
  ('fastapi.types',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\types.py',
   'PYMODULE'),
  ('fastapi.utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\utils.py',
   'PYMODULE'),
  ('fastapi.websockets',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\fastapi\\websockets.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\Miniconda\\envs\\give_tag\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\Miniconda\\envs\\give_tag\\Lib\\fractions.py', 'PYMODULE'),
  ('frozenlist',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\frozenlist\\__init__.py',
   'PYMODULE'),
  ('ftplib', 'D:\\Miniconda\\envs\\give_tag\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\Miniconda\\envs\\give_tag\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\Miniconda\\envs\\give_tag\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\Miniconda\\envs\\give_tag\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\Miniconda\\envs\\give_tag\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\Miniconda\\envs\\give_tag\\Lib\\gzip.py', 'PYMODULE'),
  ('h11',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\h11\\__init__.py',
   'PYMODULE'),
  ('h11._abnf',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\h11\\_abnf.py',
   'PYMODULE'),
  ('h11._connection',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\h11\\_connection.py',
   'PYMODULE'),
  ('h11._events',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\h11\\_events.py',
   'PYMODULE'),
  ('h11._headers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\h11\\_headers.py',
   'PYMODULE'),
  ('h11._readers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\h11\\_readers.py',
   'PYMODULE'),
  ('h11._receivebuffer',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\h11\\_receivebuffer.py',
   'PYMODULE'),
  ('h11._state',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\h11\\_state.py',
   'PYMODULE'),
  ('h11._util',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\h11\\_util.py',
   'PYMODULE'),
  ('h11._version',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\h11\\_version.py',
   'PYMODULE'),
  ('h11._writers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\h11\\_writers.py',
   'PYMODULE'),
  ('hashlib', 'D:\\Miniconda\\envs\\give_tag\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\Miniconda\\envs\\give_tag\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'D:\\Miniconda\\envs\\give_tag\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('http', 'D:\\Miniconda\\envs\\give_tag\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\http\\server.py',
   'PYMODULE'),
  ('httpcore',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\__init__.py',
   'PYMODULE'),
  ('httpcore._api',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\_api.py',
   'PYMODULE'),
  ('httpcore._async',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\_async\\__init__.py',
   'PYMODULE'),
  ('httpcore._async.connection',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\_async\\connection.py',
   'PYMODULE'),
  ('httpcore._async.connection_pool',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\_async\\connection_pool.py',
   'PYMODULE'),
  ('httpcore._async.http11',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\_async\\http11.py',
   'PYMODULE'),
  ('httpcore._async.http2',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\_async\\http2.py',
   'PYMODULE'),
  ('httpcore._async.http_proxy',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\_async\\http_proxy.py',
   'PYMODULE'),
  ('httpcore._async.interfaces',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\_async\\interfaces.py',
   'PYMODULE'),
  ('httpcore._async.socks_proxy',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\_async\\socks_proxy.py',
   'PYMODULE'),
  ('httpcore._backends',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\_backends\\__init__.py',
   'PYMODULE'),
  ('httpcore._backends.anyio',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\_backends\\anyio.py',
   'PYMODULE'),
  ('httpcore._backends.auto',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\_backends\\auto.py',
   'PYMODULE'),
  ('httpcore._backends.base',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\_backends\\base.py',
   'PYMODULE'),
  ('httpcore._backends.mock',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\_backends\\mock.py',
   'PYMODULE'),
  ('httpcore._backends.sync',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\_backends\\sync.py',
   'PYMODULE'),
  ('httpcore._backends.trio',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\_backends\\trio.py',
   'PYMODULE'),
  ('httpcore._exceptions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\_exceptions.py',
   'PYMODULE'),
  ('httpcore._models',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\_models.py',
   'PYMODULE'),
  ('httpcore._ssl',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\_ssl.py',
   'PYMODULE'),
  ('httpcore._sync',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\_sync\\__init__.py',
   'PYMODULE'),
  ('httpcore._sync.connection',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\_sync\\connection.py',
   'PYMODULE'),
  ('httpcore._sync.connection_pool',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\_sync\\connection_pool.py',
   'PYMODULE'),
  ('httpcore._sync.http11',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\_sync\\http11.py',
   'PYMODULE'),
  ('httpcore._sync.http2',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\_sync\\http2.py',
   'PYMODULE'),
  ('httpcore._sync.http_proxy',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\_sync\\http_proxy.py',
   'PYMODULE'),
  ('httpcore._sync.interfaces',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\_sync\\interfaces.py',
   'PYMODULE'),
  ('httpcore._sync.socks_proxy',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\_sync\\socks_proxy.py',
   'PYMODULE'),
  ('httpcore._synchronization',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\_synchronization.py',
   'PYMODULE'),
  ('httpcore._trace',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\_trace.py',
   'PYMODULE'),
  ('httpcore._utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpcore\\_utils.py',
   'PYMODULE'),
  ('httptools',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httptools\\__init__.py',
   'PYMODULE'),
  ('httptools._version',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httptools\\_version.py',
   'PYMODULE'),
  ('httptools.parser',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httptools\\parser\\__init__.py',
   'PYMODULE'),
  ('httptools.parser.errors',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httptools\\parser\\errors.py',
   'PYMODULE'),
  ('httpx',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpx\\__init__.py',
   'PYMODULE'),
  ('httpx.__version__',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpx\\__version__.py',
   'PYMODULE'),
  ('httpx._api',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpx\\_api.py',
   'PYMODULE'),
  ('httpx._auth',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpx\\_auth.py',
   'PYMODULE'),
  ('httpx._client',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpx\\_client.py',
   'PYMODULE'),
  ('httpx._config',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpx\\_config.py',
   'PYMODULE'),
  ('httpx._content',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpx\\_content.py',
   'PYMODULE'),
  ('httpx._decoders',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpx\\_decoders.py',
   'PYMODULE'),
  ('httpx._exceptions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpx\\_exceptions.py',
   'PYMODULE'),
  ('httpx._main',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpx\\_main.py',
   'PYMODULE'),
  ('httpx._models',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpx\\_models.py',
   'PYMODULE'),
  ('httpx._multipart',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpx\\_multipart.py',
   'PYMODULE'),
  ('httpx._status_codes',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpx\\_status_codes.py',
   'PYMODULE'),
  ('httpx._transports',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpx\\_transports\\__init__.py',
   'PYMODULE'),
  ('httpx._transports.asgi',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpx\\_transports\\asgi.py',
   'PYMODULE'),
  ('httpx._transports.base',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpx\\_transports\\base.py',
   'PYMODULE'),
  ('httpx._transports.default',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpx\\_transports\\default.py',
   'PYMODULE'),
  ('httpx._transports.mock',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpx\\_transports\\mock.py',
   'PYMODULE'),
  ('httpx._transports.wsgi',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpx\\_transports\\wsgi.py',
   'PYMODULE'),
  ('httpx._types',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpx\\_types.py',
   'PYMODULE'),
  ('httpx._urlparse',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpx\\_urlparse.py',
   'PYMODULE'),
  ('httpx._urls',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpx\\_urls.py',
   'PYMODULE'),
  ('httpx._utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httpx\\_utils.py',
   'PYMODULE'),
  ('idna',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('iniconfig',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\iniconfig\\__init__.py',
   'PYMODULE'),
  ('iniconfig._parse',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\iniconfig\\_parse.py',
   'PYMODULE'),
  ('iniconfig.exceptions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\iniconfig\\exceptions.py',
   'PYMODULE'),
  ('inspect', 'D:\\Miniconda\\envs\\give_tag\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\Miniconda\\envs\\give_tag\\Lib\\ipaddress.py', 'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('jiter',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\jiter\\__init__.py',
   'PYMODULE'),
  ('json', 'D:\\Miniconda\\envs\\give_tag\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('jsonpatch',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\jsonpatch.py',
   'PYMODULE'),
  ('jsonpointer',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\jsonpointer.py',
   'PYMODULE'),
  ('langchain_core',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\__init__.py',
   'PYMODULE'),
  ('langchain_core._api',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\_api\\__init__.py',
   'PYMODULE'),
  ('langchain_core._api.beta_decorator',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\_api\\beta_decorator.py',
   'PYMODULE'),
  ('langchain_core._api.deprecation',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\_api\\deprecation.py',
   'PYMODULE'),
  ('langchain_core._api.internal',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\_api\\internal.py',
   'PYMODULE'),
  ('langchain_core._api.path',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\_api\\path.py',
   'PYMODULE'),
  ('langchain_core._import_utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\_import_utils.py',
   'PYMODULE'),
  ('langchain_core.agents',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\agents.py',
   'PYMODULE'),
  ('langchain_core.beta',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\beta\\__init__.py',
   'PYMODULE'),
  ('langchain_core.beta.runnables',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\beta\\runnables\\__init__.py',
   'PYMODULE'),
  ('langchain_core.beta.runnables.context',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\beta\\runnables\\context.py',
   'PYMODULE'),
  ('langchain_core.caches',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\caches.py',
   'PYMODULE'),
  ('langchain_core.callbacks',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\callbacks\\__init__.py',
   'PYMODULE'),
  ('langchain_core.callbacks.base',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\callbacks\\base.py',
   'PYMODULE'),
  ('langchain_core.callbacks.file',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\callbacks\\file.py',
   'PYMODULE'),
  ('langchain_core.callbacks.manager',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\callbacks\\manager.py',
   'PYMODULE'),
  ('langchain_core.callbacks.stdout',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\callbacks\\stdout.py',
   'PYMODULE'),
  ('langchain_core.callbacks.streaming_stdout',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\callbacks\\streaming_stdout.py',
   'PYMODULE'),
  ('langchain_core.callbacks.usage',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\callbacks\\usage.py',
   'PYMODULE'),
  ('langchain_core.chat_history',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\chat_history.py',
   'PYMODULE'),
  ('langchain_core.document_loaders',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\document_loaders\\__init__.py',
   'PYMODULE'),
  ('langchain_core.document_loaders.base',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\document_loaders\\base.py',
   'PYMODULE'),
  ('langchain_core.document_loaders.blob_loaders',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\document_loaders\\blob_loaders.py',
   'PYMODULE'),
  ('langchain_core.document_loaders.langsmith',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\document_loaders\\langsmith.py',
   'PYMODULE'),
  ('langchain_core.documents',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\documents\\__init__.py',
   'PYMODULE'),
  ('langchain_core.documents.base',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\documents\\base.py',
   'PYMODULE'),
  ('langchain_core.documents.compressor',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\documents\\compressor.py',
   'PYMODULE'),
  ('langchain_core.documents.transformers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\documents\\transformers.py',
   'PYMODULE'),
  ('langchain_core.embeddings',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\embeddings\\__init__.py',
   'PYMODULE'),
  ('langchain_core.embeddings.embeddings',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\embeddings\\embeddings.py',
   'PYMODULE'),
  ('langchain_core.embeddings.fake',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\embeddings\\fake.py',
   'PYMODULE'),
  ('langchain_core.env',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\env.py',
   'PYMODULE'),
  ('langchain_core.example_selectors',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\example_selectors\\__init__.py',
   'PYMODULE'),
  ('langchain_core.example_selectors.base',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\example_selectors\\base.py',
   'PYMODULE'),
  ('langchain_core.example_selectors.length_based',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\example_selectors\\length_based.py',
   'PYMODULE'),
  ('langchain_core.example_selectors.semantic_similarity',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\example_selectors\\semantic_similarity.py',
   'PYMODULE'),
  ('langchain_core.exceptions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\exceptions.py',
   'PYMODULE'),
  ('langchain_core.globals',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\globals.py',
   'PYMODULE'),
  ('langchain_core.indexing',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\indexing\\__init__.py',
   'PYMODULE'),
  ('langchain_core.indexing.api',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\indexing\\api.py',
   'PYMODULE'),
  ('langchain_core.indexing.base',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\indexing\\base.py',
   'PYMODULE'),
  ('langchain_core.language_models',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\language_models\\__init__.py',
   'PYMODULE'),
  ('langchain_core.language_models._utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\language_models\\_utils.py',
   'PYMODULE'),
  ('langchain_core.language_models.base',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\language_models\\base.py',
   'PYMODULE'),
  ('langchain_core.language_models.chat_models',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\language_models\\chat_models.py',
   'PYMODULE'),
  ('langchain_core.language_models.fake',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\language_models\\fake.py',
   'PYMODULE'),
  ('langchain_core.language_models.fake_chat_models',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\language_models\\fake_chat_models.py',
   'PYMODULE'),
  ('langchain_core.language_models.llms',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\language_models\\llms.py',
   'PYMODULE'),
  ('langchain_core.load',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\load\\__init__.py',
   'PYMODULE'),
  ('langchain_core.load.dump',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\load\\dump.py',
   'PYMODULE'),
  ('langchain_core.load.load',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\load\\load.py',
   'PYMODULE'),
  ('langchain_core.load.mapping',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\load\\mapping.py',
   'PYMODULE'),
  ('langchain_core.load.serializable',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\load\\serializable.py',
   'PYMODULE'),
  ('langchain_core.messages',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\messages\\__init__.py',
   'PYMODULE'),
  ('langchain_core.messages.ai',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\messages\\ai.py',
   'PYMODULE'),
  ('langchain_core.messages.base',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\messages\\base.py',
   'PYMODULE'),
  ('langchain_core.messages.chat',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\messages\\chat.py',
   'PYMODULE'),
  ('langchain_core.messages.content_blocks',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\messages\\content_blocks.py',
   'PYMODULE'),
  ('langchain_core.messages.function',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\messages\\function.py',
   'PYMODULE'),
  ('langchain_core.messages.human',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\messages\\human.py',
   'PYMODULE'),
  ('langchain_core.messages.modifier',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\messages\\modifier.py',
   'PYMODULE'),
  ('langchain_core.messages.system',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\messages\\system.py',
   'PYMODULE'),
  ('langchain_core.messages.tool',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\messages\\tool.py',
   'PYMODULE'),
  ('langchain_core.messages.utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\messages\\utils.py',
   'PYMODULE'),
  ('langchain_core.output_parsers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\output_parsers\\__init__.py',
   'PYMODULE'),
  ('langchain_core.output_parsers.base',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\output_parsers\\base.py',
   'PYMODULE'),
  ('langchain_core.output_parsers.format_instructions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\output_parsers\\format_instructions.py',
   'PYMODULE'),
  ('langchain_core.output_parsers.json',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\output_parsers\\json.py',
   'PYMODULE'),
  ('langchain_core.output_parsers.list',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\output_parsers\\list.py',
   'PYMODULE'),
  ('langchain_core.output_parsers.openai_tools',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\output_parsers\\openai_tools.py',
   'PYMODULE'),
  ('langchain_core.output_parsers.pydantic',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\output_parsers\\pydantic.py',
   'PYMODULE'),
  ('langchain_core.output_parsers.string',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\output_parsers\\string.py',
   'PYMODULE'),
  ('langchain_core.output_parsers.transform',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\output_parsers\\transform.py',
   'PYMODULE'),
  ('langchain_core.output_parsers.xml',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\output_parsers\\xml.py',
   'PYMODULE'),
  ('langchain_core.outputs',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\outputs\\__init__.py',
   'PYMODULE'),
  ('langchain_core.outputs.chat_generation',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\outputs\\chat_generation.py',
   'PYMODULE'),
  ('langchain_core.outputs.chat_result',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\outputs\\chat_result.py',
   'PYMODULE'),
  ('langchain_core.outputs.generation',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\outputs\\generation.py',
   'PYMODULE'),
  ('langchain_core.outputs.llm_result',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\outputs\\llm_result.py',
   'PYMODULE'),
  ('langchain_core.outputs.run_info',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\outputs\\run_info.py',
   'PYMODULE'),
  ('langchain_core.prompt_values',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\prompt_values.py',
   'PYMODULE'),
  ('langchain_core.prompts',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\prompts\\__init__.py',
   'PYMODULE'),
  ('langchain_core.prompts.base',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\prompts\\base.py',
   'PYMODULE'),
  ('langchain_core.prompts.chat',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\prompts\\chat.py',
   'PYMODULE'),
  ('langchain_core.prompts.dict',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\prompts\\dict.py',
   'PYMODULE'),
  ('langchain_core.prompts.few_shot',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\prompts\\few_shot.py',
   'PYMODULE'),
  ('langchain_core.prompts.few_shot_with_templates',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\prompts\\few_shot_with_templates.py',
   'PYMODULE'),
  ('langchain_core.prompts.image',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\prompts\\image.py',
   'PYMODULE'),
  ('langchain_core.prompts.loading',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\prompts\\loading.py',
   'PYMODULE'),
  ('langchain_core.prompts.message',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\prompts\\message.py',
   'PYMODULE'),
  ('langchain_core.prompts.pipeline',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\prompts\\pipeline.py',
   'PYMODULE'),
  ('langchain_core.prompts.prompt',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\prompts\\prompt.py',
   'PYMODULE'),
  ('langchain_core.prompts.string',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\prompts\\string.py',
   'PYMODULE'),
  ('langchain_core.prompts.structured',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\prompts\\structured.py',
   'PYMODULE'),
  ('langchain_core.rate_limiters',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\rate_limiters.py',
   'PYMODULE'),
  ('langchain_core.retrievers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\retrievers.py',
   'PYMODULE'),
  ('langchain_core.runnables',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\runnables\\__init__.py',
   'PYMODULE'),
  ('langchain_core.runnables.base',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\runnables\\base.py',
   'PYMODULE'),
  ('langchain_core.runnables.branch',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\runnables\\branch.py',
   'PYMODULE'),
  ('langchain_core.runnables.config',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\runnables\\config.py',
   'PYMODULE'),
  ('langchain_core.runnables.configurable',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\runnables\\configurable.py',
   'PYMODULE'),
  ('langchain_core.runnables.fallbacks',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\runnables\\fallbacks.py',
   'PYMODULE'),
  ('langchain_core.runnables.graph',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\runnables\\graph.py',
   'PYMODULE'),
  ('langchain_core.runnables.graph_ascii',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\runnables\\graph_ascii.py',
   'PYMODULE'),
  ('langchain_core.runnables.graph_mermaid',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\runnables\\graph_mermaid.py',
   'PYMODULE'),
  ('langchain_core.runnables.graph_png',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\runnables\\graph_png.py',
   'PYMODULE'),
  ('langchain_core.runnables.history',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\runnables\\history.py',
   'PYMODULE'),
  ('langchain_core.runnables.passthrough',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\runnables\\passthrough.py',
   'PYMODULE'),
  ('langchain_core.runnables.retry',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\runnables\\retry.py',
   'PYMODULE'),
  ('langchain_core.runnables.router',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\runnables\\router.py',
   'PYMODULE'),
  ('langchain_core.runnables.schema',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\runnables\\schema.py',
   'PYMODULE'),
  ('langchain_core.runnables.utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\runnables\\utils.py',
   'PYMODULE'),
  ('langchain_core.tools',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\tools\\__init__.py',
   'PYMODULE'),
  ('langchain_core.tools.base',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\tools\\base.py',
   'PYMODULE'),
  ('langchain_core.tools.convert',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\tools\\convert.py',
   'PYMODULE'),
  ('langchain_core.tools.render',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\tools\\render.py',
   'PYMODULE'),
  ('langchain_core.tools.retriever',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\tools\\retriever.py',
   'PYMODULE'),
  ('langchain_core.tools.simple',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\tools\\simple.py',
   'PYMODULE'),
  ('langchain_core.tools.structured',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\tools\\structured.py',
   'PYMODULE'),
  ('langchain_core.tracers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\tracers\\__init__.py',
   'PYMODULE'),
  ('langchain_core.tracers._streaming',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\tracers\\_streaming.py',
   'PYMODULE'),
  ('langchain_core.tracers.base',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\tracers\\base.py',
   'PYMODULE'),
  ('langchain_core.tracers.context',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\tracers\\context.py',
   'PYMODULE'),
  ('langchain_core.tracers.core',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\tracers\\core.py',
   'PYMODULE'),
  ('langchain_core.tracers.evaluation',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\tracers\\evaluation.py',
   'PYMODULE'),
  ('langchain_core.tracers.event_stream',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\tracers\\event_stream.py',
   'PYMODULE'),
  ('langchain_core.tracers.langchain',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\tracers\\langchain.py',
   'PYMODULE'),
  ('langchain_core.tracers.log_stream',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\tracers\\log_stream.py',
   'PYMODULE'),
  ('langchain_core.tracers.memory_stream',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\tracers\\memory_stream.py',
   'PYMODULE'),
  ('langchain_core.tracers.root_listeners',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\tracers\\root_listeners.py',
   'PYMODULE'),
  ('langchain_core.tracers.run_collector',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\tracers\\run_collector.py',
   'PYMODULE'),
  ('langchain_core.tracers.schemas',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\tracers\\schemas.py',
   'PYMODULE'),
  ('langchain_core.tracers.stdout',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\tracers\\stdout.py',
   'PYMODULE'),
  ('langchain_core.utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\utils\\__init__.py',
   'PYMODULE'),
  ('langchain_core.utils._merge',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\utils\\_merge.py',
   'PYMODULE'),
  ('langchain_core.utils.aiter',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\utils\\aiter.py',
   'PYMODULE'),
  ('langchain_core.utils.env',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\utils\\env.py',
   'PYMODULE'),
  ('langchain_core.utils.formatting',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\utils\\formatting.py',
   'PYMODULE'),
  ('langchain_core.utils.function_calling',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\utils\\function_calling.py',
   'PYMODULE'),
  ('langchain_core.utils.image',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\utils\\image.py',
   'PYMODULE'),
  ('langchain_core.utils.input',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\utils\\input.py',
   'PYMODULE'),
  ('langchain_core.utils.interactive_env',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\utils\\interactive_env.py',
   'PYMODULE'),
  ('langchain_core.utils.iter',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\utils\\iter.py',
   'PYMODULE'),
  ('langchain_core.utils.json',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\utils\\json.py',
   'PYMODULE'),
  ('langchain_core.utils.json_schema',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\utils\\json_schema.py',
   'PYMODULE'),
  ('langchain_core.utils.loading',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\utils\\loading.py',
   'PYMODULE'),
  ('langchain_core.utils.mustache',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\utils\\mustache.py',
   'PYMODULE'),
  ('langchain_core.utils.pydantic',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\utils\\pydantic.py',
   'PYMODULE'),
  ('langchain_core.utils.strings',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\utils\\strings.py',
   'PYMODULE'),
  ('langchain_core.utils.usage',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\utils\\usage.py',
   'PYMODULE'),
  ('langchain_core.utils.utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\utils\\utils.py',
   'PYMODULE'),
  ('langchain_core.vectorstores',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\vectorstores\\__init__.py',
   'PYMODULE'),
  ('langchain_core.vectorstores.base',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\vectorstores\\base.py',
   'PYMODULE'),
  ('langchain_core.vectorstores.in_memory',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\vectorstores\\in_memory.py',
   'PYMODULE'),
  ('langchain_core.vectorstores.utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\vectorstores\\utils.py',
   'PYMODULE'),
  ('langchain_core.version',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_core\\version.py',
   'PYMODULE'),
  ('langchain_deepseek',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_deepseek\\__init__.py',
   'PYMODULE'),
  ('langchain_deepseek.chat_models',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_deepseek\\chat_models.py',
   'PYMODULE'),
  ('langchain_openai',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_openai\\__init__.py',
   'PYMODULE'),
  ('langchain_openai.chat_models',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_openai\\chat_models\\__init__.py',
   'PYMODULE'),
  ('langchain_openai.chat_models._client_utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_openai\\chat_models\\_client_utils.py',
   'PYMODULE'),
  ('langchain_openai.chat_models._compat',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_openai\\chat_models\\_compat.py',
   'PYMODULE'),
  ('langchain_openai.chat_models.azure',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_openai\\chat_models\\azure.py',
   'PYMODULE'),
  ('langchain_openai.chat_models.base',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_openai\\chat_models\\base.py',
   'PYMODULE'),
  ('langchain_openai.embeddings',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_openai\\embeddings\\__init__.py',
   'PYMODULE'),
  ('langchain_openai.embeddings.azure',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_openai\\embeddings\\azure.py',
   'PYMODULE'),
  ('langchain_openai.embeddings.base',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_openai\\embeddings\\base.py',
   'PYMODULE'),
  ('langchain_openai.llms',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_openai\\llms\\__init__.py',
   'PYMODULE'),
  ('langchain_openai.llms.azure',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_openai\\llms\\azure.py',
   'PYMODULE'),
  ('langchain_openai.llms.base',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langchain_openai\\llms\\base.py',
   'PYMODULE'),
  ('langsmith',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\__init__.py',
   'PYMODULE'),
  ('langsmith._expect',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\_expect.py',
   'PYMODULE'),
  ('langsmith._internal',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\_internal\\__init__.py',
   'PYMODULE'),
  ('langsmith._internal._aiter',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\_internal\\_aiter.py',
   'PYMODULE'),
  ('langsmith._internal._background_thread',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\_internal\\_background_thread.py',
   'PYMODULE'),
  ('langsmith._internal._beta_decorator',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\_internal\\_beta_decorator.py',
   'PYMODULE'),
  ('langsmith._internal._compressed_traces',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\_internal\\_compressed_traces.py',
   'PYMODULE'),
  ('langsmith._internal._constants',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\_internal\\_constants.py',
   'PYMODULE'),
  ('langsmith._internal._edit_distance',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\_internal\\_edit_distance.py',
   'PYMODULE'),
  ('langsmith._internal._embedding_distance',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\_internal\\_embedding_distance.py',
   'PYMODULE'),
  ('langsmith._internal._multipart',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\_internal\\_multipart.py',
   'PYMODULE'),
  ('langsmith._internal._operations',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\_internal\\_operations.py',
   'PYMODULE'),
  ('langsmith._internal._orjson',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\_internal\\_orjson.py',
   'PYMODULE'),
  ('langsmith._internal._otel_utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\_internal\\_otel_utils.py',
   'PYMODULE'),
  ('langsmith._internal._patch',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\_internal\\_patch.py',
   'PYMODULE'),
  ('langsmith._internal._serde',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\_internal\\_serde.py',
   'PYMODULE'),
  ('langsmith._internal.otel', '-', 'PYMODULE'),
  ('langsmith._internal.otel._otel_client',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\_internal\\otel\\_otel_client.py',
   'PYMODULE'),
  ('langsmith._internal.otel._otel_exporter',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\_internal\\otel\\_otel_exporter.py',
   'PYMODULE'),
  ('langsmith.async_client',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\async_client.py',
   'PYMODULE'),
  ('langsmith.client',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\client.py',
   'PYMODULE'),
  ('langsmith.env',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\env\\__init__.py',
   'PYMODULE'),
  ('langsmith.env._git',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\env\\_git.py',
   'PYMODULE'),
  ('langsmith.env._runtime_env',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\env\\_runtime_env.py',
   'PYMODULE'),
  ('langsmith.evaluation',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\evaluation\\__init__.py',
   'PYMODULE'),
  ('langsmith.evaluation._arunner',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\evaluation\\_arunner.py',
   'PYMODULE'),
  ('langsmith.evaluation._name_generation',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\evaluation\\_name_generation.py',
   'PYMODULE'),
  ('langsmith.evaluation._runner',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\evaluation\\_runner.py',
   'PYMODULE'),
  ('langsmith.evaluation.evaluator',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\evaluation\\evaluator.py',
   'PYMODULE'),
  ('langsmith.evaluation.integrations',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\evaluation\\integrations\\__init__.py',
   'PYMODULE'),
  ('langsmith.evaluation.integrations._langchain',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\evaluation\\integrations\\_langchain.py',
   'PYMODULE'),
  ('langsmith.evaluation.string_evaluator',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\evaluation\\string_evaluator.py',
   'PYMODULE'),
  ('langsmith.run_helpers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\run_helpers.py',
   'PYMODULE'),
  ('langsmith.run_trees',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\run_trees.py',
   'PYMODULE'),
  ('langsmith.schemas',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\schemas.py',
   'PYMODULE'),
  ('langsmith.testing',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\testing\\__init__.py',
   'PYMODULE'),
  ('langsmith.testing._internal',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\testing\\_internal.py',
   'PYMODULE'),
  ('langsmith.utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\langsmith\\utils.py',
   'PYMODULE'),
  ('logging',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('logging.config',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\logging\\config.py',
   'PYMODULE'),
  ('logging.handlers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\logging\\handlers.py',
   'PYMODULE'),
  ('loguru',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\loguru\\__init__.py',
   'PYMODULE'),
  ('loguru._asyncio_loop',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\loguru\\_asyncio_loop.py',
   'PYMODULE'),
  ('loguru._better_exceptions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\loguru\\_better_exceptions.py',
   'PYMODULE'),
  ('loguru._colorama',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\loguru\\_colorama.py',
   'PYMODULE'),
  ('loguru._colorizer',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\loguru\\_colorizer.py',
   'PYMODULE'),
  ('loguru._contextvars',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\loguru\\_contextvars.py',
   'PYMODULE'),
  ('loguru._ctime_functions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\loguru\\_ctime_functions.py',
   'PYMODULE'),
  ('loguru._datetime',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\loguru\\_datetime.py',
   'PYMODULE'),
  ('loguru._defaults',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\loguru\\_defaults.py',
   'PYMODULE'),
  ('loguru._error_interceptor',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\loguru\\_error_interceptor.py',
   'PYMODULE'),
  ('loguru._file_sink',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\loguru\\_file_sink.py',
   'PYMODULE'),
  ('loguru._filters',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\loguru\\_filters.py',
   'PYMODULE'),
  ('loguru._get_frame',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\loguru\\_get_frame.py',
   'PYMODULE'),
  ('loguru._handler',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\loguru\\_handler.py',
   'PYMODULE'),
  ('loguru._locks_machinery',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\loguru\\_locks_machinery.py',
   'PYMODULE'),
  ('loguru._logger',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\loguru\\_logger.py',
   'PYMODULE'),
  ('loguru._recattrs',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\loguru\\_recattrs.py',
   'PYMODULE'),
  ('loguru._simple_sinks',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\loguru\\_simple_sinks.py',
   'PYMODULE'),
  ('loguru._string_parsers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\loguru\\_string_parsers.py',
   'PYMODULE'),
  ('lzma', 'D:\\Miniconda\\envs\\give_tag\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'D:\\Miniconda\\envs\\give_tag\\Lib\\mimetypes.py', 'PYMODULE'),
  ('multidict',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\multidict\\__init__.py',
   'PYMODULE'),
  ('multidict._abc',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\multidict\\_abc.py',
   'PYMODULE'),
  ('multidict._compat',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\multidict\\_compat.py',
   'PYMODULE'),
  ('multidict._multidict_py',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\multidict\\_multidict_py.py',
   'PYMODULE'),
  ('multipart',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\multipart\\__init__.py',
   'PYMODULE'),
  ('multipart.decoders',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\multipart\\decoders.py',
   'PYMODULE'),
  ('multipart.exceptions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\multipart\\exceptions.py',
   'PYMODULE'),
  ('multipart.multipart',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\multipart\\multipart.py',
   'PYMODULE'),
  ('multiprocessing',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\Miniconda\\envs\\give_tag\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers', 'D:\\Miniconda\\envs\\give_tag\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\Miniconda\\envs\\give_tag\\Lib\\opcode.py', 'PYMODULE'),
  ('openai',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\__init__.py',
   'PYMODULE'),
  ('openai._base_client',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\_base_client.py',
   'PYMODULE'),
  ('openai._client',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\_client.py',
   'PYMODULE'),
  ('openai._compat',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\_compat.py',
   'PYMODULE'),
  ('openai._constants',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\_constants.py',
   'PYMODULE'),
  ('openai._exceptions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\_exceptions.py',
   'PYMODULE'),
  ('openai._extras',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\_extras\\__init__.py',
   'PYMODULE'),
  ('openai._extras._common',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\_extras\\_common.py',
   'PYMODULE'),
  ('openai._extras.numpy_proxy',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\_extras\\numpy_proxy.py',
   'PYMODULE'),
  ('openai._extras.pandas_proxy',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\_extras\\pandas_proxy.py',
   'PYMODULE'),
  ('openai._extras.sounddevice_proxy',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\_extras\\sounddevice_proxy.py',
   'PYMODULE'),
  ('openai._files',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\_files.py',
   'PYMODULE'),
  ('openai._legacy_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\_legacy_response.py',
   'PYMODULE'),
  ('openai._models',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\_models.py',
   'PYMODULE'),
  ('openai._module_client',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\_module_client.py',
   'PYMODULE'),
  ('openai._qs',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\_qs.py',
   'PYMODULE'),
  ('openai._resource',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\_resource.py',
   'PYMODULE'),
  ('openai._response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\_response.py',
   'PYMODULE'),
  ('openai._streaming',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\_streaming.py',
   'PYMODULE'),
  ('openai._types',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\_types.py',
   'PYMODULE'),
  ('openai._utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\_utils\\__init__.py',
   'PYMODULE'),
  ('openai._utils._logs',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\_utils\\_logs.py',
   'PYMODULE'),
  ('openai._utils._proxy',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\_utils\\_proxy.py',
   'PYMODULE'),
  ('openai._utils._reflection',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\_utils\\_reflection.py',
   'PYMODULE'),
  ('openai._utils._resources_proxy',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\_utils\\_resources_proxy.py',
   'PYMODULE'),
  ('openai._utils._streams',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\_utils\\_streams.py',
   'PYMODULE'),
  ('openai._utils._sync',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\_utils\\_sync.py',
   'PYMODULE'),
  ('openai._utils._transform',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\_utils\\_transform.py',
   'PYMODULE'),
  ('openai._utils._typing',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\_utils\\_typing.py',
   'PYMODULE'),
  ('openai._utils._utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\_utils\\_utils.py',
   'PYMODULE'),
  ('openai._version',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\_version.py',
   'PYMODULE'),
  ('openai.lib',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\lib\\__init__.py',
   'PYMODULE'),
  ('openai.lib._old_api',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\lib\\_old_api.py',
   'PYMODULE'),
  ('openai.lib._parsing',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\lib\\_parsing\\__init__.py',
   'PYMODULE'),
  ('openai.lib._parsing._completions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\lib\\_parsing\\_completions.py',
   'PYMODULE'),
  ('openai.lib._parsing._responses',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\lib\\_parsing\\_responses.py',
   'PYMODULE'),
  ('openai.lib._pydantic',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\lib\\_pydantic.py',
   'PYMODULE'),
  ('openai.lib._tools',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\lib\\_tools.py',
   'PYMODULE'),
  ('openai.lib.azure',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\lib\\azure.py',
   'PYMODULE'),
  ('openai.lib.streaming',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\lib\\streaming\\__init__.py',
   'PYMODULE'),
  ('openai.lib.streaming._assistants',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\lib\\streaming\\_assistants.py',
   'PYMODULE'),
  ('openai.lib.streaming._deltas',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\lib\\streaming\\_deltas.py',
   'PYMODULE'),
  ('openai.lib.streaming.chat',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\lib\\streaming\\chat\\__init__.py',
   'PYMODULE'),
  ('openai.lib.streaming.chat._completions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\lib\\streaming\\chat\\_completions.py',
   'PYMODULE'),
  ('openai.lib.streaming.chat._events',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\lib\\streaming\\chat\\_events.py',
   'PYMODULE'),
  ('openai.lib.streaming.chat._types',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\lib\\streaming\\chat\\_types.py',
   'PYMODULE'),
  ('openai.lib.streaming.responses',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\lib\\streaming\\responses\\__init__.py',
   'PYMODULE'),
  ('openai.lib.streaming.responses._events',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\lib\\streaming\\responses\\_events.py',
   'PYMODULE'),
  ('openai.lib.streaming.responses._responses',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\lib\\streaming\\responses\\_responses.py',
   'PYMODULE'),
  ('openai.lib.streaming.responses._types',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\lib\\streaming\\responses\\_types.py',
   'PYMODULE'),
  ('openai.pagination',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\pagination.py',
   'PYMODULE'),
  ('openai.resources',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\__init__.py',
   'PYMODULE'),
  ('openai.resources.audio',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\audio\\__init__.py',
   'PYMODULE'),
  ('openai.resources.audio.audio',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\audio\\audio.py',
   'PYMODULE'),
  ('openai.resources.audio.speech',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\audio\\speech.py',
   'PYMODULE'),
  ('openai.resources.audio.transcriptions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\audio\\transcriptions.py',
   'PYMODULE'),
  ('openai.resources.audio.translations',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\audio\\translations.py',
   'PYMODULE'),
  ('openai.resources.batches',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\batches.py',
   'PYMODULE'),
  ('openai.resources.beta',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\beta\\__init__.py',
   'PYMODULE'),
  ('openai.resources.beta.assistants',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\beta\\assistants.py',
   'PYMODULE'),
  ('openai.resources.beta.beta',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\beta\\beta.py',
   'PYMODULE'),
  ('openai.resources.beta.realtime',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\beta\\realtime\\__init__.py',
   'PYMODULE'),
  ('openai.resources.beta.realtime.realtime',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\beta\\realtime\\realtime.py',
   'PYMODULE'),
  ('openai.resources.beta.realtime.sessions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\beta\\realtime\\sessions.py',
   'PYMODULE'),
  ('openai.resources.beta.realtime.transcription_sessions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\beta\\realtime\\transcription_sessions.py',
   'PYMODULE'),
  ('openai.resources.beta.threads',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\beta\\threads\\__init__.py',
   'PYMODULE'),
  ('openai.resources.beta.threads.messages',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\beta\\threads\\messages.py',
   'PYMODULE'),
  ('openai.resources.beta.threads.runs',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\beta\\threads\\runs\\__init__.py',
   'PYMODULE'),
  ('openai.resources.beta.threads.runs.runs',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\beta\\threads\\runs\\runs.py',
   'PYMODULE'),
  ('openai.resources.beta.threads.runs.steps',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\beta\\threads\\runs\\steps.py',
   'PYMODULE'),
  ('openai.resources.beta.threads.threads',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\beta\\threads\\threads.py',
   'PYMODULE'),
  ('openai.resources.chat',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\chat\\__init__.py',
   'PYMODULE'),
  ('openai.resources.chat.chat',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\chat\\chat.py',
   'PYMODULE'),
  ('openai.resources.chat.completions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\chat\\completions\\__init__.py',
   'PYMODULE'),
  ('openai.resources.chat.completions.completions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\chat\\completions\\completions.py',
   'PYMODULE'),
  ('openai.resources.chat.completions.messages',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\chat\\completions\\messages.py',
   'PYMODULE'),
  ('openai.resources.completions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\completions.py',
   'PYMODULE'),
  ('openai.resources.containers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\containers\\__init__.py',
   'PYMODULE'),
  ('openai.resources.containers.containers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\containers\\containers.py',
   'PYMODULE'),
  ('openai.resources.containers.files',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\containers\\files\\__init__.py',
   'PYMODULE'),
  ('openai.resources.containers.files.content',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\containers\\files\\content.py',
   'PYMODULE'),
  ('openai.resources.containers.files.files',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\containers\\files\\files.py',
   'PYMODULE'),
  ('openai.resources.embeddings',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\embeddings.py',
   'PYMODULE'),
  ('openai.resources.evals',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\evals\\__init__.py',
   'PYMODULE'),
  ('openai.resources.evals.evals',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\evals\\evals.py',
   'PYMODULE'),
  ('openai.resources.evals.runs',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\evals\\runs\\__init__.py',
   'PYMODULE'),
  ('openai.resources.evals.runs.output_items',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\evals\\runs\\output_items.py',
   'PYMODULE'),
  ('openai.resources.evals.runs.runs',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\evals\\runs\\runs.py',
   'PYMODULE'),
  ('openai.resources.files',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\files.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\fine_tuning\\__init__.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.alpha',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\fine_tuning\\alpha\\__init__.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.alpha.alpha',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\fine_tuning\\alpha\\alpha.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.alpha.graders',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\fine_tuning\\alpha\\graders.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.checkpoints',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\fine_tuning\\checkpoints\\__init__.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.checkpoints.checkpoints',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\fine_tuning\\checkpoints\\checkpoints.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.checkpoints.permissions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\fine_tuning\\checkpoints\\permissions.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.fine_tuning',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\fine_tuning\\fine_tuning.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.jobs',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\fine_tuning\\jobs\\__init__.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.jobs.checkpoints',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\fine_tuning\\jobs\\checkpoints.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.jobs.jobs',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\fine_tuning\\jobs\\jobs.py',
   'PYMODULE'),
  ('openai.resources.images',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\images.py',
   'PYMODULE'),
  ('openai.resources.models',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\models.py',
   'PYMODULE'),
  ('openai.resources.moderations',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\moderations.py',
   'PYMODULE'),
  ('openai.resources.responses',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\responses\\__init__.py',
   'PYMODULE'),
  ('openai.resources.responses.input_items',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\responses\\input_items.py',
   'PYMODULE'),
  ('openai.resources.responses.responses',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\responses\\responses.py',
   'PYMODULE'),
  ('openai.resources.uploads',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\uploads\\__init__.py',
   'PYMODULE'),
  ('openai.resources.uploads.parts',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\uploads\\parts.py',
   'PYMODULE'),
  ('openai.resources.uploads.uploads',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\uploads\\uploads.py',
   'PYMODULE'),
  ('openai.resources.vector_stores',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\vector_stores\\__init__.py',
   'PYMODULE'),
  ('openai.resources.vector_stores.file_batches',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\vector_stores\\file_batches.py',
   'PYMODULE'),
  ('openai.resources.vector_stores.files',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\vector_stores\\files.py',
   'PYMODULE'),
  ('openai.resources.vector_stores.vector_stores',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\vector_stores\\vector_stores.py',
   'PYMODULE'),
  ('openai.resources.webhooks',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\resources\\webhooks.py',
   'PYMODULE'),
  ('openai.types',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\__init__.py',
   'PYMODULE'),
  ('openai.types.audio',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\audio\\__init__.py',
   'PYMODULE'),
  ('openai.types.audio.speech_create_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\audio\\speech_create_params.py',
   'PYMODULE'),
  ('openai.types.audio.speech_model',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\audio\\speech_model.py',
   'PYMODULE'),
  ('openai.types.audio.transcription',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\audio\\transcription.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_create_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\audio\\transcription_create_params.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_create_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\audio\\transcription_create_response.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_include',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\audio\\transcription_include.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_segment',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\audio\\transcription_segment.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_stream_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\audio\\transcription_stream_event.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_text_delta_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\audio\\transcription_text_delta_event.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_text_done_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\audio\\transcription_text_done_event.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_verbose',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\audio\\transcription_verbose.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_word',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\audio\\transcription_word.py',
   'PYMODULE'),
  ('openai.types.audio.translation',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\audio\\translation.py',
   'PYMODULE'),
  ('openai.types.audio.translation_create_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\audio\\translation_create_params.py',
   'PYMODULE'),
  ('openai.types.audio.translation_create_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\audio\\translation_create_response.py',
   'PYMODULE'),
  ('openai.types.audio.translation_verbose',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\audio\\translation_verbose.py',
   'PYMODULE'),
  ('openai.types.audio_model',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\audio_model.py',
   'PYMODULE'),
  ('openai.types.audio_response_format',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\audio_response_format.py',
   'PYMODULE'),
  ('openai.types.auto_file_chunking_strategy_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\auto_file_chunking_strategy_param.py',
   'PYMODULE'),
  ('openai.types.batch',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\batch.py',
   'PYMODULE'),
  ('openai.types.batch_create_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\batch_create_params.py',
   'PYMODULE'),
  ('openai.types.batch_error',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\batch_error.py',
   'PYMODULE'),
  ('openai.types.batch_list_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\batch_list_params.py',
   'PYMODULE'),
  ('openai.types.batch_request_counts',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\batch_request_counts.py',
   'PYMODULE'),
  ('openai.types.beta',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\__init__.py',
   'PYMODULE'),
  ('openai.types.beta.assistant',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\assistant.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_create_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\assistant_create_params.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_deleted',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\assistant_deleted.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_list_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\assistant_list_params.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_response_format_option',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\assistant_response_format_option.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_response_format_option_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\assistant_response_format_option_param.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_stream_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\assistant_stream_event.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\assistant_tool.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool_choice',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\assistant_tool_choice.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool_choice_function',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\assistant_tool_choice_function.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool_choice_function_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\assistant_tool_choice_function_param.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool_choice_option',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\assistant_tool_choice_option.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool_choice_option_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\assistant_tool_choice_option_param.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool_choice_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\assistant_tool_choice_param.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\assistant_tool_param.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_update_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\assistant_update_params.py',
   'PYMODULE'),
  ('openai.types.beta.code_interpreter_tool',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\code_interpreter_tool.py',
   'PYMODULE'),
  ('openai.types.beta.code_interpreter_tool_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\code_interpreter_tool_param.py',
   'PYMODULE'),
  ('openai.types.beta.file_search_tool',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\file_search_tool.py',
   'PYMODULE'),
  ('openai.types.beta.file_search_tool_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\file_search_tool_param.py',
   'PYMODULE'),
  ('openai.types.beta.function_tool',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\function_tool.py',
   'PYMODULE'),
  ('openai.types.beta.function_tool_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\function_tool_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\__init__.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_created_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_created_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_content',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_content.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_content_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_content_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_create_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_create_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_create_event_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_create_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_created_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_created_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_delete_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_delete_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_delete_event_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_delete_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_deleted_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_deleted_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_input_audio_transcription_completed_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_input_audio_transcription_completed_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_input_audio_transcription_delta_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_input_audio_transcription_delta_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_input_audio_transcription_failed_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_input_audio_transcription_failed_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_retrieve_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_retrieve_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_retrieve_event_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_retrieve_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_truncate_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_truncate_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_truncate_event_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_truncate_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_truncated_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_truncated_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_with_reference',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_with_reference.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_with_reference_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_with_reference_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.error_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\error_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_append_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_append_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_append_event_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_append_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_clear_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_clear_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_clear_event_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_clear_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_cleared_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_cleared_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_commit_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_commit_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_commit_event_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_commit_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_committed_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_committed_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_speech_started_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_speech_started_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_speech_stopped_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_speech_stopped_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.rate_limits_updated_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\rate_limits_updated_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.realtime_client_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_client_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.realtime_client_event_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_client_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.realtime_connect_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_connect_params.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.realtime_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_response.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.realtime_response_status',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_response_status.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.realtime_response_usage',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_response_usage.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.realtime_server_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_server_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_audio_delta_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_audio_delta_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_audio_done_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_audio_done_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_audio_transcript_delta_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_audio_transcript_delta_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_audio_transcript_done_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_audio_transcript_done_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_cancel_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_cancel_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_cancel_event_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_cancel_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_content_part_added_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_content_part_added_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_content_part_done_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_content_part_done_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_create_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_create_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_create_event_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_create_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_created_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_created_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_done_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_done_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_function_call_arguments_delta_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_function_call_arguments_delta_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_function_call_arguments_done_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_function_call_arguments_done_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_output_item_added_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_output_item_added_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_output_item_done_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_output_item_done_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_text_delta_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_text_delta_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_text_done_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\response_text_done_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.session',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\session.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.session_create_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\session_create_params.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.session_create_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\session_create_response.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.session_created_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\session_created_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.session_update_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\session_update_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.session_update_event_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\session_update_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.session_updated_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\session_updated_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.transcription_session',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\transcription_session.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.transcription_session_create_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\transcription_session_create_params.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.transcription_session_update',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\transcription_session_update.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.transcription_session_update_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\transcription_session_update_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.transcription_session_updated_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\realtime\\transcription_session_updated_event.py',
   'PYMODULE'),
  ('openai.types.beta.thread',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\thread.py',
   'PYMODULE'),
  ('openai.types.beta.thread_create_and_run_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\thread_create_and_run_params.py',
   'PYMODULE'),
  ('openai.types.beta.thread_create_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\thread_create_params.py',
   'PYMODULE'),
  ('openai.types.beta.thread_deleted',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\thread_deleted.py',
   'PYMODULE'),
  ('openai.types.beta.thread_update_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\thread_update_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\__init__.py',
   'PYMODULE'),
  ('openai.types.beta.threads.annotation',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\annotation.py',
   'PYMODULE'),
  ('openai.types.beta.threads.annotation_delta',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\annotation_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.file_citation_annotation',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\file_citation_annotation.py',
   'PYMODULE'),
  ('openai.types.beta.threads.file_citation_delta_annotation',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\file_citation_delta_annotation.py',
   'PYMODULE'),
  ('openai.types.beta.threads.file_path_annotation',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\file_path_annotation.py',
   'PYMODULE'),
  ('openai.types.beta.threads.file_path_delta_annotation',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\file_path_delta_annotation.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_file',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_file.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_file_content_block',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_file_content_block.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_file_content_block_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_file_content_block_param.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_file_delta',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_file_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_file_delta_block',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_file_delta_block.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_file_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_file_param.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_url',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_url.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_url_content_block',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_url_content_block.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_url_content_block_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_url_content_block_param.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_url_delta',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_url_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_url_delta_block',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_url_delta_block.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_url_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\image_url_param.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\message.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_content',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\message_content.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_content_delta',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\message_content_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_content_part_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\message_content_part_param.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_create_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\message_create_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_deleted',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\message_deleted.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_delta',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\message_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_delta_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\message_delta_event.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_list_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\message_list_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_update_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\message_update_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.refusal_content_block',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\refusal_content_block.py',
   'PYMODULE'),
  ('openai.types.beta.threads.refusal_delta_block',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\refusal_delta_block.py',
   'PYMODULE'),
  ('openai.types.beta.threads.required_action_function_tool_call',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\required_action_function_tool_call.py',
   'PYMODULE'),
  ('openai.types.beta.threads.run',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\run.py',
   'PYMODULE'),
  ('openai.types.beta.threads.run_create_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\run_create_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.run_list_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\run_list_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.run_status',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\run_status.py',
   'PYMODULE'),
  ('openai.types.beta.threads.run_submit_tool_outputs_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\run_submit_tool_outputs_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.run_update_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\run_update_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\__init__.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.code_interpreter_logs',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\code_interpreter_logs.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.code_interpreter_output_image',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\code_interpreter_output_image.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.code_interpreter_tool_call',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\code_interpreter_tool_call.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.code_interpreter_tool_call_delta',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\code_interpreter_tool_call_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.file_search_tool_call',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\file_search_tool_call.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.file_search_tool_call_delta',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\file_search_tool_call_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.function_tool_call',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\function_tool_call.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.function_tool_call_delta',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\function_tool_call_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.message_creation_step_details',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\message_creation_step_details.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.run_step',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\run_step.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.run_step_delta',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\run_step_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.run_step_delta_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\run_step_delta_event.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.run_step_delta_message_delta',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\run_step_delta_message_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.run_step_include',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\run_step_include.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.step_list_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\step_list_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.step_retrieve_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\step_retrieve_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.tool_call',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\tool_call.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.tool_call_delta',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\tool_call_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.tool_call_delta_object',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\tool_call_delta_object.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.tool_calls_step_details',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\tool_calls_step_details.py',
   'PYMODULE'),
  ('openai.types.beta.threads.text',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\text.py',
   'PYMODULE'),
  ('openai.types.beta.threads.text_content_block',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\text_content_block.py',
   'PYMODULE'),
  ('openai.types.beta.threads.text_content_block_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\text_content_block_param.py',
   'PYMODULE'),
  ('openai.types.beta.threads.text_delta',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\text_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.text_delta_block',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\beta\\threads\\text_delta_block.py',
   'PYMODULE'),
  ('openai.types.chat',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\__init__.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_assistant_message_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_assistant_message_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_audio',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_audio.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_audio_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_audio_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_chunk',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_chunk.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_content_part_image_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_content_part_image_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_content_part_input_audio_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_content_part_input_audio_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_content_part_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_content_part_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_content_part_refusal_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_content_part_refusal_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_content_part_text_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_content_part_text_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_deleted',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_deleted.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_developer_message_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_developer_message_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_function_call_option_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_function_call_option_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_function_message_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_function_message_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_message',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_message.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_message_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_message_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_message_tool_call',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_message_tool_call.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_message_tool_call_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_message_tool_call_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_modality',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_modality.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_named_tool_choice_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_named_tool_choice_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_prediction_content_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_prediction_content_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_reasoning_effort',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_reasoning_effort.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_role',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_role.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_store_message',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_store_message.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_stream_options_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_stream_options_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_system_message_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_system_message_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_token_logprob',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_token_logprob.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_tool',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_tool.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_tool_choice_option_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_tool_choice_option_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_tool_message_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_tool_message_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_tool_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_tool_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_user_message_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\chat_completion_user_message_param.py',
   'PYMODULE'),
  ('openai.types.chat.completion_create_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\completion_create_params.py',
   'PYMODULE'),
  ('openai.types.chat.completion_list_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\completion_list_params.py',
   'PYMODULE'),
  ('openai.types.chat.completion_update_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\completion_update_params.py',
   'PYMODULE'),
  ('openai.types.chat.completions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\completions\\__init__.py',
   'PYMODULE'),
  ('openai.types.chat.completions.message_list_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\completions\\message_list_params.py',
   'PYMODULE'),
  ('openai.types.chat.parsed_chat_completion',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\parsed_chat_completion.py',
   'PYMODULE'),
  ('openai.types.chat.parsed_function_tool_call',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat\\parsed_function_tool_call.py',
   'PYMODULE'),
  ('openai.types.chat_model',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\chat_model.py',
   'PYMODULE'),
  ('openai.types.completion',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\completion.py',
   'PYMODULE'),
  ('openai.types.completion_choice',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\completion_choice.py',
   'PYMODULE'),
  ('openai.types.completion_create_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\completion_create_params.py',
   'PYMODULE'),
  ('openai.types.completion_usage',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\completion_usage.py',
   'PYMODULE'),
  ('openai.types.container_create_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\container_create_params.py',
   'PYMODULE'),
  ('openai.types.container_create_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\container_create_response.py',
   'PYMODULE'),
  ('openai.types.container_list_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\container_list_params.py',
   'PYMODULE'),
  ('openai.types.container_list_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\container_list_response.py',
   'PYMODULE'),
  ('openai.types.container_retrieve_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\container_retrieve_response.py',
   'PYMODULE'),
  ('openai.types.containers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\containers\\__init__.py',
   'PYMODULE'),
  ('openai.types.containers.file_create_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\containers\\file_create_params.py',
   'PYMODULE'),
  ('openai.types.containers.file_create_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\containers\\file_create_response.py',
   'PYMODULE'),
  ('openai.types.containers.file_list_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\containers\\file_list_params.py',
   'PYMODULE'),
  ('openai.types.containers.file_list_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\containers\\file_list_response.py',
   'PYMODULE'),
  ('openai.types.containers.file_retrieve_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\containers\\file_retrieve_response.py',
   'PYMODULE'),
  ('openai.types.create_embedding_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\create_embedding_response.py',
   'PYMODULE'),
  ('openai.types.embedding',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\embedding.py',
   'PYMODULE'),
  ('openai.types.embedding_create_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\embedding_create_params.py',
   'PYMODULE'),
  ('openai.types.embedding_model',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\embedding_model.py',
   'PYMODULE'),
  ('openai.types.eval_create_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\eval_create_params.py',
   'PYMODULE'),
  ('openai.types.eval_create_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\eval_create_response.py',
   'PYMODULE'),
  ('openai.types.eval_custom_data_source_config',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\eval_custom_data_source_config.py',
   'PYMODULE'),
  ('openai.types.eval_delete_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\eval_delete_response.py',
   'PYMODULE'),
  ('openai.types.eval_list_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\eval_list_params.py',
   'PYMODULE'),
  ('openai.types.eval_list_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\eval_list_response.py',
   'PYMODULE'),
  ('openai.types.eval_retrieve_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\eval_retrieve_response.py',
   'PYMODULE'),
  ('openai.types.eval_stored_completions_data_source_config',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\eval_stored_completions_data_source_config.py',
   'PYMODULE'),
  ('openai.types.eval_update_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\eval_update_params.py',
   'PYMODULE'),
  ('openai.types.eval_update_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\eval_update_response.py',
   'PYMODULE'),
  ('openai.types.evals',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\evals\\__init__.py',
   'PYMODULE'),
  ('openai.types.evals.create_eval_completions_run_data_source',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\evals\\create_eval_completions_run_data_source.py',
   'PYMODULE'),
  ('openai.types.evals.create_eval_completions_run_data_source_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\evals\\create_eval_completions_run_data_source_param.py',
   'PYMODULE'),
  ('openai.types.evals.create_eval_jsonl_run_data_source',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\evals\\create_eval_jsonl_run_data_source.py',
   'PYMODULE'),
  ('openai.types.evals.create_eval_jsonl_run_data_source_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\evals\\create_eval_jsonl_run_data_source_param.py',
   'PYMODULE'),
  ('openai.types.evals.eval_api_error',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\evals\\eval_api_error.py',
   'PYMODULE'),
  ('openai.types.evals.run_cancel_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\evals\\run_cancel_response.py',
   'PYMODULE'),
  ('openai.types.evals.run_create_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\evals\\run_create_params.py',
   'PYMODULE'),
  ('openai.types.evals.run_create_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\evals\\run_create_response.py',
   'PYMODULE'),
  ('openai.types.evals.run_delete_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\evals\\run_delete_response.py',
   'PYMODULE'),
  ('openai.types.evals.run_list_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\evals\\run_list_params.py',
   'PYMODULE'),
  ('openai.types.evals.run_list_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\evals\\run_list_response.py',
   'PYMODULE'),
  ('openai.types.evals.run_retrieve_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\evals\\run_retrieve_response.py',
   'PYMODULE'),
  ('openai.types.evals.runs',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\evals\\runs\\__init__.py',
   'PYMODULE'),
  ('openai.types.evals.runs.output_item_list_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\evals\\runs\\output_item_list_params.py',
   'PYMODULE'),
  ('openai.types.evals.runs.output_item_list_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\evals\\runs\\output_item_list_response.py',
   'PYMODULE'),
  ('openai.types.evals.runs.output_item_retrieve_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\evals\\runs\\output_item_retrieve_response.py',
   'PYMODULE'),
  ('openai.types.file_chunking_strategy',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\file_chunking_strategy.py',
   'PYMODULE'),
  ('openai.types.file_chunking_strategy_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\file_chunking_strategy_param.py',
   'PYMODULE'),
  ('openai.types.file_content',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\file_content.py',
   'PYMODULE'),
  ('openai.types.file_create_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\file_create_params.py',
   'PYMODULE'),
  ('openai.types.file_deleted',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\file_deleted.py',
   'PYMODULE'),
  ('openai.types.file_list_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\file_list_params.py',
   'PYMODULE'),
  ('openai.types.file_object',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\file_object.py',
   'PYMODULE'),
  ('openai.types.file_purpose',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\file_purpose.py',
   'PYMODULE'),
  ('openai.types.fine_tuning',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\__init__.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.alpha',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\alpha\\__init__.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.alpha.grader_run_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\alpha\\grader_run_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.alpha.grader_run_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\alpha\\grader_run_response.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.alpha.grader_validate_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\alpha\\grader_validate_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.alpha.grader_validate_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\alpha\\grader_validate_response.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.checkpoints',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\checkpoints\\__init__.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.checkpoints.permission_create_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\checkpoints\\permission_create_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.checkpoints.permission_create_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\checkpoints\\permission_create_response.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.checkpoints.permission_delete_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\checkpoints\\permission_delete_response.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.checkpoints.permission_retrieve_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\checkpoints\\permission_retrieve_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.checkpoints.permission_retrieve_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\checkpoints\\permission_retrieve_response.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.dpo_hyperparameters',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\dpo_hyperparameters.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.dpo_hyperparameters_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\dpo_hyperparameters_param.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.dpo_method',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\dpo_method.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.dpo_method_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\dpo_method_param.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.fine_tuning_job',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\fine_tuning_job.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.fine_tuning_job_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\fine_tuning_job_event.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.fine_tuning_job_integration',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\fine_tuning_job_integration.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.fine_tuning_job_wandb_integration',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\fine_tuning_job_wandb_integration.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.fine_tuning_job_wandb_integration_object',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\fine_tuning_job_wandb_integration_object.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.job_create_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\job_create_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.job_list_events_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\job_list_events_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.job_list_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\job_list_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.jobs',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\jobs\\__init__.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.jobs.checkpoint_list_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\jobs\\checkpoint_list_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.jobs.fine_tuning_job_checkpoint',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\jobs\\fine_tuning_job_checkpoint.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.reinforcement_hyperparameters',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\reinforcement_hyperparameters.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.reinforcement_hyperparameters_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\reinforcement_hyperparameters_param.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.reinforcement_method',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\reinforcement_method.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.reinforcement_method_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\reinforcement_method_param.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.supervised_hyperparameters',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\supervised_hyperparameters.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.supervised_hyperparameters_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\supervised_hyperparameters_param.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.supervised_method',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\supervised_method.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.supervised_method_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\fine_tuning\\supervised_method_param.py',
   'PYMODULE'),
  ('openai.types.graders',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\graders\\__init__.py',
   'PYMODULE'),
  ('openai.types.graders.label_model_grader',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\graders\\label_model_grader.py',
   'PYMODULE'),
  ('openai.types.graders.label_model_grader_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\graders\\label_model_grader_param.py',
   'PYMODULE'),
  ('openai.types.graders.multi_grader',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\graders\\multi_grader.py',
   'PYMODULE'),
  ('openai.types.graders.multi_grader_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\graders\\multi_grader_param.py',
   'PYMODULE'),
  ('openai.types.graders.python_grader',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\graders\\python_grader.py',
   'PYMODULE'),
  ('openai.types.graders.python_grader_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\graders\\python_grader_param.py',
   'PYMODULE'),
  ('openai.types.graders.score_model_grader',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\graders\\score_model_grader.py',
   'PYMODULE'),
  ('openai.types.graders.score_model_grader_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\graders\\score_model_grader_param.py',
   'PYMODULE'),
  ('openai.types.graders.string_check_grader',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\graders\\string_check_grader.py',
   'PYMODULE'),
  ('openai.types.graders.string_check_grader_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\graders\\string_check_grader_param.py',
   'PYMODULE'),
  ('openai.types.graders.text_similarity_grader',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\graders\\text_similarity_grader.py',
   'PYMODULE'),
  ('openai.types.graders.text_similarity_grader_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\graders\\text_similarity_grader_param.py',
   'PYMODULE'),
  ('openai.types.image',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\image.py',
   'PYMODULE'),
  ('openai.types.image_create_variation_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\image_create_variation_params.py',
   'PYMODULE'),
  ('openai.types.image_edit_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\image_edit_params.py',
   'PYMODULE'),
  ('openai.types.image_generate_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\image_generate_params.py',
   'PYMODULE'),
  ('openai.types.image_model',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\image_model.py',
   'PYMODULE'),
  ('openai.types.images_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\images_response.py',
   'PYMODULE'),
  ('openai.types.model',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\model.py',
   'PYMODULE'),
  ('openai.types.model_deleted',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\model_deleted.py',
   'PYMODULE'),
  ('openai.types.moderation',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\moderation.py',
   'PYMODULE'),
  ('openai.types.moderation_create_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\moderation_create_params.py',
   'PYMODULE'),
  ('openai.types.moderation_create_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\moderation_create_response.py',
   'PYMODULE'),
  ('openai.types.moderation_image_url_input_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\moderation_image_url_input_param.py',
   'PYMODULE'),
  ('openai.types.moderation_model',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\moderation_model.py',
   'PYMODULE'),
  ('openai.types.moderation_multi_modal_input_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\moderation_multi_modal_input_param.py',
   'PYMODULE'),
  ('openai.types.moderation_text_input_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\moderation_text_input_param.py',
   'PYMODULE'),
  ('openai.types.other_file_chunking_strategy_object',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\other_file_chunking_strategy_object.py',
   'PYMODULE'),
  ('openai.types.responses',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\__init__.py',
   'PYMODULE'),
  ('openai.types.responses.computer_tool',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\computer_tool.py',
   'PYMODULE'),
  ('openai.types.responses.computer_tool_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\computer_tool_param.py',
   'PYMODULE'),
  ('openai.types.responses.easy_input_message',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\easy_input_message.py',
   'PYMODULE'),
  ('openai.types.responses.easy_input_message_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\easy_input_message_param.py',
   'PYMODULE'),
  ('openai.types.responses.file_search_tool',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\file_search_tool.py',
   'PYMODULE'),
  ('openai.types.responses.file_search_tool_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\file_search_tool_param.py',
   'PYMODULE'),
  ('openai.types.responses.function_tool',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\function_tool.py',
   'PYMODULE'),
  ('openai.types.responses.function_tool_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\function_tool_param.py',
   'PYMODULE'),
  ('openai.types.responses.input_item_list_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\input_item_list_params.py',
   'PYMODULE'),
  ('openai.types.responses.parsed_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\parsed_response.py',
   'PYMODULE'),
  ('openai.types.responses.response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response.py',
   'PYMODULE'),
  ('openai.types.responses.response_audio_delta_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_audio_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_audio_done_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_audio_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_audio_transcript_delta_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_audio_transcript_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_audio_transcript_done_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_audio_transcript_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_code_interpreter_call_code_delta_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_code_interpreter_call_code_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_code_interpreter_call_code_done_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_code_interpreter_call_code_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_code_interpreter_call_completed_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_code_interpreter_call_completed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_code_interpreter_call_in_progress_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_code_interpreter_call_in_progress_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_code_interpreter_call_interpreting_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_code_interpreter_call_interpreting_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_code_interpreter_tool_call',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_code_interpreter_tool_call.py',
   'PYMODULE'),
  ('openai.types.responses.response_code_interpreter_tool_call_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_code_interpreter_tool_call_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_completed_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_completed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_computer_tool_call',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_computer_tool_call.py',
   'PYMODULE'),
  ('openai.types.responses.response_computer_tool_call_output_item',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_computer_tool_call_output_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_computer_tool_call_output_screenshot',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_computer_tool_call_output_screenshot.py',
   'PYMODULE'),
  ('openai.types.responses.response_computer_tool_call_output_screenshot_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_computer_tool_call_output_screenshot_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_computer_tool_call_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_computer_tool_call_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_content_part_added_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_content_part_added_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_content_part_done_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_content_part_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_create_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_create_params.py',
   'PYMODULE'),
  ('openai.types.responses.response_created_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_created_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_error',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_error.py',
   'PYMODULE'),
  ('openai.types.responses.response_error_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_error_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_failed_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_failed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_file_search_call_completed_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_file_search_call_completed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_file_search_call_in_progress_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_file_search_call_in_progress_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_file_search_call_searching_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_file_search_call_searching_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_file_search_tool_call',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_file_search_tool_call.py',
   'PYMODULE'),
  ('openai.types.responses.response_file_search_tool_call_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_file_search_tool_call_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_format_text_config',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_format_text_config.py',
   'PYMODULE'),
  ('openai.types.responses.response_format_text_config_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_format_text_config_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_format_text_json_schema_config',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_format_text_json_schema_config.py',
   'PYMODULE'),
  ('openai.types.responses.response_format_text_json_schema_config_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_format_text_json_schema_config_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_call_arguments_delta_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_function_call_arguments_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_call_arguments_done_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_function_call_arguments_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_tool_call',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_function_tool_call.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_tool_call_item',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_function_tool_call_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_tool_call_output_item',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_function_tool_call_output_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_tool_call_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_function_tool_call_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_web_search',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_function_web_search.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_web_search_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_function_web_search_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_image_gen_call_completed_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_image_gen_call_completed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_image_gen_call_generating_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_image_gen_call_generating_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_image_gen_call_in_progress_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_image_gen_call_in_progress_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_image_gen_call_partial_image_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_image_gen_call_partial_image_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_in_progress_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_in_progress_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_includable',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_includable.py',
   'PYMODULE'),
  ('openai.types.responses.response_incomplete_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_incomplete_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_content',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_input_content.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_content_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_input_content_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_file',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_input_file.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_file_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_input_file_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_image',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_input_image.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_image_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_input_image_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_item',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_input_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_item_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_input_item_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_message_content_list',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_input_message_content_list.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_message_content_list_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_input_message_content_list_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_message_item',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_input_message_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_input_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_text',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_input_text.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_text_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_input_text_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_item',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_item_list',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_item_list.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_call_arguments_delta_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_mcp_call_arguments_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_call_arguments_done_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_mcp_call_arguments_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_call_completed_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_mcp_call_completed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_call_failed_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_mcp_call_failed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_call_in_progress_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_mcp_call_in_progress_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_list_tools_completed_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_mcp_list_tools_completed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_list_tools_failed_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_mcp_list_tools_failed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_list_tools_in_progress_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_mcp_list_tools_in_progress_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_item',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_output_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_item_added_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_output_item_added_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_item_done_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_output_item_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_message',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_output_message.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_message_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_output_message_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_refusal',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_output_refusal.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_refusal_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_output_refusal_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_text',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_output_text.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_text_annotation_added_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_output_text_annotation_added_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_text_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_output_text_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_prompt',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_prompt.py',
   'PYMODULE'),
  ('openai.types.responses.response_prompt_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_prompt_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_queued_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_queued_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_delta_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_reasoning_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_done_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_reasoning_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_item',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_reasoning_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_item_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_reasoning_item_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_summary_delta_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_reasoning_summary_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_summary_done_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_reasoning_summary_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_summary_part_added_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_reasoning_summary_part_added_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_summary_part_done_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_reasoning_summary_part_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_summary_text_delta_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_reasoning_summary_text_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_summary_text_done_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_reasoning_summary_text_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_refusal_delta_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_refusal_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_refusal_done_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_refusal_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_retrieve_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_retrieve_params.py',
   'PYMODULE'),
  ('openai.types.responses.response_status',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_status.py',
   'PYMODULE'),
  ('openai.types.responses.response_stream_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_stream_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_text_config',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_text_config.py',
   'PYMODULE'),
  ('openai.types.responses.response_text_config_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_text_config_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_text_delta_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_text_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_text_done_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_text_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_usage',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_usage.py',
   'PYMODULE'),
  ('openai.types.responses.response_web_search_call_completed_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_web_search_call_completed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_web_search_call_in_progress_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_web_search_call_in_progress_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_web_search_call_searching_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\response_web_search_call_searching_event.py',
   'PYMODULE'),
  ('openai.types.responses.tool',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\tool.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_function',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\tool_choice_function.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_function_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\tool_choice_function_param.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_mcp',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\tool_choice_mcp.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_mcp_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\tool_choice_mcp_param.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_options',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\tool_choice_options.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_types',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\tool_choice_types.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_types_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\tool_choice_types_param.py',
   'PYMODULE'),
  ('openai.types.responses.tool_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\tool_param.py',
   'PYMODULE'),
  ('openai.types.responses.web_search_tool',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\web_search_tool.py',
   'PYMODULE'),
  ('openai.types.responses.web_search_tool_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\responses\\web_search_tool_param.py',
   'PYMODULE'),
  ('openai.types.shared',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\shared\\__init__.py',
   'PYMODULE'),
  ('openai.types.shared.all_models',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\shared\\all_models.py',
   'PYMODULE'),
  ('openai.types.shared.chat_model',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\shared\\chat_model.py',
   'PYMODULE'),
  ('openai.types.shared.comparison_filter',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\shared\\comparison_filter.py',
   'PYMODULE'),
  ('openai.types.shared.compound_filter',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\shared\\compound_filter.py',
   'PYMODULE'),
  ('openai.types.shared.error_object',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\shared\\error_object.py',
   'PYMODULE'),
  ('openai.types.shared.function_definition',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\shared\\function_definition.py',
   'PYMODULE'),
  ('openai.types.shared.function_parameters',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\shared\\function_parameters.py',
   'PYMODULE'),
  ('openai.types.shared.metadata',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\shared\\metadata.py',
   'PYMODULE'),
  ('openai.types.shared.reasoning',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\shared\\reasoning.py',
   'PYMODULE'),
  ('openai.types.shared.reasoning_effort',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\shared\\reasoning_effort.py',
   'PYMODULE'),
  ('openai.types.shared.response_format_json_object',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\shared\\response_format_json_object.py',
   'PYMODULE'),
  ('openai.types.shared.response_format_json_schema',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\shared\\response_format_json_schema.py',
   'PYMODULE'),
  ('openai.types.shared.response_format_text',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\shared\\response_format_text.py',
   'PYMODULE'),
  ('openai.types.shared.responses_model',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\shared\\responses_model.py',
   'PYMODULE'),
  ('openai.types.shared_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\shared_params\\__init__.py',
   'PYMODULE'),
  ('openai.types.shared_params.chat_model',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\shared_params\\chat_model.py',
   'PYMODULE'),
  ('openai.types.shared_params.comparison_filter',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\shared_params\\comparison_filter.py',
   'PYMODULE'),
  ('openai.types.shared_params.compound_filter',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\shared_params\\compound_filter.py',
   'PYMODULE'),
  ('openai.types.shared_params.function_definition',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\shared_params\\function_definition.py',
   'PYMODULE'),
  ('openai.types.shared_params.function_parameters',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\shared_params\\function_parameters.py',
   'PYMODULE'),
  ('openai.types.shared_params.metadata',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\shared_params\\metadata.py',
   'PYMODULE'),
  ('openai.types.shared_params.reasoning',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\shared_params\\reasoning.py',
   'PYMODULE'),
  ('openai.types.shared_params.reasoning_effort',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\shared_params\\reasoning_effort.py',
   'PYMODULE'),
  ('openai.types.shared_params.response_format_json_object',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\shared_params\\response_format_json_object.py',
   'PYMODULE'),
  ('openai.types.shared_params.response_format_json_schema',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\shared_params\\response_format_json_schema.py',
   'PYMODULE'),
  ('openai.types.shared_params.response_format_text',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\shared_params\\response_format_text.py',
   'PYMODULE'),
  ('openai.types.shared_params.responses_model',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\shared_params\\responses_model.py',
   'PYMODULE'),
  ('openai.types.static_file_chunking_strategy',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\static_file_chunking_strategy.py',
   'PYMODULE'),
  ('openai.types.static_file_chunking_strategy_object',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\static_file_chunking_strategy_object.py',
   'PYMODULE'),
  ('openai.types.static_file_chunking_strategy_object_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\static_file_chunking_strategy_object_param.py',
   'PYMODULE'),
  ('openai.types.static_file_chunking_strategy_param',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\static_file_chunking_strategy_param.py',
   'PYMODULE'),
  ('openai.types.upload',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\upload.py',
   'PYMODULE'),
  ('openai.types.upload_complete_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\upload_complete_params.py',
   'PYMODULE'),
  ('openai.types.upload_create_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\upload_create_params.py',
   'PYMODULE'),
  ('openai.types.uploads',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\uploads\\__init__.py',
   'PYMODULE'),
  ('openai.types.uploads.part_create_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\uploads\\part_create_params.py',
   'PYMODULE'),
  ('openai.types.uploads.upload_part',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\uploads\\upload_part.py',
   'PYMODULE'),
  ('openai.types.vector_store',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\vector_store.py',
   'PYMODULE'),
  ('openai.types.vector_store_create_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\vector_store_create_params.py',
   'PYMODULE'),
  ('openai.types.vector_store_deleted',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\vector_store_deleted.py',
   'PYMODULE'),
  ('openai.types.vector_store_list_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\vector_store_list_params.py',
   'PYMODULE'),
  ('openai.types.vector_store_search_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\vector_store_search_params.py',
   'PYMODULE'),
  ('openai.types.vector_store_search_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\vector_store_search_response.py',
   'PYMODULE'),
  ('openai.types.vector_store_update_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\vector_store_update_params.py',
   'PYMODULE'),
  ('openai.types.vector_stores',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\vector_stores\\__init__.py',
   'PYMODULE'),
  ('openai.types.vector_stores.file_batch_create_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\vector_stores\\file_batch_create_params.py',
   'PYMODULE'),
  ('openai.types.vector_stores.file_batch_list_files_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\vector_stores\\file_batch_list_files_params.py',
   'PYMODULE'),
  ('openai.types.vector_stores.file_content_response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\vector_stores\\file_content_response.py',
   'PYMODULE'),
  ('openai.types.vector_stores.file_create_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\vector_stores\\file_create_params.py',
   'PYMODULE'),
  ('openai.types.vector_stores.file_list_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\vector_stores\\file_list_params.py',
   'PYMODULE'),
  ('openai.types.vector_stores.file_update_params',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\vector_stores\\file_update_params.py',
   'PYMODULE'),
  ('openai.types.vector_stores.vector_store_file',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\vector_stores\\vector_store_file.py',
   'PYMODULE'),
  ('openai.types.vector_stores.vector_store_file_batch',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\vector_stores\\vector_store_file_batch.py',
   'PYMODULE'),
  ('openai.types.vector_stores.vector_store_file_deleted',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\vector_stores\\vector_store_file_deleted.py',
   'PYMODULE'),
  ('openai.types.webhooks',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\webhooks\\__init__.py',
   'PYMODULE'),
  ('openai.types.webhooks.batch_cancelled_webhook_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\webhooks\\batch_cancelled_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.batch_completed_webhook_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\webhooks\\batch_completed_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.batch_expired_webhook_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\webhooks\\batch_expired_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.batch_failed_webhook_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\webhooks\\batch_failed_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.eval_run_canceled_webhook_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\webhooks\\eval_run_canceled_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.eval_run_failed_webhook_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\webhooks\\eval_run_failed_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.eval_run_succeeded_webhook_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\webhooks\\eval_run_succeeded_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.fine_tuning_job_cancelled_webhook_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\webhooks\\fine_tuning_job_cancelled_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.fine_tuning_job_failed_webhook_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\webhooks\\fine_tuning_job_failed_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.fine_tuning_job_succeeded_webhook_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\webhooks\\fine_tuning_job_succeeded_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.response_cancelled_webhook_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\webhooks\\response_cancelled_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.response_completed_webhook_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\webhooks\\response_completed_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.response_failed_webhook_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\webhooks\\response_failed_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.response_incomplete_webhook_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\webhooks\\response_incomplete_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.unwrap_webhook_event',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\webhooks\\unwrap_webhook_event.py',
   'PYMODULE'),
  ('openai.types.websocket_connection_options',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\types\\websocket_connection_options.py',
   'PYMODULE'),
  ('openai.version',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\openai\\version.py',
   'PYMODULE'),
  ('orjson',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\orjson\\__init__.py',
   'PYMODULE'),
  ('packaging',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib', 'D:\\Miniconda\\envs\\give_tag\\Lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'D:\\Miniconda\\envs\\give_tag\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'D:\\Miniconda\\envs\\give_tag\\Lib\\pickle.py', 'PYMODULE'),
  ('pkg_resources',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'D:\\Miniconda\\envs\\give_tag\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\Miniconda\\envs\\give_tag\\Lib\\platform.py', 'PYMODULE'),
  ('plistlib', 'D:\\Miniconda\\envs\\give_tag\\Lib\\plistlib.py', 'PYMODULE'),
  ('pluggy',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pluggy\\__init__.py',
   'PYMODULE'),
  ('pluggy._callers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pluggy\\_callers.py',
   'PYMODULE'),
  ('pluggy._hooks',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pluggy\\_hooks.py',
   'PYMODULE'),
  ('pluggy._manager',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pluggy\\_manager.py',
   'PYMODULE'),
  ('pluggy._result',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pluggy\\_result.py',
   'PYMODULE'),
  ('pluggy._tracing',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pluggy\\_tracing.py',
   'PYMODULE'),
  ('pluggy._version',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pluggy\\_version.py',
   'PYMODULE'),
  ('pluggy._warnings',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pluggy\\_warnings.py',
   'PYMODULE'),
  ('pprint', 'D:\\Miniconda\\envs\\give_tag\\Lib\\pprint.py', 'PYMODULE'),
  ('propcache',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\propcache\\__init__.py',
   'PYMODULE'),
  ('propcache._helpers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\propcache\\_helpers.py',
   'PYMODULE'),
  ('propcache._helpers_py',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\propcache\\_helpers_py.py',
   'PYMODULE'),
  ('propcache.api',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\propcache\\api.py',
   'PYMODULE'),
  ('py',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\py.py',
   'PYMODULE'),
  ('py_compile',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pydantic',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\__init__.py',
   'PYMODULE'),
  ('pydantic._internal',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\_internal\\__init__.py',
   'PYMODULE'),
  ('pydantic._internal._config',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\_internal\\_config.py',
   'PYMODULE'),
  ('pydantic._internal._core_metadata',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\_internal\\_core_metadata.py',
   'PYMODULE'),
  ('pydantic._internal._core_utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\_internal\\_core_utils.py',
   'PYMODULE'),
  ('pydantic._internal._dataclasses',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\_internal\\_dataclasses.py',
   'PYMODULE'),
  ('pydantic._internal._decorators',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\_internal\\_decorators.py',
   'PYMODULE'),
  ('pydantic._internal._decorators_v1',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\_internal\\_decorators_v1.py',
   'PYMODULE'),
  ('pydantic._internal._discriminated_union',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\_internal\\_discriminated_union.py',
   'PYMODULE'),
  ('pydantic._internal._docs_extraction',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\_internal\\_docs_extraction.py',
   'PYMODULE'),
  ('pydantic._internal._fields',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\_internal\\_fields.py',
   'PYMODULE'),
  ('pydantic._internal._forward_ref',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\_internal\\_forward_ref.py',
   'PYMODULE'),
  ('pydantic._internal._generate_schema',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\_internal\\_generate_schema.py',
   'PYMODULE'),
  ('pydantic._internal._generics',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\_internal\\_generics.py',
   'PYMODULE'),
  ('pydantic._internal._git',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\_internal\\_git.py',
   'PYMODULE'),
  ('pydantic._internal._import_utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\_internal\\_import_utils.py',
   'PYMODULE'),
  ('pydantic._internal._internal_dataclass',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\_internal\\_internal_dataclass.py',
   'PYMODULE'),
  ('pydantic._internal._known_annotated_metadata',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\_internal\\_known_annotated_metadata.py',
   'PYMODULE'),
  ('pydantic._internal._mock_val_ser',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\_internal\\_mock_val_ser.py',
   'PYMODULE'),
  ('pydantic._internal._model_construction',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\_internal\\_model_construction.py',
   'PYMODULE'),
  ('pydantic._internal._namespace_utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\_internal\\_namespace_utils.py',
   'PYMODULE'),
  ('pydantic._internal._repr',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\_internal\\_repr.py',
   'PYMODULE'),
  ('pydantic._internal._schema_gather',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\_internal\\_schema_gather.py',
   'PYMODULE'),
  ('pydantic._internal._schema_generation_shared',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\_internal\\_schema_generation_shared.py',
   'PYMODULE'),
  ('pydantic._internal._serializers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\_internal\\_serializers.py',
   'PYMODULE'),
  ('pydantic._internal._signature',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\_internal\\_signature.py',
   'PYMODULE'),
  ('pydantic._internal._typing_extra',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\_internal\\_typing_extra.py',
   'PYMODULE'),
  ('pydantic._internal._utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\_internal\\_utils.py',
   'PYMODULE'),
  ('pydantic._internal._validate_call',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\_internal\\_validate_call.py',
   'PYMODULE'),
  ('pydantic._internal._validators',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\_internal\\_validators.py',
   'PYMODULE'),
  ('pydantic._migration',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\_migration.py',
   'PYMODULE'),
  ('pydantic.alias_generators',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\alias_generators.py',
   'PYMODULE'),
  ('pydantic.aliases',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\aliases.py',
   'PYMODULE'),
  ('pydantic.annotated_handlers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\annotated_handlers.py',
   'PYMODULE'),
  ('pydantic.class_validators',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\class_validators.py',
   'PYMODULE'),
  ('pydantic.color',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\color.py',
   'PYMODULE'),
  ('pydantic.config',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\config.py',
   'PYMODULE'),
  ('pydantic.dataclasses',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\dataclasses.py',
   'PYMODULE'),
  ('pydantic.datetime_parse',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\datetime_parse.py',
   'PYMODULE'),
  ('pydantic.decorator',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\decorator.py',
   'PYMODULE'),
  ('pydantic.deprecated',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\deprecated\\__init__.py',
   'PYMODULE'),
  ('pydantic.deprecated.class_validators',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\deprecated\\class_validators.py',
   'PYMODULE'),
  ('pydantic.deprecated.config',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\deprecated\\config.py',
   'PYMODULE'),
  ('pydantic.deprecated.copy_internals',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\deprecated\\copy_internals.py',
   'PYMODULE'),
  ('pydantic.deprecated.decorator',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\deprecated\\decorator.py',
   'PYMODULE'),
  ('pydantic.deprecated.json',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\deprecated\\json.py',
   'PYMODULE'),
  ('pydantic.deprecated.parse',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\deprecated\\parse.py',
   'PYMODULE'),
  ('pydantic.deprecated.tools',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\deprecated\\tools.py',
   'PYMODULE'),
  ('pydantic.env_settings',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\env_settings.py',
   'PYMODULE'),
  ('pydantic.error_wrappers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\error_wrappers.py',
   'PYMODULE'),
  ('pydantic.errors',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\errors.py',
   'PYMODULE'),
  ('pydantic.experimental',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\experimental\\__init__.py',
   'PYMODULE'),
  ('pydantic.experimental.arguments_schema',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\experimental\\arguments_schema.py',
   'PYMODULE'),
  ('pydantic.experimental.pipeline',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\experimental\\pipeline.py',
   'PYMODULE'),
  ('pydantic.fields',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\fields.py',
   'PYMODULE'),
  ('pydantic.functional_serializers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\functional_serializers.py',
   'PYMODULE'),
  ('pydantic.functional_validators',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\functional_validators.py',
   'PYMODULE'),
  ('pydantic.generics',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\generics.py',
   'PYMODULE'),
  ('pydantic.json',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\json.py',
   'PYMODULE'),
  ('pydantic.json_schema',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\json_schema.py',
   'PYMODULE'),
  ('pydantic.main',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\main.py',
   'PYMODULE'),
  ('pydantic.mypy',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\mypy.py',
   'PYMODULE'),
  ('pydantic.networks',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\networks.py',
   'PYMODULE'),
  ('pydantic.parse',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\parse.py',
   'PYMODULE'),
  ('pydantic.plugin',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\plugin\\__init__.py',
   'PYMODULE'),
  ('pydantic.plugin._loader',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\plugin\\_loader.py',
   'PYMODULE'),
  ('pydantic.plugin._schema_validator',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\plugin\\_schema_validator.py',
   'PYMODULE'),
  ('pydantic.root_model',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\root_model.py',
   'PYMODULE'),
  ('pydantic.schema',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\schema.py',
   'PYMODULE'),
  ('pydantic.tools',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\tools.py',
   'PYMODULE'),
  ('pydantic.type_adapter',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\type_adapter.py',
   'PYMODULE'),
  ('pydantic.types',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\types.py',
   'PYMODULE'),
  ('pydantic.typing',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\typing.py',
   'PYMODULE'),
  ('pydantic.utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\utils.py',
   'PYMODULE'),
  ('pydantic.v1',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\v1\\__init__.py',
   'PYMODULE'),
  ('pydantic.v1._hypothesis_plugin',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\v1\\_hypothesis_plugin.py',
   'PYMODULE'),
  ('pydantic.v1.annotated_types',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\v1\\annotated_types.py',
   'PYMODULE'),
  ('pydantic.v1.class_validators',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\v1\\class_validators.py',
   'PYMODULE'),
  ('pydantic.v1.color',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\v1\\color.py',
   'PYMODULE'),
  ('pydantic.v1.config',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\v1\\config.py',
   'PYMODULE'),
  ('pydantic.v1.dataclasses',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\v1\\dataclasses.py',
   'PYMODULE'),
  ('pydantic.v1.datetime_parse',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\v1\\datetime_parse.py',
   'PYMODULE'),
  ('pydantic.v1.decorator',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\v1\\decorator.py',
   'PYMODULE'),
  ('pydantic.v1.env_settings',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\v1\\env_settings.py',
   'PYMODULE'),
  ('pydantic.v1.error_wrappers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\v1\\error_wrappers.py',
   'PYMODULE'),
  ('pydantic.v1.errors',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\v1\\errors.py',
   'PYMODULE'),
  ('pydantic.v1.fields',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\v1\\fields.py',
   'PYMODULE'),
  ('pydantic.v1.generics',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\v1\\generics.py',
   'PYMODULE'),
  ('pydantic.v1.json',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\v1\\json.py',
   'PYMODULE'),
  ('pydantic.v1.main',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\v1\\main.py',
   'PYMODULE'),
  ('pydantic.v1.mypy',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\v1\\mypy.py',
   'PYMODULE'),
  ('pydantic.v1.networks',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\v1\\networks.py',
   'PYMODULE'),
  ('pydantic.v1.parse',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\v1\\parse.py',
   'PYMODULE'),
  ('pydantic.v1.schema',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\v1\\schema.py',
   'PYMODULE'),
  ('pydantic.v1.tools',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\v1\\tools.py',
   'PYMODULE'),
  ('pydantic.v1.types',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\v1\\types.py',
   'PYMODULE'),
  ('pydantic.v1.typing',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\v1\\typing.py',
   'PYMODULE'),
  ('pydantic.v1.utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\v1\\utils.py',
   'PYMODULE'),
  ('pydantic.v1.validators',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\v1\\validators.py',
   'PYMODULE'),
  ('pydantic.v1.version',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\v1\\version.py',
   'PYMODULE'),
  ('pydantic.validate_call_decorator',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\validate_call_decorator.py',
   'PYMODULE'),
  ('pydantic.validators',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\validators.py',
   'PYMODULE'),
  ('pydantic.version',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\version.py',
   'PYMODULE'),
  ('pydantic.warnings',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic\\warnings.py',
   'PYMODULE'),
  ('pydantic_core',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic_core\\__init__.py',
   'PYMODULE'),
  ('pydantic_core.core_schema',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic_core\\core_schema.py',
   'PYMODULE'),
  ('pydoc', 'D:\\Miniconda\\envs\\give_tag\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pytest',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pytest\\__init__.py',
   'PYMODULE'),
  ('queue', 'D:\\Miniconda\\envs\\give_tag\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\Miniconda\\envs\\give_tag\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\Miniconda\\envs\\give_tag\\Lib\\random.py', 'PYMODULE'),
  ('regex',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\regex\\__init__.py',
   'PYMODULE'),
  ('regex._regex_core',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\regex\\_regex_core.py',
   'PYMODULE'),
  ('regex.regex',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\regex\\regex.py',
   'PYMODULE'),
  ('requests',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests_toolbelt',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests_toolbelt\\__init__.py',
   'PYMODULE'),
  ('requests_toolbelt._compat',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests_toolbelt\\_compat.py',
   'PYMODULE'),
  ('requests_toolbelt.adapters',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests_toolbelt\\adapters\\__init__.py',
   'PYMODULE'),
  ('requests_toolbelt.adapters.source',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests_toolbelt\\adapters\\source.py',
   'PYMODULE'),
  ('requests_toolbelt.adapters.ssl',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests_toolbelt\\adapters\\ssl.py',
   'PYMODULE'),
  ('requests_toolbelt.auth',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests_toolbelt\\auth\\__init__.py',
   'PYMODULE'),
  ('requests_toolbelt.auth._digest_auth_compat',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests_toolbelt\\auth\\_digest_auth_compat.py',
   'PYMODULE'),
  ('requests_toolbelt.auth.guess',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests_toolbelt\\auth\\guess.py',
   'PYMODULE'),
  ('requests_toolbelt.auth.http_proxy_digest',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests_toolbelt\\auth\\http_proxy_digest.py',
   'PYMODULE'),
  ('requests_toolbelt.multipart',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests_toolbelt\\multipart\\__init__.py',
   'PYMODULE'),
  ('requests_toolbelt.multipart.decoder',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests_toolbelt\\multipart\\decoder.py',
   'PYMODULE'),
  ('requests_toolbelt.multipart.encoder',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests_toolbelt\\multipart\\encoder.py',
   'PYMODULE'),
  ('requests_toolbelt.streaming_iterator',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests_toolbelt\\streaming_iterator.py',
   'PYMODULE'),
  ('requests_toolbelt.utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests_toolbelt\\utils\\__init__.py',
   'PYMODULE'),
  ('requests_toolbelt.utils.user_agent',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\requests_toolbelt\\utils\\user_agent.py',
   'PYMODULE'),
  ('rlcompleter',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy', 'D:\\Miniconda\\envs\\give_tag\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\Miniconda\\envs\\give_tag\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'D:\\Miniconda\\envs\\give_tag\\Lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.cygwin',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\cygwin.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.unix',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\unix.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.zos',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\zos.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._distutils.zosccompiler',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_distutils\\zosccompiler.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'D:\\Miniconda\\envs\\give_tag\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\Miniconda\\envs\\give_tag\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\Miniconda\\envs\\give_tag\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'D:\\Miniconda\\envs\\give_tag\\Lib\\site.py', 'PYMODULE'),
  ('smtplib', 'D:\\Miniconda\\envs\\give_tag\\Lib\\smtplib.py', 'PYMODULE'),
  ('sniffio',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\sniffio\\__init__.py',
   'PYMODULE'),
  ('sniffio._impl',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\sniffio\\_impl.py',
   'PYMODULE'),
  ('sniffio._version',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\sniffio\\_version.py',
   'PYMODULE'),
  ('socket', 'D:\\Miniconda\\envs\\give_tag\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\socketserver.py',
   'PYMODULE'),
  ('ssl', 'D:\\Miniconda\\envs\\give_tag\\Lib\\ssl.py', 'PYMODULE'),
  ('starlette',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\starlette\\__init__.py',
   'PYMODULE'),
  ('starlette._compat',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\starlette\\_compat.py',
   'PYMODULE'),
  ('starlette._utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\starlette\\_utils.py',
   'PYMODULE'),
  ('starlette.applications',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\starlette\\applications.py',
   'PYMODULE'),
  ('starlette.background',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\starlette\\background.py',
   'PYMODULE'),
  ('starlette.concurrency',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\starlette\\concurrency.py',
   'PYMODULE'),
  ('starlette.convertors',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\starlette\\convertors.py',
   'PYMODULE'),
  ('starlette.datastructures',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\starlette\\datastructures.py',
   'PYMODULE'),
  ('starlette.exceptions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\starlette\\exceptions.py',
   'PYMODULE'),
  ('starlette.formparsers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\starlette\\formparsers.py',
   'PYMODULE'),
  ('starlette.middleware',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\starlette\\middleware\\__init__.py',
   'PYMODULE'),
  ('starlette.middleware.base',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\starlette\\middleware\\base.py',
   'PYMODULE'),
  ('starlette.middleware.cors',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\starlette\\middleware\\cors.py',
   'PYMODULE'),
  ('starlette.middleware.errors',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\starlette\\middleware\\errors.py',
   'PYMODULE'),
  ('starlette.middleware.exceptions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\starlette\\middleware\\exceptions.py',
   'PYMODULE'),
  ('starlette.requests',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\starlette\\requests.py',
   'PYMODULE'),
  ('starlette.responses',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\starlette\\responses.py',
   'PYMODULE'),
  ('starlette.routing',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\starlette\\routing.py',
   'PYMODULE'),
  ('starlette.status',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\starlette\\status.py',
   'PYMODULE'),
  ('starlette.types',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\starlette\\types.py',
   'PYMODULE'),
  ('starlette.websockets',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\starlette\\websockets.py',
   'PYMODULE'),
  ('statistics',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\statistics.py',
   'PYMODULE'),
  ('string', 'D:\\Miniconda\\envs\\give_tag\\Lib\\string.py', 'PYMODULE'),
  ('stringprep',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig', 'D:\\Miniconda\\envs\\give_tag\\Lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'D:\\Miniconda\\envs\\give_tag\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\Miniconda\\envs\\give_tag\\Lib\\tempfile.py', 'PYMODULE'),
  ('tenacity',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\tenacity\\__init__.py',
   'PYMODULE'),
  ('tenacity._utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\tenacity\\_utils.py',
   'PYMODULE'),
  ('tenacity.after',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\tenacity\\after.py',
   'PYMODULE'),
  ('tenacity.asyncio',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\tenacity\\asyncio\\__init__.py',
   'PYMODULE'),
  ('tenacity.asyncio.retry',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\tenacity\\asyncio\\retry.py',
   'PYMODULE'),
  ('tenacity.before',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\tenacity\\before.py',
   'PYMODULE'),
  ('tenacity.before_sleep',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\tenacity\\before_sleep.py',
   'PYMODULE'),
  ('tenacity.nap',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\tenacity\\nap.py',
   'PYMODULE'),
  ('tenacity.retry',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\tenacity\\retry.py',
   'PYMODULE'),
  ('tenacity.stop',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\tenacity\\stop.py',
   'PYMODULE'),
  ('tenacity.tornadoweb',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\tenacity\\tornadoweb.py',
   'PYMODULE'),
  ('tenacity.wait',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\tenacity\\wait.py',
   'PYMODULE'),
  ('textwrap', 'D:\\Miniconda\\envs\\give_tag\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\Miniconda\\envs\\give_tag\\Lib\\threading.py', 'PYMODULE'),
  ('tiktoken',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\tiktoken\\__init__.py',
   'PYMODULE'),
  ('tiktoken.core',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\tiktoken\\core.py',
   'PYMODULE'),
  ('tiktoken.model',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\tiktoken\\model.py',
   'PYMODULE'),
  ('tiktoken.registry',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\tiktoken\\registry.py',
   'PYMODULE'),
  ('tiktoken_ext', '-', 'PYMODULE'),
  ('token', 'D:\\Miniconda\\envs\\give_tag\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\Miniconda\\envs\\give_tag\\Lib\\tokenize.py', 'PYMODULE'),
  ('tomllib',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._re',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('tomllib._types',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tqdm',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\tqdm\\__init__.py',
   'PYMODULE'),
  ('tqdm._dist_ver',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\tqdm\\_dist_ver.py',
   'PYMODULE'),
  ('tqdm._monitor',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\tqdm\\_monitor.py',
   'PYMODULE'),
  ('tqdm._tqdm_pandas',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\tqdm\\_tqdm_pandas.py',
   'PYMODULE'),
  ('tqdm.asyncio',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\tqdm\\asyncio.py',
   'PYMODULE'),
  ('tqdm.auto',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\tqdm\\auto.py',
   'PYMODULE'),
  ('tqdm.autonotebook',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\tqdm\\autonotebook.py',
   'PYMODULE'),
  ('tqdm.cli',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\tqdm\\cli.py',
   'PYMODULE'),
  ('tqdm.gui',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\tqdm\\gui.py',
   'PYMODULE'),
  ('tqdm.notebook',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\tqdm\\notebook.py',
   'PYMODULE'),
  ('tqdm.std',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\tqdm\\std.py',
   'PYMODULE'),
  ('tqdm.utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\tqdm\\utils.py',
   'PYMODULE'),
  ('tqdm.version',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\tqdm\\version.py',
   'PYMODULE'),
  ('tracemalloc',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty', 'D:\\Miniconda\\envs\\give_tag\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\Miniconda\\envs\\give_tag\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('typing_inspection',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\typing_inspection\\__init__.py',
   'PYMODULE'),
  ('typing_inspection.introspection',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\typing_inspection\\introspection.py',
   'PYMODULE'),
  ('typing_inspection.typing_objects',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\typing_inspection\\typing_objects.py',
   'PYMODULE'),
  ('unittest',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uuid', 'D:\\Miniconda\\envs\\give_tag\\Lib\\uuid.py', 'PYMODULE'),
  ('uvicorn',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\__init__.py',
   'PYMODULE'),
  ('uvicorn.__main__',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\__main__.py',
   'PYMODULE'),
  ('uvicorn._subprocess',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\_subprocess.py',
   'PYMODULE'),
  ('uvicorn._types',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\_types.py',
   'PYMODULE'),
  ('uvicorn.config',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\config.py',
   'PYMODULE'),
  ('uvicorn.importer',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\importer.py',
   'PYMODULE'),
  ('uvicorn.lifespan',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\lifespan\\__init__.py',
   'PYMODULE'),
  ('uvicorn.lifespan.off',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\lifespan\\off.py',
   'PYMODULE'),
  ('uvicorn.lifespan.on',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\lifespan\\on.py',
   'PYMODULE'),
  ('uvicorn.logging',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\logging.py',
   'PYMODULE'),
  ('uvicorn.loops',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\loops\\__init__.py',
   'PYMODULE'),
  ('uvicorn.loops.asyncio',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\loops\\asyncio.py',
   'PYMODULE'),
  ('uvicorn.loops.auto',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\loops\\auto.py',
   'PYMODULE'),
  ('uvicorn.loops.uvloop',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\loops\\uvloop.py',
   'PYMODULE'),
  ('uvicorn.main',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\main.py',
   'PYMODULE'),
  ('uvicorn.middleware',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\middleware\\__init__.py',
   'PYMODULE'),
  ('uvicorn.middleware.asgi2',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\middleware\\asgi2.py',
   'PYMODULE'),
  ('uvicorn.middleware.message_logger',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\middleware\\message_logger.py',
   'PYMODULE'),
  ('uvicorn.middleware.proxy_headers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\middleware\\proxy_headers.py',
   'PYMODULE'),
  ('uvicorn.middleware.wsgi',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\middleware\\wsgi.py',
   'PYMODULE'),
  ('uvicorn.protocols',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\protocols\\__init__.py',
   'PYMODULE'),
  ('uvicorn.protocols.http',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\protocols\\http\\__init__.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.auto',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\protocols\\http\\auto.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.flow_control',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\protocols\\http\\flow_control.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.h11_impl',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\protocols\\http\\h11_impl.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.httptools_impl',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\protocols\\http\\httptools_impl.py',
   'PYMODULE'),
  ('uvicorn.protocols.utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\protocols\\utils.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\protocols\\websockets\\__init__.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets.auto',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\protocols\\websockets\\auto.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets.websockets_impl',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\protocols\\websockets\\websockets_impl.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets.wsproto_impl',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\protocols\\websockets\\wsproto_impl.py',
   'PYMODULE'),
  ('uvicorn.server',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\server.py',
   'PYMODULE'),
  ('uvicorn.supervisors',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\supervisors\\__init__.py',
   'PYMODULE'),
  ('uvicorn.supervisors.basereload',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\supervisors\\basereload.py',
   'PYMODULE'),
  ('uvicorn.supervisors.multiprocess',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\supervisors\\multiprocess.py',
   'PYMODULE'),
  ('uvicorn.supervisors.statreload',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\supervisors\\statreload.py',
   'PYMODULE'),
  ('uvicorn.supervisors.watchfilesreload',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\supervisors\\watchfilesreload.py',
   'PYMODULE'),
  ('uvicorn.supervisors.watchgodreload',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\supervisors\\watchgodreload.py',
   'PYMODULE'),
  ('uvicorn.workers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\uvicorn\\workers.py',
   'PYMODULE'),
  ('watchfiles',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\watchfiles\\__init__.py',
   'PYMODULE'),
  ('watchfiles.filters',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\watchfiles\\filters.py',
   'PYMODULE'),
  ('watchfiles.main',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\watchfiles\\main.py',
   'PYMODULE'),
  ('watchfiles.run',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\watchfiles\\run.py',
   'PYMODULE'),
  ('watchfiles.version',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\watchfiles\\version.py',
   'PYMODULE'),
  ('webbrowser',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('websockets',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\__init__.py',
   'PYMODULE'),
  ('websockets.__main__',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\__main__.py',
   'PYMODULE'),
  ('websockets.asyncio',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\asyncio\\__init__.py',
   'PYMODULE'),
  ('websockets.asyncio.async_timeout',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\asyncio\\async_timeout.py',
   'PYMODULE'),
  ('websockets.asyncio.client',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\asyncio\\client.py',
   'PYMODULE'),
  ('websockets.asyncio.compatibility',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\asyncio\\compatibility.py',
   'PYMODULE'),
  ('websockets.asyncio.connection',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\asyncio\\connection.py',
   'PYMODULE'),
  ('websockets.asyncio.messages',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\asyncio\\messages.py',
   'PYMODULE'),
  ('websockets.asyncio.router',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\asyncio\\router.py',
   'PYMODULE'),
  ('websockets.asyncio.server',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\asyncio\\server.py',
   'PYMODULE'),
  ('websockets.auth',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\auth.py',
   'PYMODULE'),
  ('websockets.cli',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\cli.py',
   'PYMODULE'),
  ('websockets.client',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\client.py',
   'PYMODULE'),
  ('websockets.connection',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\connection.py',
   'PYMODULE'),
  ('websockets.datastructures',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\datastructures.py',
   'PYMODULE'),
  ('websockets.exceptions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\exceptions.py',
   'PYMODULE'),
  ('websockets.extensions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\extensions\\__init__.py',
   'PYMODULE'),
  ('websockets.extensions.base',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\extensions\\base.py',
   'PYMODULE'),
  ('websockets.extensions.permessage_deflate',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\extensions\\permessage_deflate.py',
   'PYMODULE'),
  ('websockets.frames',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\frames.py',
   'PYMODULE'),
  ('websockets.headers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\headers.py',
   'PYMODULE'),
  ('websockets.http',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\http.py',
   'PYMODULE'),
  ('websockets.http11',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\http11.py',
   'PYMODULE'),
  ('websockets.imports',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\imports.py',
   'PYMODULE'),
  ('websockets.legacy',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\legacy\\__init__.py',
   'PYMODULE'),
  ('websockets.legacy.auth',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\legacy\\auth.py',
   'PYMODULE'),
  ('websockets.legacy.client',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\legacy\\client.py',
   'PYMODULE'),
  ('websockets.legacy.exceptions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\legacy\\exceptions.py',
   'PYMODULE'),
  ('websockets.legacy.framing',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\legacy\\framing.py',
   'PYMODULE'),
  ('websockets.legacy.handshake',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\legacy\\handshake.py',
   'PYMODULE'),
  ('websockets.legacy.http',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\legacy\\http.py',
   'PYMODULE'),
  ('websockets.legacy.protocol',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\legacy\\protocol.py',
   'PYMODULE'),
  ('websockets.legacy.server',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\legacy\\server.py',
   'PYMODULE'),
  ('websockets.protocol',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\protocol.py',
   'PYMODULE'),
  ('websockets.server',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\server.py',
   'PYMODULE'),
  ('websockets.streams',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\streams.py',
   'PYMODULE'),
  ('websockets.sync',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\sync\\__init__.py',
   'PYMODULE'),
  ('websockets.sync.client',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\sync\\client.py',
   'PYMODULE'),
  ('websockets.sync.connection',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\sync\\connection.py',
   'PYMODULE'),
  ('websockets.sync.messages',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\sync\\messages.py',
   'PYMODULE'),
  ('websockets.sync.router',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\sync\\router.py',
   'PYMODULE'),
  ('websockets.sync.server',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\sync\\server.py',
   'PYMODULE'),
  ('websockets.sync.utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\sync\\utils.py',
   'PYMODULE'),
  ('websockets.typing',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\typing.py',
   'PYMODULE'),
  ('websockets.uri',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\uri.py',
   'PYMODULE'),
  ('websockets.utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\utils.py',
   'PYMODULE'),
  ('websockets.version',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\version.py',
   'PYMODULE'),
  ('wheel',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\wheel\\__init__.py',
   'PYMODULE'),
  ('wheel.cli',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('wheel.cli.convert',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('wheel.cli.pack',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('wheel.cli.tags',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('wheel.cli.unpack',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('wheel.macosx_libfile',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('wheel.metadata',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\wheel\\metadata.py',
   'PYMODULE'),
  ('wheel.util',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\wheel\\util.py',
   'PYMODULE'),
  ('wheel.vendored',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._elffile',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._manylinux',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._musllinux',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._parser',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._structures',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._tokenizer',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.markers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.requirements',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.specifiers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.tags',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.utils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.version',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('wheel.wheelfile',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('win32_setctime',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\win32_setctime\\__init__.py',
   'PYMODULE'),
  ('win32_setctime._setctime',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\win32_setctime\\_setctime.py',
   'PYMODULE'),
  ('xml', 'D:\\Miniconda\\envs\\give_tag\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.etree',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('yaml',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.composer',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.dumper',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.error',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('yaml.events',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.loader',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.parser',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.reader',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.representer',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.tokens',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('yarl',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\yarl\\__init__.py',
   'PYMODULE'),
  ('yarl._parse',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\yarl\\_parse.py',
   'PYMODULE'),
  ('yarl._path',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\yarl\\_path.py',
   'PYMODULE'),
  ('yarl._query',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\yarl\\_query.py',
   'PYMODULE'),
  ('yarl._quoters',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\yarl\\_quoters.py',
   'PYMODULE'),
  ('yarl._quoting',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\yarl\\_quoting.py',
   'PYMODULE'),
  ('yarl._quoting_py',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\yarl\\_quoting_py.py',
   'PYMODULE'),
  ('yarl._url',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\yarl\\_url.py',
   'PYMODULE'),
  ('zipfile', 'D:\\Miniconda\\envs\\give_tag\\Lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'D:\\Miniconda\\envs\\give_tag\\Lib\\zipimport.py', 'PYMODULE'),
  ('zoneinfo',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('zoneinfo._common',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\zoneinfo\\_common.py',
   'PYMODULE'),
  ('zoneinfo._tzpath',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\zoneinfo\\_tzpath.py',
   'PYMODULE'),
  ('zoneinfo._zoneinfo',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\zoneinfo\\_zoneinfo.py',
   'PYMODULE'),
  ('zstandard',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\zstandard\\__init__.py',
   'PYMODULE'),
  ('zstandard.backend_cffi',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\zstandard\\backend_cffi.py',
   'PYMODULE')])
