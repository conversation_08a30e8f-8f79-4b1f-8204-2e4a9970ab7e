# Qwen3推理功能配置示例

## 概述

本文档介绍如何在智能命题系统中配置和使用Qwen3模型的推理功能，通过`enable_thinking`参数来控制推理过程。

## 配置说明

### 1. 启用推理功能的配置

在`WorkflowPost`中配置Qwen3模型时，系统会自动在命题规划步骤启用推理功能：

```json
{
  "TaskId": "qwen3_thinking_test",
  "TaskName": "智能命题",
  "WorkflowPost": {
    "NodeList": [
      {
        "NodeName": "智能命题节点",
        "NodeType": "intelligent_question",
        "NodeContent": [
          {
            "NodePrompt": {
              "function": "智能命题",
              "workflow": "标准流程",
              "总体任务": "生成高质量试题",
              "总体要求": "符合教学要求"
            },
            "NodeModel": {
              "ModelName": "qwen-turbo",
              "ModelArgs": {
                "TopP": "0.95",
                "Temperature": "0.6",
                "MaxToken": "4096",
                "ApiKey": "your_qwen_api_key",
                "ChatUrl": "https://api.lkeap.cloud.tencent.com/v1"
              }
            }
          }
        ]
      }
    ]
  }
}
```

### 2. 禁用推理功能的配置

如果需要禁用推理功能，可以在`ModelArgs`中添加`EnableThinking: false`：

```json
{
  "NodeModel": {
    "ModelName": "qwen-turbo",
    "ModelArgs": {
      "TopP": "0.95",
      "Temperature": "0.6",
      "MaxToken": "4096",
      "ApiKey": "your_qwen_api_key",
      "ChatUrl": "https://api.lkeap.cloud.tencent.com/v1",
      "EnableThinking": false
    }
  }
}
```

## 工作原理

### 1. 自动推理启用

当检测到Qwen3模型时，系统会自动在命题规划步骤启用推理功能：

```python
# 为Qwen3模型在命题规划步骤启用推理功能
if "qwen" in model_name.lower():
    additional_params["enable_thinking"] = True
    self.logger.info(f"为Qwen3模型启用推理功能: {model_name}")
```

### 2. 推理参数传递

系统会将`enable_thinking`参数传递给LLM实例创建函数：

```python
# 构建extra_body参数
extra_body = {}
if enable_thinking:
    extra_body = {"chat_template_kwargs": {"enable_thinking": True}}
else:
    extra_body = {"chat_template_kwargs": {"enable_thinking": False}}
```

### 3. 推理内容处理

系统会处理Qwen3返回的推理内容：

```python
# 处理Qwen3推理内容
if hasattr(chunk, 'additional_kwargs') and 'thinking' in chunk.additional_kwargs:
    thinking_delta = chunk.additional_kwargs.get('thinking', '')
    if thinking_delta:
        # 推送到流式接口
        await stream_callback({
            "type": "thinking",
            "content": thinking_delta,
            "step": "qwen_thinking",
            "accumulated": reasoning_content,
            "timestamp": asyncio.get_event_loop().time()
        })
```

## 流式输出格式

### 1. 推理过程数据

启用推理功能时，`/QueryStream`接口会返回推理过程数据：

```json
{
  "id": "task_id",
  "node_name": "planning",
  "content": "推理内容...",
  "current_time": "2024-01-01 12:00:00.123",
  "data": {
    "step": "planning",
    "thinking": "推理内容...",
    "progress": 25,
    "detail": "推理内容...",
    "thinking_type": "qwen_thinking"
  }
}
```

### 2. 普通内容数据

禁用推理功能时，返回普通内容数据：

```json
{
  "id": "task_id",
  "node_name": "planning",
  "content": "普通内容...",
  "current_time": "2024-01-01 12:00:00.123",
  "data": {
    "step": "planning",
    "thinking": "普通内容...",
    "progress": 25,
    "detail": "普通内容..."
  }
}
```

## 测试方法

### 1. 使用测试脚本

运行Qwen3推理功能测试：

```bash
python test_qwen3_thinking.py
```

### 2. 使用Postman测试

#### 启用推理功能

```json
POST http://localhost:8000/AgentGen
Content-Type: application/json

{
  "TaskId": "qwen3_thinking_test",
  "TaskName": "智能命题",
  "QuesPost": {
    "AiQuesTypePost": {
      "QuesTypeName": "选择题",
      "QuesStr": "测试题目内容",
      "Elements": [],
      "Childs": []
    },
    "ExamSubjectName": "语文",
    "ExamProjectName": "测试项目"
  },
  "WorkflowPost": {
    "NodeList": [
      {
        "NodeName": "智能命题节点",
        "NodeType": "intelligent_question",
        "NodeContent": [
          {
            "NodePrompt": {
              "function": "智能命题",
              "workflow": "标准流程",
              "总体任务": "生成高质量试题",
              "总体要求": "符合教学要求"
            },
            "NodeModel": {
              "ModelName": "qwen-turbo",
              "ModelArgs": {
                "TopP": "0.95",
                "Temperature": "0.6",
                "MaxToken": "4096",
                "ApiKey": "your_qwen_api_key",
                "ChatUrl": "https://api.lkeap.cloud.tencent.com/v1"
              }
            }
          }
        ]
      }
    ]
  }
}
```

#### 监听流式数据

```json
POST http://localhost:8000/QueryStream
Content-Type: application/json

{
  "TaskId": "qwen3_thinking_test"
}
```

## 注意事项

1. **API密钥配置**：请确保使用有效的Qwen3 API密钥
2. **API地址配置**：请使用正确的API地址
3. **推理功能限制**：推理功能仅在命题规划步骤启用
4. **流式输出**：推理过程会实时推送到`/QueryStream`接口
5. **兼容性**：支持所有Qwen3系列模型

## 故障排除

### 1. 推理功能未启用

检查模型名称是否包含"qwen"：
```python
if "qwen" in model_name.lower():
    additional_params["enable_thinking"] = True
```

### 2. 推理内容未接收

检查API配置是否正确：
- API密钥是否有效
- API地址是否正确
- 网络连接是否正常

### 3. 流式数据异常

检查流式回调处理：
```python
if hasattr(chunk, 'additional_kwargs') and 'thinking' in chunk.additional_kwargs:
    # 处理推理内容
```

## 总结

通过以上配置，您可以在智能命题系统中使用Qwen3模型的推理功能，实现更智能的命题规划过程。推理过程会实时推送到前端，让用户能够看到模型的思考过程。 