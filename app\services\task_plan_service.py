from typing import List, Dict, Any, Optional
from loguru import logger
from app.models.schemas import QuestionTaskPlan, DifficultyLevel
from app.services.llm_manager import generate_text
from app.core.state_manager import task_manager, TaskStatus
import json


class TaskPlanService:
    """任务规划服务"""
    
    def __init__(self):
        self.logger = logger
    
    async def create_task_plan(
        self,
        material: str,
        question_tasks: List[QuestionTaskPlan],
        task_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        创建智能任务规划
        
        Args:
            material: 命题材料
            question_tasks: 命题任务列表
            task_id: 任务ID
            
        Returns:
            任务规划结果
        """
        try:
            self.logger.info(f"开始创建任务规划: {len(question_tasks)} 个任务")
            
            # 更新任务状态
            if task_id:
                await task_manager.update_task_status(
                    task_id,
                    TaskStatus.PROCESSING,
                    step_info={
                        "step": "analyzing_material",
                        "progress": 10,
                        "detail": "分析材料内容"
                    }
                )
            
            # 分析材料
            material_analysis = await self._analyze_material(material, task_id)
            
            # 更新任务状态
            if task_id:
                await task_manager.update_task_status(
                    task_id,
                    TaskStatus.PROCESSING,
                    step_info={
                        "step": "planning_tasks",
                        "progress": 40,
                        "detail": "规划任务结构"
                    }
                )
            
            # 创建详细任务规划
            detailed_plan = await self._create_detailed_plan(
                material_analysis, question_tasks, task_id
            )
            
            # 更新任务状态
            if task_id:
                await task_manager.update_task_status(
                    task_id,
                    TaskStatus.PROCESSING,
                    step_info={
                        "step": "estimating_time",
                        "progress": 70,
                        "detail": "估算完成时间"
                    }
                )
            
            # 估算完成时间
            estimated_time = self._estimate_completion_time(detailed_plan)
            
            # 计算总题目数量
            total_questions = sum(task.quantity for task in question_tasks)
            
            # 更新任务状态
            if task_id:
                await task_manager.update_task_status(
                    task_id,
                    TaskStatus.PROCESSING,
                    step_info={
                        "step": "finalizing_plan",
                        "progress": 90,
                        "detail": "完善任务规划"
                    }
                )
            
            result = {
                "task_plan": detailed_plan,
                "total_questions": total_questions,
                "estimated_time": estimated_time,
                "material_analysis": material_analysis
            }
            
            self.logger.info(f"任务规划创建成功: {total_questions} 题，预计 {estimated_time} 分钟")
            return result
            
        except Exception as e:
            self.logger.error(f"任务规划创建失败: {e}")
            
            # 更新任务状态为失败
            if task_id:
                await task_manager.update_task_status(
                    task_id,
                    TaskStatus.FAILED,
                    error_message=str(e)
                )
            
            raise
    
    async def _analyze_material(self, material: str, task_id: Optional[str] = None) -> Dict[str, Any]:
        """分析材料内容"""
        
        prompt = f"""请分析以下命题材料，提取关键信息：

材料内容：
{material}

请从以下角度分析材料：
1. 主要主题和知识点
2. 材料类型和特点
3. 适合的题型建议
4. 难度等级评估
5. 可命题的角度和方向

请以JSON格式返回分析结果：
{{
    "main_topics": ["主题1", "主题2"],
    "knowledge_points": ["知识点1", "知识点2"],
    "material_type": "材料类型",
    "characteristics": ["特点1", "特点2"],
    "suitable_question_types": ["题型1", "题型2"],
    "difficulty_assessment": "难度评估",
    "question_angles": ["角度1", "角度2"]
}}"""
        
        try:
            result = await generate_text(prompt, temperature=0.3)
            
            # 尝试解析JSON
            try:
                analysis = json.loads(result)
            except json.JSONDecodeError:
                # 如果解析失败，返回基本分析
                analysis = {
                    "main_topics": ["材料分析"],
                    "knowledge_points": ["基础知识"],
                    "material_type": "文本材料",
                    "characteristics": ["内容丰富"],
                    "suitable_question_types": ["选择题", "简答题"],
                    "difficulty_assessment": "中等",
                    "question_angles": ["理解分析"]
                }
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"材料分析失败: {e}")
            # 返回默认分析
            return {
                "main_topics": ["材料分析"],
                "knowledge_points": ["基础知识"],
                "material_type": "文本材料",
                "characteristics": ["内容丰富"],
                "suitable_question_types": ["选择题", "简答题"],
                "difficulty_assessment": "中等",
                "question_angles": ["理解分析"]
            }
    
    async def _create_detailed_plan(
        self,
        material_analysis: Dict[str, Any],
        question_tasks: List[QuestionTaskPlan],
        task_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """创建详细任务规划"""
        
        detailed_plan = []
        
        for i, task in enumerate(question_tasks):
            # 为每个任务创建详细规划
            task_detail = {
                "task_id": f"task_{i+1}",
                "question_type": task.question_type,
                "quantity": task.quantity,
                "difficulty": task.difficulty.value,
                "knowledge_points": task.knowledge_points,
                "special_requirements": task.special_requirements,
                "structural_elements": task.structural_elements,
                "estimated_time_per_question": self._estimate_time_per_question(
                    task.question_type, task.difficulty
                ),
                "total_estimated_time": self._estimate_time_per_question(
                    task.question_type, task.difficulty
                ) * task.quantity,
                "priority": self._calculate_priority(task),
                "dependencies": [],
                "resources_needed": self._identify_resources_needed(task),
                "quality_criteria": self._define_quality_criteria(task)
            }
            
            detailed_plan.append(task_detail)
        
        # 优化任务顺序
        optimized_plan = self._optimize_task_order(detailed_plan)
        
        return optimized_plan
    
    def _estimate_time_per_question(self, question_type: str, difficulty: DifficultyLevel) -> int:
        """估算每题完成时间（分钟）"""
        
        base_times = {
            "单选题": 2,
            "多选题": 3,
            "填空题": 3,
            "判断题": 1,
            "简答题": 8,
            "论述题": 15,
            "阅读理解": 10,
            "作文题": 20
        }
        
        difficulty_multipliers = {
            DifficultyLevel.EASY: 0.8,
            DifficultyLevel.MEDIUM: 1.0,
            DifficultyLevel.HARD: 1.5
        }
        
        base_time = base_times.get(question_type, 5)
        multiplier = difficulty_multipliers.get(difficulty, 1.0)
        
        return int(base_time * multiplier)
    
    def _estimate_completion_time(self, detailed_plan: List[Dict[str, Any]]) -> int:
        """估算总完成时间"""
        return sum(task["total_estimated_time"] for task in detailed_plan)
    
    def _calculate_priority(self, task: QuestionTaskPlan) -> int:
        """计算任务优先级（1-10，10最高）"""
        
        priority = 5  # 基础优先级
        
        # 根据难度调整
        if task.difficulty == DifficultyLevel.EASY:
            priority += 2  # 简单题优先
        elif task.difficulty == DifficultyLevel.HARD:
            priority -= 1  # 困难题后做
        
        # 根据题型调整
        if task.question_type in ["单选题", "判断题"]:
            priority += 1  # 客观题优先
        elif task.question_type in ["论述题", "作文题"]:
            priority -= 2  # 主观题后做
        
        return max(1, min(10, priority))
    
    def _identify_resources_needed(self, task: QuestionTaskPlan) -> List[str]:
        """识别所需资源"""
        
        resources = ["LLM模型"]
        
        if task.question_type in ["阅读理解", "作文题"]:
            resources.append("材料分析")
        
        if task.difficulty == DifficultyLevel.HARD:
            resources.append("专家审核")
        
        if task.special_requirements:
            resources.append("特殊要求处理")
        
        return resources
    
    def _define_quality_criteria(self, task: QuestionTaskPlan) -> Dict[str, Any]:
        """定义质量标准"""
        
        return {
            "accuracy": "内容准确无误",
            "clarity": "表达清晰明确",
            "difficulty_match": f"符合{task.difficulty.value}难度要求",
            "knowledge_coverage": "覆盖指定知识点",
            "format_compliance": "符合题型格式要求"
        }
    
    def _optimize_task_order(self, detailed_plan: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """优化任务顺序"""
        
        # 按优先级排序
        return sorted(detailed_plan, key=lambda x: x["priority"], reverse=True)
    
    async def optimize_task_plan(
        self,
        task_plan: List[Dict[str, Any]],
        optimization_goals: List[str],
        task_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """优化任务规划"""
        
        # 这里实现任务规划优化逻辑
        # 为简化，返回基本优化结果
        
        optimized_plan = task_plan.copy()
        
        return {
            "optimized_plan": optimized_plan,
            "optimization_summary": "任务规划已优化",
            "improvement_percentage": 10
        }
    
    async def validate_task_plan(
        self,
        task_plan: List[Dict[str, Any]],
        task_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """验证任务规划"""
        
        # 这里实现任务规划验证逻辑
        # 为简化，返回基本验证结果
        
        return {
            "is_valid": True,
            "validation_report": "任务规划验证通过",
            "suggestions": []
        }


# 创建全局服务实例
task_plan_service = TaskPlanService()
