#!/usr/bin/env python3
"""
测试 ainvoke 方法的兼容性和可用性
"""

import asyncio
import inspect
from typing import Dict, Any
from app.services.llm_manager import LLMManager

async def test_ainvoke_availability():
    """测试 ainvoke 方法的可用性"""
    
    print("🔍 测试 ainvoke 方法的可用性...")
    print("="*60)
    
    # 测试配置
    test_configs = [
        {
            "name": "deepseek-r1",
            "api_key": "test-key",
            "api_base": "https://api.deepseek.com/v1",
            "temperature": 0.7,
            "max_tokens": 100
        },
        {
            "name": "gpt-4",
            "api_key": "test-key", 
            "api_base": "https://api.openai.com/v1",
            "temperature": 0.7,
            "max_tokens": 100
        },
        {
            "name": "qwen3-turbo",
            "api_key": "test-key",
            "api_base": "https://dashscope.aliyuncs.com/compatible-mode/v1",
            "temperature": 0.7,
            "max_tokens": 100
        }
    ]
    
    async with LLMMana<PERSON>() as llm_manager:
        
        for i, config in enumerate(test_configs, 1):
            print(f"🔧 测试 {i}: {config['name']}")
            print("-" * 40)
            
            try:
                # 创建LLM实例
                llm_instance = llm_manager._get_cached_llm_instance(config)
                
                # 检查实例类型
                instance_type = type(llm_instance).__name__
                print(f"   实例类型: {instance_type}")
                
                # 检查可用方法
                available_methods = [method for method in dir(llm_instance) 
                                   if not method.startswith('_')]
                
                # 检查 invoke 和 ainvoke 方法
                has_invoke = hasattr(llm_instance, 'invoke')
                has_ainvoke = hasattr(llm_instance, 'ainvoke')
                
                print(f"   有 invoke 方法: {'✅' if has_invoke else '❌'}")
                print(f"   有 ainvoke 方法: {'✅' if has_ainvoke else '❌'}")
                
                if has_invoke:
                    invoke_method = getattr(llm_instance, 'invoke')
                    invoke_signature = inspect.signature(invoke_method)
                    print(f"   invoke 签名: {invoke_signature}")
                    
                    # 检查是否为协程函数
                    is_coroutine = inspect.iscoroutinefunction(invoke_method)
                    print(f"   invoke 是协程: {'✅' if is_coroutine else '❌'}")
                
                if has_ainvoke:
                    ainvoke_method = getattr(llm_instance, 'ainvoke')
                    ainvoke_signature = inspect.signature(ainvoke_method)
                    print(f"   ainvoke 签名: {ainvoke_signature}")
                    
                    # 检查是否为协程函数
                    is_coroutine = inspect.iscoroutinefunction(ainvoke_method)
                    print(f"   ainvoke 是协程: {'✅' if is_coroutine else '❌'}")
                
                # 检查其他相关方法
                related_methods = [m for m in available_methods 
                                 if 'invoke' in m.lower() or 'call' in m.lower()]
                if related_methods:
                    print(f"   相关方法: {related_methods}")
                
                print()
                
            except Exception as e:
                print(f"   ❌ 创建实例失败: {e}")
                print()

async def test_invoke_vs_ainvoke_performance():
    """测试 invoke 和 ainvoke 的性能差异"""
    
    print("⚡ 测试 invoke 和 ainvoke 的性能差异...")
    print("="*60)
    
    test_config = {
        "name": "deepseek-r1",
        "api_key": "test-key",
        "api_base": "https://api.deepseek.com/v1",
        "temperature": 0.7,
        "max_tokens": 100
    }
    
    test_messages = [{"role": "user", "content": "Hello, this is a test message."}]
    
    async with LLMManager() as llm_manager:
        try:
            llm_instance = llm_manager._get_cached_llm_instance(test_config)
            
            # 测试 invoke 方法（如果可用）
            if hasattr(llm_instance, 'invoke'):
                print("🔧 测试 invoke 方法...")
                
                import time
                start_time = time.time()
                
                try:
                    # 检查是否为协程函数
                    if inspect.iscoroutinefunction(llm_instance.invoke):
                        print("   invoke 是协程函数，使用 await 调用")
                        # result = await llm_instance.invoke(test_messages)
                        print("   （跳过实际调用以避免API费用）")
                    else:
                        print("   invoke 是同步函数")
                        # result = llm_instance.invoke(test_messages)
                        print("   （跳过实际调用以避免API费用）")
                    
                    duration = time.time() - start_time
                    print(f"   invoke 调用耗时: {duration:.3f}秒（模拟）")
                    
                except Exception as e:
                    print(f"   invoke 调用失败: {e}")
            
            # 测试 ainvoke 方法（如果可用）
            if hasattr(llm_instance, 'ainvoke'):
                print("🔧 测试 ainvoke 方法...")
                
                import time
                start_time = time.time()
                
                try:
                    # ainvoke 应该总是协程函数
                    print("   ainvoke 是协程函数，使用 await 调用")
                    # result = await llm_instance.ainvoke(test_messages)
                    print("   （跳过实际调用以避免API费用）")
                    
                    duration = time.time() - start_time
                    print(f"   ainvoke 调用耗时: {duration:.3f}秒（模拟）")
                    
                except Exception as e:
                    print(f"   ainvoke 调用失败: {e}")
            
            print()
            
        except Exception as e:
            print(f"❌ 性能测试失败: {e}")

def analyze_project_async_architecture():
    """分析项目的异步架构"""
    
    print("🏗️ 分析项目的异步架构...")
    print("="*60)
    
    print("📋 当前项目异步调用链:")
    print("1. FastAPI 路由 (async)")
    print("   ↓")
    print("2. process_*_sync 函数 (async)")
    print("   ↓") 
    print("3. IntelligentQuestionService.generate_questions (async)")
    print("   ↓")
    print("4. _call_llm_* 方法 (async)")
    print("   ↓")
    print("5. LLMManager._call_langchain_api (async)")
    print("   ↓")
    print("6. llm.invoke (sync) ← 当前使用")
    print("   或")
    print("6. llm.ainvoke (async) ← 建议改为")
    print()
    
    print("🎯 改为 ainvoke 的优势:")
    print("✅ 完全异步调用链，避免阻塞")
    print("✅ 更好的并发性能")
    print("✅ 符合 FastAPI 异步最佳实践")
    print("✅ 减少线程池开销")
    print()
    
    print("⚠️ 需要考虑的问题:")
    print("1. langchain 版本兼容性")
    print("2. 所有模型类型是否都支持 ainvoke")
    print("3. 错误处理和异常传播")
    print("4. 性能测试和验证")
    print()

def generate_migration_plan():
    """生成迁移计划"""
    
    print("📋 invoke → ainvoke 迁移计划...")
    print("="*60)
    
    print("🔄 迁移步骤:")
    print()
    
    print("1️⃣ **兼容性验证**")
    print("   - 验证所有使用的 langchain 模型类都支持 ainvoke")
    print("   - 检查 langchain 版本要求")
    print("   - 测试不同模型类型的 ainvoke 行为")
    print()
    
    print("2️⃣ **代码修改**")
    print("   - 修改 _call_langchain_api 中的 llm.invoke → llm.ainvoke")
    print("   - 添加适当的 await 关键字")
    print("   - 更新错误处理逻辑")
    print()
    
    print("3️⃣ **测试验证**")
    print("   - 单元测试覆盖")
    print("   - 集成测试验证")
    print("   - 性能基准测试")
    print("   - 并发压力测试")
    print()
    
    print("4️⃣ **渐进式部署**")
    print("   - 先在测试环境验证")
    print("   - 灰度发布到生产环境")
    print("   - 监控性能指标")
    print()
    
    print("🛡️ **风险控制**")
    print("   - 保留 invoke 作为降级方案")
    print("   - 添加配置开关控制使用哪种方法")
    print("   - 完善监控和告警")

async def main():
    """主函数"""
    try:
        await test_ainvoke_availability()
        await test_invoke_vs_ainvoke_performance()
        analyze_project_async_architecture()
        generate_migration_plan()
        
        print("🎯 总结:")
        print("="*60)
        print("1. 检查了 langchain 实例的方法可用性")
        print("2. 分析了项目的异步架构")
        print("3. 提供了迁移计划和风险控制建议")
        print()
        print("💡 建议:")
        print("- 如果所有模型都支持 ainvoke，建议进行迁移")
        print("- 迁移前进行充分的测试验证")
        print("- 考虑添加配置开关以便快速回滚")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
