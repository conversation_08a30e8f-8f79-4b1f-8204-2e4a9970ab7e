# 生产环境日志优化说明

## 优化概述

为了减少生产环境中的控制台日志输出，我们对日志系统进行了全面优化，通过环境变量控制日志级别和输出方式。

## 优化内容

### 1. 环境变量控制

通过 `ENVIRONMENT` 环境变量控制日志行为：

- **开发环境** (`ENVIRONMENT=development`)：
  - 控制台输出：DEBUG级别及以上
  - 文件输出：DEBUG级别及以上
  - 保留期：7天

- **生产环境** (`ENVIRONMENT=production` 或未设置)：
  - 控制台输出：WARNING级别及以上（只显示警告和错误）
  - 文件输出：INFO级别及以上
  - 保留期：30天（普通日志），90天（错误日志）

### 2. 日志级别调整

将大量频繁的INFO级别日志调整为DEBUG级别：

#### 智能命题服务 (`intelligent_question_service.py`)
- ✅ 保留关键业务日志（INFO级别）：
  - "开始智能命题"
  - "智能命题完成"
  
- 🔧 调整为DEBUG级别：
  - LLM调用详情
  - 步骤完成标记
  - 模型配置信息
  - 重试成功信息
  - 规划尝试信息

#### LLM管理器 (`llm_manager.py`)
- 🔧 调整为DEBUG级别：
  - 流式API调用开始/结束
  - 连接建立成功
  - 推理内容推送详情

#### 状态管理器 (`state_manager.py`)
- ✅ 保留关键业务日志（INFO级别）：
  - 任务创建
  
- 🔧 调整为DEBUG级别：
  - 连接状态变更
  - 任务状态更新
  - 队列操作详情
  - 任务清理信息

### 3. 日志文件分离

生产环境下创建了专门的错误日志文件：
- `logs/app.log` - 包含INFO及以上级别的所有日志
- `logs/error.log` - 只包含ERROR级别的日志，便于快速定位问题

## 使用方法

### 1. 设置环境变量

**开发环境：**
```bash
export ENVIRONMENT=development
```

**生产环境：**
```bash
export ENVIRONMENT=production
# 或者不设置（默认为生产环境）
```

### 2. 使用Docker

在 `docker-compose.yml` 中设置：
```yaml
services:
  app:
    environment:
      - ENVIRONMENT=production
```

### 3. 使用systemd服务

在服务文件中设置：
```ini
[Service]
Environment=ENVIRONMENT=production
```

## 优化效果

### 开发环境
- 控制台输出详细，便于调试
- 包含所有DEBUG信息
- 快速定位问题

### 生产环境
- **控制台输出减少90%以上**
- 只显示重要的警告和错误信息
- 详细日志仍记录在文件中
- 提高系统性能，减少I/O开销

## 日志级别说明

| 级别 | 用途 | 生产环境控制台 | 生产环境文件 |
|------|------|----------------|--------------|
| DEBUG | 详细调试信息 | ❌ | ❌ |
| INFO | 一般信息 | ❌ | ✅ |
| WARNING | 警告信息 | ✅ | ✅ |
| ERROR | 错误信息 | ✅ | ✅ |

## 监控建议

1. **实时监控错误日志**：
   ```bash
   tail -f logs/error.log
   ```

2. **查看最近的警告和错误**：
   ```bash
   grep -E "(WARNING|ERROR)" logs/app.log | tail -20
   ```

3. **统计错误频率**：
   ```bash
   grep "ERROR" logs/app.log | wc -l
   ```

## 回滚方案

如果需要临时启用详细日志，可以：

1. **临时设置环境变量**：
   ```bash
   export ENVIRONMENT=development
   # 重启应用
   ```

2. **或者修改代码中的日志级别**：
   ```python
   # 在main.py中临时修改
   logger.add(sys.stdout, level="DEBUG")
   ```

## 注意事项

1. **性能影响**：DEBUG级别日志会影响性能，生产环境建议关闭
2. **磁盘空间**：注意监控日志文件大小，定期清理旧日志
3. **敏感信息**：确保日志中不包含敏感信息（API密钥、用户数据等）
4. **日志轮转**：已配置自动日志轮转，无需手动管理

## 总结

通过这次优化，生产环境的控制台日志输出大幅减少，同时保持了完整的日志记录功能。开发人员可以通过环境变量轻松切换日志模式，既满足了生产环境的简洁需求，也保证了开发调试的便利性。
