# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],  # 你的主程序入口
    pathex=['.'],  # 搜索路径，当前目录
    binaries=[],   # 额外的二进制文件
    datas=[  # 把配置文件打包到根目录
        ('app', 'app'),        # 把整个app目录打包进去
    ],
    hiddenimports=[],  # 如果有动态import的包，可以在这里补充
    hookspath=[],      # 自定义hook路径
    hooksconfig={},
    runtime_hooks=[],  # 运行时hook
    excludes=[],       # 不需要的包
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='智能命题—0710',  # 生成的exe名称
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,  # 如果是命令行程序用True，GUI程序用False
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='智能命题'
) 