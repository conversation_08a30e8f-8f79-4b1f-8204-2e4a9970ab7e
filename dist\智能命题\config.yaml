# 智能命题系统配置文件
model_service:
  mode: offline   # 可选值：offline 或 online
# 模型配置
models:
  default: "deepseek-r1"
  available:
    - name: "deepseek-r1"
      api_base: "https://api.deepseek.com/v1"
      api_key: "${DEEPSEEK_API_KEY}"
      max_tokens: 4000
      temperature: 0.7
    - name: "gpt-4"
      api_base: "https://api.openai.com/v1"
      api_key: "${OPENAI_API_KEY}"
      max_tokens: 4000
      temperature: 0.7
    - name: "qwen-plus"
      api_base: "https://dashscope.aliyuncs.com/api/v1"
      api_key: "${DASHSCOPE_API_KEY}"
      max_tokens: 4000
      temperature: 0.7
    - name: "local"
      api_base: "http://172.168.0.200:8082/v1"
      api_key: "${DASHSCOPE_API_KEY}"
      max_tokens: 4000
      temperature: 0.7

# 试题克隆配置
question_clone:
  # 克隆策略
  strategies:
    full: "完全克隆：保持原题结构，只变更试题内容"
    content_only: "仅克隆内容：保持所有属性，只改变QuesStr"
    structure_only: "仅克隆结构：保持题型和属性，清空具体内容"
  default_grade: "高中生"
  
  # 组合题克隆步骤
  composite_steps:
    - "分析组合题结构"
    - "克隆材料部分"
    - "克隆题干结构"
    - "生成子题目内容"
    - "组装完整试题"
  
  # 基础题克隆步骤
  basic_steps:
    - "分析题目结构"
    - "生成新试题内容"
    - "验证克隆质量"

# 提示词模板
prompts:
  # 基础题克隆提示词
  basic_clone: |
    # 角色与任务
    
    你是一个专业的试题克隆专家。请基于以下原题进行克隆，生成一道新的试题。

    # 原题信息
    - 题目内容：{original_content}
    - 题目类型：{question_type}
    - 学科：{subject}
    - 受试人群：{grade}
    - 难度：{difficulty}
    - 知识点：{knowledge_points}

    # 克隆要求
    1. 保持相同的题型结构和难度
    2. 使用不同的具体内容（如不同的材料、不同的题目描述）
    3. 保持相同的知识点要求
    4. 确保试题质量不低于原题
    5. 生成的内容要符合{subject}学科的特点

    # 输出试题结构
    {mapped_structure}
    
    # 输出要求：
    1. 不要输出任何与试题结构无关的内容
    2. 严格按照输出试题结构的要求生成试题

    请生成新的子题目内容：
    请生成新的试题内容：

  # 组合题材料克隆提示词
  composite_material_clone: |
    # 角色与任务
    你是一个专业的试题材料克隆专家。请基于以下原题材料进行克隆，生成新的材料内容。

    # 原材料信息
    - 材料内容：{original_material}
    - 学科：{subject}
    - 年级：{grade}
    - 难度：{difficulty}

    # 克隆要求
    1. 保持相同的材料类型和长度
    2. 使用不同的具体内容
    3. 保持相同的难度和知识点要求
    4. 确保材料质量不低于原题

    # 输出要求
    1. 不要输出任何与材料内容无关的内容；
    2. 严格按照输出材料内容的要求生成材料内容；
    
    请生成新的材料内容：

  # 组合题子题目克隆提示词
  composite_subquestion_clone: |
    你是一个专业的子题目克隆专家。请基于以下原题子题目进行克隆，生成新的子题目内容。

    原子题目信息：
    - 子题目内容：{original_subquestion}
    - 题目类型：{subquestion_type}
    - 材料背景：{material_context}
    - 学科：{subject}
    - 原子题结构：{original_content}
    
    克隆要求：
    1.保持相同的题目类型和分值
    2.基于新的材料内容生成相应的子题目
    3.保持相同的知识点要求
    4.确保题目质量不低于原题
    5.严格按照结构要求生成内容
    6.生成的内容应该是完整的子题目，包含题目描述和要求

    输出试题结构：
    {mapped_structure}
    输出要求：
    1. 不要输出任何与试题结构无关的内容
    2. 严格按照输出试题结构的要求生成试题

    请生成新的子题目内容：

# 系统配置
system:
  # 日志级别
  log_level: "INFO"
  
  # 任务超时时间（秒）
  task_timeout: 300
  
  # 流式数据推送间隔（秒）
  stream_interval: 1
  
  # 最大并发任务数
  max_concurrent_tasks: 10

# 错误码配置
error_codes:
  # 业务错误码
  business:
    INVALID_QUESTION_DATA: {"code": 4001, "msg": "无效的试题数据"}
    CLONE_FAILED: {"code": 4002, "msg": "试题克隆失败"}
    MODEL_ERROR: {"code": 4003, "msg": "模型调用失败"}
    TIMEOUT_ERROR: {"code": 4004, "msg": "任务超时"}
  
  # HTTP状态码
  http:
    SUCCESS: 200
    BAD_REQUEST: 400
    UNAUTHORIZED: 401
    FORBIDDEN: 403
    NOT_FOUND: 404
    INTERNAL_ERROR: 500 