2025-07-17 17:45:24 | INFO     | __main__:lifespan:64 - 智能命题系统启动中...
2025-07-17 17:45:24 | INFO     | __main__:lifespan:65 - 系统初始化完成
2025-07-17 17:45:50 | INFO     | app.core.state_manager:create_task:90 - 创建任务: 6fd691c5-266d-ce58-16b9-22dde098ba42
2025-07-17 17:45:50 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-07-17 17:45:50 | INFO     | app.services.question_cloner:clone_question:47 - 开始克隆试题，策略: full
2025-07-17 17:45:50 | INFO     | app.services.question_cloner:_clone_composite_question:284 - 开始克隆组合题
2025-07-17 17:47:32 | INFO     | app.services.question_cloner:clone_question:120 - 试题克隆完成
2025-07-17 17:49:27 | INFO     | app.core.state_manager:create_task:90 - 创建任务: fe0da97a-7a0b-7b56-1bb2-bbd5379254d9
2025-07-17 17:49:27 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-07-17 17:49:27 | INFO     | app.utils.workflow_utils:extract_model_config_from_workflow:49 - 从WorkflowPost提取到模型配置: default
2025-07-17 17:49:37 | INFO     | app.core.state_manager:create_task:90 - 创建任务: fe0da97a-7a0b-7b56-1bb2-bbd5379254d9
2025-07-17 17:49:37 | INFO     | app.services.intelligent_question_service:generate_questions:39 - 开始智能命题
2025-07-17 17:50:13 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
2025-07-17 17:51:07 | INFO     | app.services.intelligent_question_service:generate_questions:92 - 智能命题完成
