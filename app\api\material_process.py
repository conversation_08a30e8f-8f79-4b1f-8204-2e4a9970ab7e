from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from app.models.schemas import (
    MaterialProcessRequest, MaterialProcessResponse,
    StandardResponse, ErrorResponse, FileType
)
from app.services.material_process_service import material_process_service
from app.core.state_manager import task_manager, TaskStatus
import uuid
import base64
from datetime import datetime
from loguru import logger
from typing import Optional

router = APIRouter()


@router.post("/ProcessMaterial", response_model=MaterialProcessResponse)
async def process_material(request: MaterialProcessRequest):
    """
    处理材料内容（文本或文件）
    
    功能：
    - 支持直接文本内容处理
    - 支持Base64编码的文件处理
    - 提取和清理内容
    - 返回处理后的材料
    """
    try:
        # 生成任务ID
        task_id = request.task_id or str(uuid.uuid4())
        
        # 创建任务
        await task_manager.create_task(task_id)
        
        # 更新任务状态
        await task_manager.update_task_status(
            task_id,
            TaskStatus.PROCESSING,
            step_info={
                "step": "processing_material",
                "progress": 0,
                "detail": "开始处理材料内容"
            }
        )
        
        logger.info(f"开始处理材料: {task_id}")
        
        # 调用服务处理材料
        result = await material_process_service.process_material(
            content=request.content,
            file_data=request.file_data,
            file_type=request.file_type,
            filename=request.filename,
            task_id=task_id
        )
        
        # 更新任务状态为完成
        await task_manager.update_task_status(
            task_id,
            TaskStatus.COMPLETED,
            result=result
        )
        
        # 构建响应
        response = MaterialProcessResponse(
            extracted_content=result["extracted_content"],
            original_filename=request.filename,
            file_type=request.file_type,
            content_length=len(result["extracted_content"]),
            task_id=task_id,
            processed_at=datetime.now()
        )
        
        logger.info(f"材料处理完成: {task_id}")
        return response
        
    except Exception as e:
        logger.error(f"材料处理失败: {e}")
        
        # 更新任务状态为失败
        if 'task_id' in locals():
            await task_manager.update_task_status(
                task_id,
                TaskStatus.FAILED,
                error_message=str(e)
            )
        
        raise HTTPException(
            status_code=500,
            detail=f"材料处理失败: {str(e)}"
        )


@router.post("/UploadFile", response_model=MaterialProcessResponse)
async def upload_file(
    file: UploadFile = File(...),
    task_id: Optional[str] = Form(None)
):
    """
    上传文件并处理
    
    功能：
    - 支持多种文件格式（PDF、DOC、DOCX、TXT）
    - 自动检测文件类型
    - 提取文件内容
    """
    try:
        # 生成任务ID
        task_id = task_id or str(uuid.uuid4())
        
        # 创建任务
        await task_manager.create_task(task_id)
        
        # 读取文件内容
        file_content = await file.read()
        file_data = base64.b64encode(file_content).decode('utf-8')
        
        # 检测文件类型
        file_type = _detect_file_type(file.filename)
        
        # 构建处理请求
        process_request = MaterialProcessRequest(
            content=None,
            file_data=file_data,
            file_type=file_type,
            filename=file.filename,
            task_id=task_id
        )
        
        # 调用处理接口
        return await process_material(process_request)
        
    except Exception as e:
        logger.error(f"文件上传处理失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"文件上传处理失败: {str(e)}"
        )


@router.get("/GetMaterialProcessStatus/{task_id}")
async def get_material_process_status(task_id: str):
    """
    查询材料处理任务状态
    """
    try:
        task_info = await task_manager.get_task_info(task_id)
        
        if not task_info:
            raise HTTPException(
                status_code=404,
                detail=f"任务不存在: {task_id}"
            )
        
        return StandardResponse(
            code=200,
            msg="查询成功",
            data={
                "task_id": task_id,
                "status": task_info.status.value,
                "created_at": task_info.created_at,
                "updated_at": task_info.updated_at,
                "result": task_info.result,
                "error_message": task_info.error_message
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询任务状态失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"查询任务状态失败: {str(e)}"
        )


def _detect_file_type(filename: str) -> FileType:
    """检测文件类型"""
    if not filename:
        return FileType.TXT
    
    extension = filename.lower().split('.')[-1]
    
    if extension == 'pdf':
        return FileType.PDF
    elif extension == 'doc':
        return FileType.DOC
    elif extension == 'docx':
        return FileType.DOCX
    else:
        return FileType.TXT
