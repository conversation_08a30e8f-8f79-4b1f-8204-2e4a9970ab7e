#!/usr/bin/env python3
"""
测试异步修复的脚本
验证智能命题服务的异步功能是否正常工作
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.intelligent_question_service import IntelligentQuestionService
from app.core.state_manager import TaskStatus


async def test_async_question_generation():
    """测试异步试题生成功能"""
    print("开始测试异步试题生成功能...")
    
    # 创建服务实例
    service = IntelligentQuestionService()
    
    # 模拟试题生成请求数据
    test_ques_type_post = {
        "QuesCount": 2,
        "QuesTypeName": "单选题",
        "QuesTypeId": 1,
        "Difficulty": "中等",
        "KnowledgePoints": "测试知识点"
    }
    
    test_planning_tasks = {
        "subquestion_tasks": [
            {"question_index": 1, "difficulty": "中等", "knowledge_points": "测试知识点1"},
            {"question_index": 2, "difficulty": "中等", "knowledge_points": "测试知识点2"}
        ],
        "planning_type": "basic"
    }
    
    # 状态回调函数
    async def status_callback(status, message, details=None):
        print(f"状态更新: {status.value} - {message}")
        if details:
            print(f"详情: {details}")
    
    try:
        # 测试异步生成试题
        print("\n测试 _generate_questions_async 方法...")
        start_time = asyncio.get_event_loop().time()
        
        results = await service._generate_questions_async(
            test_ques_type_post,
            "语文",
            test_planning_tasks,
            None,  # workflow_config
            None,  # stream_callback
            status_callback
        )
        
        end_time = asyncio.get_event_loop().time()
        duration = end_time - start_time
        
        print(f"\n异步生成完成，耗时: {duration:.2f}秒")
        print(f"生成结果数量: {len(results)}")
        for i, result in enumerate(results, 1):
            print(f"试题{i}: {result[:100]}..." if len(result) > 100 else f"试题{i}: {result}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_concurrent_requests():
    """测试并发请求处理能力"""
    print("\n开始测试并发请求处理能力...")
    
    service = IntelligentQuestionService()
    
    # 创建多个并发任务
    async def generate_single_task(task_id):
        test_ques_type_post = {
            "QuesCount": 1,
            "QuesTypeName": "单选题",
            "QuesTypeId": 1,
            "Difficulty": "中等",
            "KnowledgePoints": f"测试知识点{task_id}"
        }
        
        test_planning_tasks = {
            "subquestion_tasks": [
                {"question_index": 1, "difficulty": "中等", "knowledge_points": f"测试知识点{task_id}"}
            ],
            "planning_type": "basic"
        }
        
        async def status_callback(status, message, details=None):
            print(f"任务{task_id} - 状态: {status.value} - {message}")
        
        try:
            results = await service._generate_questions_async(
                test_ques_type_post,
                "语文",
                test_planning_tasks,
                None,
                None,
                status_callback
            )
            print(f"任务{task_id}完成，结果: {len(results)}个试题")
            return f"任务{task_id}成功"
        except Exception as e:
            print(f"任务{task_id}失败: {e}")
            return f"任务{task_id}失败: {e}"
    
    # 创建3个并发任务
    tasks = [generate_single_task(i) for i in range(1, 4)]
    
    start_time = asyncio.get_event_loop().time()
    results = await asyncio.gather(*tasks, return_exceptions=True)
    end_time = asyncio.get_event_loop().time()
    
    print(f"\n并发测试完成，总耗时: {end_time - start_time:.2f}秒")
    for result in results:
        print(f"结果: {result}")


async def main():
    """主测试函数"""
    print("=" * 60)
    print("智能命题服务异步功能测试")
    print("=" * 60)
    
    # 测试1: 基本异步功能
    success1 = await test_async_question_generation()
    
    # 测试2: 并发处理能力
    if success1:
        await test_concurrent_requests()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)


if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
