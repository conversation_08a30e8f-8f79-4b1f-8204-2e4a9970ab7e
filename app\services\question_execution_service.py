from typing import List, Dict, Any, Optional
import asyncio
import time
from loguru import logger
from app.models.schemas import GeneratedQuestion, DifficultyLevel
from app.services.llm_manager import generate_text
from app.core.state_manager import task_manager, TaskStatus
import uuid
import json


class QuestionExecutionService:
    """试题执行服务"""
    
    def __init__(self):
        self.logger = logger
    
    async def execute_questions(
        self,
        task_plan: List[Dict[str, Any]],
        task_id: Optional[str] = None,
        stream_response: bool = True
    ) -> Dict[str, Any]:
        """
        执行试题生成任务
        
        Args:
            task_plan: 任务规划列表
            task_id: 任务ID
            stream_response: 是否流式响应
            
        Returns:
            执行结果
        """
        try:
            self.logger.info(f"开始执行试题生成: {len(task_plan)} 个任务")
            
            start_time = time.time()
            all_questions = []
            execution_summary = {
                "total_tasks": len(task_plan),
                "completed_tasks": 0,
                "failed_tasks": 0,
                "total_questions": 0,
                "execution_details": []
            }
            
            # 逐个执行任务
            for i, task in enumerate(task_plan):
                try:
                    # 更新任务状态
                    if task_id:
                        progress = int((i / len(task_plan)) * 100)
                        await task_manager.update_task_status(
                            task_id,
                            TaskStatus.PROCESSING,
                            step_info={
                                "step": f"executing_task_{i+1}",
                                "progress": progress,
                                "detail": f"执行任务 {i+1}/{len(task_plan)}: {task.get('question_type', '未知题型')}"
                            }
                        )
                    
                    # 执行单个任务
                    task_result = await self._execute_single_task(task, task_id)
                    
                    all_questions.extend(task_result["questions"])
                    execution_summary["completed_tasks"] += 1
                    execution_summary["total_questions"] += len(task_result["questions"])
                    
                    execution_summary["execution_details"].append({
                        "task_id": task.get("task_id", f"task_{i+1}"),
                        "question_type": task.get("question_type", "未知"),
                        "quantity": len(task_result["questions"]),
                        "status": "completed",
                        "execution_time": task_result.get("execution_time", 0)
                    })
                    
                except Exception as e:
                    self.logger.error(f"任务执行失败 {i+1}: {e}")
                    execution_summary["failed_tasks"] += 1
                    
                    execution_summary["execution_details"].append({
                        "task_id": task.get("task_id", f"task_{i+1}"),
                        "question_type": task.get("question_type", "未知"),
                        "quantity": 0,
                        "status": "failed",
                        "error": str(e)
                    })
            
            # 计算总执行时间
            total_time = time.time() - start_time
            execution_summary["total_execution_time"] = total_time
            
            # 最终状态更新
            if task_id:
                await task_manager.update_task_status(
                    task_id,
                    TaskStatus.PROCESSING,
                    step_info={
                        "step": "completed",
                        "progress": 100,
                        "detail": f"执行完成，生成 {len(all_questions)} 题"
                    }
                )
            
            result = {
                "questions": all_questions,
                "execution_summary": execution_summary
            }
            
            self.logger.info(f"试题执行完成: 生成 {len(all_questions)} 题，耗时 {total_time:.2f} 秒")
            return result
            
        except Exception as e:
            self.logger.error(f"试题执行失败: {e}")
            
            # 更新任务状态为失败
            if task_id:
                await task_manager.update_task_status(
                    task_id,
                    TaskStatus.FAILED,
                    error_message=str(e)
                )
            
            raise
    
    async def _execute_single_task(
        self,
        task: Dict[str, Any],
        parent_task_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """执行单个任务"""
        
        start_time = time.time()
        
        question_type = task.get("question_type", "单选题")
        quantity = task.get("quantity", 1)
        difficulty = task.get("difficulty", "中")
        knowledge_points = task.get("knowledge_points", [])
        special_requirements = task.get("special_requirements", "")
        
        self.logger.info(f"执行单个任务: {question_type} x {quantity}")
        
        questions = []
        
        # 并发生成试题
        if quantity > 1:
            # 创建并发任务
            tasks = []
            for i in range(quantity):
                task_coroutine = self._generate_single_question(
                    question_type=question_type,
                    difficulty=difficulty,
                    knowledge_points=knowledge_points,
                    special_requirements=special_requirements,
                    question_index=i+1
                )
                tasks.append(task_coroutine)
            
            # 并发执行
            question_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            for i, result in enumerate(question_results):
                if isinstance(result, Exception):
                    self.logger.error(f"试题 {i+1} 生成失败: {result}")
                else:
                    questions.append(result)
        else:
            # 单个试题生成
            question = await self._generate_single_question(
                question_type=question_type,
                difficulty=difficulty,
                knowledge_points=knowledge_points,
                special_requirements=special_requirements,
                question_index=1
            )
            questions.append(question)
        
        execution_time = time.time() - start_time
        
        return {
            "questions": questions,
            "execution_time": execution_time
        }
    
    async def _generate_single_question(
        self,
        question_type: str,
        difficulty: str,
        knowledge_points: List[str],
        special_requirements: str,
        question_index: int
    ) -> GeneratedQuestion:
        """生成单个试题"""
        
        # 构建生成提示词
        prompt = self._build_question_prompt(
            question_type, difficulty, knowledge_points, special_requirements
        )
        
        # 调用LLM生成
        result = await generate_text(prompt, temperature=0.7)
        
        # 解析生成结果
        question = self._parse_question_result(
            result, question_type, difficulty, knowledge_points, question_index
        )
        
        return question
    
    def _build_question_prompt(
        self,
        question_type: str,
        difficulty: str,
        knowledge_points: List[str],
        special_requirements: str
    ) -> str:
        """构建试题生成提示词"""
        
        knowledge_str = "、".join(knowledge_points) if knowledge_points else "相关知识"
        
        prompt = f"""请生成一道{question_type}，要求如下：

题型：{question_type}
难度：{difficulty}
知识点：{knowledge_str}
特殊要求：{special_requirements or "无"}

请按以下JSON格式返回：
{{
    "question_content": "题目内容",
    "options": ["选项A", "选项B", "选项C", "选项D"],  // 仅选择题需要
    "answer": "正确答案",
    "explanation": "答案解析"
}}

注意：
1. 题目内容要准确、清晰
2. 选项要合理、有区分度
3. 答案要正确
4. 解析要详细、有说服力"""
        
        return prompt
    
    def _parse_question_result(
        self,
        result: str,
        question_type: str,
        difficulty: str,
        knowledge_points: List[str],
        question_index: int
    ) -> GeneratedQuestion:
        """解析试题生成结果"""
        
        try:
            # 尝试解析JSON
            data = json.loads(result)
            
            question = GeneratedQuestion(
                question_id=str(uuid.uuid4()),
                question_type=question_type,
                question_content=data.get("question_content", "题目生成失败"),
                options=data.get("options") if question_type in ["单选题", "多选题"] else None,
                answer=data.get("answer", "答案生成失败"),
                explanation=data.get("explanation", "解析生成失败"),
                difficulty=DifficultyLevel(difficulty) if difficulty in ["易", "中", "难"] else DifficultyLevel.MEDIUM,
                knowledge_points=knowledge_points,
                metadata={
                    "generation_time": time.time(),
                    "question_index": question_index
                }
            )
            
            return question
            
        except json.JSONDecodeError:
            # JSON解析失败，创建默认试题
            self.logger.error(f"试题结果解析失败: {result[:100]}...")
            
            return GeneratedQuestion(
                question_id=str(uuid.uuid4()),
                question_type=question_type,
                question_content=f"【{question_type}】题目生成失败，请重试",
                options=["选项A", "选项B", "选项C", "选项D"] if question_type in ["单选题", "多选题"] else None,
                answer="生成失败",
                explanation="解析生成失败",
                difficulty=DifficultyLevel.MEDIUM,
                knowledge_points=knowledge_points,
                metadata={
                    "generation_time": time.time(),
                    "question_index": question_index,
                    "error": "解析失败"
                }
            )
    
    async def batch_execute_questions(
        self,
        task_plans: List[List[Dict[str, Any]]],
        batch_id: str,
        batch_size: int = 5,
        parallel_processing: bool = True
    ) -> Dict[str, Any]:
        """批量执行试题生成"""
        
        # 简化实现，逐个处理
        results = []
        successful_tasks = 0
        failed_tasks = 0
        total_questions = 0
        
        start_time = time.time()
        
        for i, task_plan in enumerate(task_plans):
            try:
                result = await self.execute_questions(task_plan)
                results.append(result)
                successful_tasks += 1
                total_questions += len(result["questions"])
            except Exception as e:
                self.logger.error(f"批量任务 {i+1} 失败: {e}")
                failed_tasks += 1
                results.append({"error": str(e)})
        
        execution_time = time.time() - start_time
        
        return {
            "successful_tasks": successful_tasks,
            "failed_tasks": failed_tasks,
            "total_questions": total_questions,
            "execution_time": execution_time,
            "results": results
        }
    
    async def preview_questions(
        self,
        task_plan: Dict[str, Any],
        preview_count: int = 3,
        task_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """预览试题生成"""
        
        start_time = time.time()
        
        # 限制预览数量
        limited_task = task_plan.copy()
        limited_task["quantity"] = min(preview_count, task_plan.get("quantity", 1))
        
        # 执行预览生成
        result = await self._execute_single_task(limited_task, task_id)
        
        generation_time = time.time() - start_time
        
        return {
            "preview_questions": result["questions"],
            "generation_time": generation_time,
            "quality_assessment": {
                "generated_count": len(result["questions"]),
                "success_rate": len(result["questions"]) / limited_task["quantity"] if limited_task["quantity"] > 0 else 0
            }
        }


# 创建全局服务实例
question_execution_service = QuestionExecutionService()
