"""
错误码定义
统一管理系统的错误码和错误信息
"""

from enum import Enum
from typing import Dict, Any


class ErrorCode(Enum):
    """错误码枚举"""
    # 成功
    SUCCESS = 200

    # 客户端错误 (4xx)
    BAD_REQUEST = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    METHOD_NOT_ALLOWED = 405
    REQUEST_TIMEOUT = 408
    CONFLICT = 409
    UNPROCESSABLE_ENTITY = 422
    TOO_MANY_REQUESTS = 429

    # 服务器错误 (5xx)
    INTERNAL_SERVER_ERROR = 500
    NOT_IMPLEMENTED = 501
    BAD_GATEWAY = 502
    SERVICE_UNAVAILABLE = 503
    GATEWAY_TIMEOUT = 504

    # 业务错误码 (自定义)
    INVALID_QUESTION_DATA = 4001
    CLONE_FAILED = 4002
    MODEL_ERROR = 4003
    TIMEOUT_ERROR = 4004
    TASK_NOT_FOUND = 4005
    STREAM_ERROR = 4006
    WORKFLOW_ERROR = 4007


class BusinessError(Exception):
    """业务错误基类"""

    def __init__(self, error_code: ErrorCode, message: str = None, details: Dict[str, Any] = None):
        self.error_code = error_code
        self.message = message or self._get_default_message(error_code)
        self.details = details or {}
        super().__init__(self.message)

    def _get_default_message(self, error_code: ErrorCode) -> str:
        """获取默认错误消息"""
        default_messages = {
            ErrorCode.INVALID_QUESTION_DATA: "无效的试题数据",
            ErrorCode.CLONE_FAILED: "试题克隆失败",
            ErrorCode.MODEL_ERROR: "模型调用失败",
            ErrorCode.TIMEOUT_ERROR: "任务超时",
            ErrorCode.TASK_NOT_FOUND: "任务不存在",
            ErrorCode.STREAM_ERROR: "流式输出错误",
            ErrorCode.WORKFLOW_ERROR: "工作流配置错误"
        }
        return default_messages.get(error_code, "未知错误")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "code": self.error_code.value,
            "message": self.message,
            "details": self.details
        }


class ValidationError(BusinessError):
    """数据验证错误"""

    def __init__(self, message: str = "数据验证失败", details: Dict[str, Any] = None):
        super().__init__(ErrorCode.INVALID_QUESTION_DATA, message, details)


class CloneError(BusinessError):
    """克隆错误"""

    def __init__(self, message: str = "试题克隆失败", details: Dict[str, Any] = None):
        super().__init__(ErrorCode.CLONE_FAILED, message, details)


class ModelError(BusinessError):
    """模型错误"""

    def __init__(self, message: str = "模型调用失败", details: Dict[str, Any] = None):
        super().__init__(ErrorCode.MODEL_ERROR, message, details)


class TaskNotFoundError(BusinessError):
    """任务不存在错误"""

    def __init__(self, task_id: str):
        super().__init__(ErrorCode.TASK_NOT_FOUND, f"任务不存在: {task_id}")


class TimeoutError(BusinessError):
    """超时错误"""

    def __init__(self, message: str = "任务超时", details: Dict[str, Any] = None):
        super().__init__(ErrorCode.TIMEOUT_ERROR, message, details)


class StreamError(BusinessError):
    """流式输出错误"""

    def __init__(self, message: str = "流式输出错误", details: Dict[str, Any] = None):
        super().__init__(ErrorCode.STREAM_ERROR, message, details)


class WorkflowError(BusinessError):
    """工作流错误"""

    def __init__(self, message: str = "工作流配置错误", details: Dict[str, Any] = None):
        super().__init__(ErrorCode.WORKFLOW_ERROR, message, details)


# 错误码映射表
ERROR_CODE_MAP = {
    ErrorCode.SUCCESS: {"code": 200, "message": "成功"},
    ErrorCode.BAD_REQUEST: {"code": 400, "message": "请求参数错误"},
    ErrorCode.UNAUTHORIZED: {"code": 401, "message": "未授权"},
    ErrorCode.FORBIDDEN: {"code": 403, "message": "禁止访问"},
    ErrorCode.NOT_FOUND: {"code": 404, "message": "资源不存在"},
    ErrorCode.METHOD_NOT_ALLOWED: {"code": 405, "message": "方法不允许"},
    ErrorCode.REQUEST_TIMEOUT: {"code": 408, "message": "请求超时"},
    ErrorCode.CONFLICT: {"code": 409, "message": "资源冲突"},
    ErrorCode.UNPROCESSABLE_ENTITY: {"code": 422, "message": "请求实体无法处理"},
    ErrorCode.TOO_MANY_REQUESTS: {"code": 429, "message": "请求过于频繁"},
    ErrorCode.INTERNAL_SERVER_ERROR: {"code": 500, "message": "服务器内部错误"},
    ErrorCode.NOT_IMPLEMENTED: {"code": 501, "message": "功能未实现"},
    ErrorCode.BAD_GATEWAY: {"code": 502, "message": "网关错误"},
    ErrorCode.SERVICE_UNAVAILABLE: {"code": 503, "message": "服务不可用"},
    ErrorCode.GATEWAY_TIMEOUT: {"code": 504, "message": "网关超时"},
    ErrorCode.INVALID_QUESTION_DATA: {"code": 4001, "message": "无效的试题数据"},
    ErrorCode.CLONE_FAILED: {"code": 4002, "message": "试题克隆失败"},
    ErrorCode.MODEL_ERROR: {"code": 4003, "message": "模型调用失败"},
    ErrorCode.TIMEOUT_ERROR: {"code": 4004, "message": "任务超时"},
    ErrorCode.TASK_NOT_FOUND: {"code": 4005, "message": "任务不存在"},
    ErrorCode.STREAM_ERROR: {"code": 4006, "message": "流式输出错误"},
    ErrorCode.WORKFLOW_ERROR: {"code": 4007, "message": "工作流配置错误"}
}


def get_error_response(error_code: ErrorCode, message: str = None, details: Dict[str, Any] = None) -> Dict[str, Any]:
    """获取标准错误响应"""
    error_info = ERROR_CODE_MAP.get(
        error_code, {"code": 500, "message": "未知错误"})

    return {
        "code": error_info["code"],
        "message": message or error_info["message"],
        "details": details or {},
        "timestamp": "2024-01-01T12:00:00Z"
    }


def handle_business_error(error: BusinessError) -> Dict[str, Any]:
    """处理业务错误"""
    return {
        "code": error.error_code.value,
        "message": error.message,
        "details": error.details,
        "timestamp": "2024-01-01T12:00:00Z"
    }
