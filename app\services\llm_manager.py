"""
LLM模型管理器
支持多种模型的统一调用接口
"""

import asyncio
import aiohttp
import json
from typing import Dict, Any, Optional, List, Callable
from loguru import logger
from langchain_openai import ChatOpenAI
from langchain_deepseek import ChatDeepSeek
from langchain_core.callbacks import BaseCallbackHandler
from app.utils.config_manager import config_manager, get_model_service_mode


class StreamingCallbackHandler(BaseCallbackHandler):
    """流式回调处理器"""

    def __init__(self):
        self.content = ""
        self.reasoning_content = ""

    def on_llm_new_token(self, token: str, **kwargs) -> None:
        """处理新的token"""
        self.content += token

    def on_llm_end(self, response, **kwargs) -> None:
        """处理LLM结束"""
        pass


class LLMManager:
    """LLM模型管理器"""

    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.default_model = config_manager.get(
            "models.default", "deepseek-r1")
        # 添加LLM实例缓存，避免重复创建
        self._llm_cache: Dict[str, Any] = {}

    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
        # 清理LLM缓存
        self._clear_llm_cache()

    async def generate_text(self, prompt: str, model_name: Optional[str] = None,
                            api_key: Optional[str] = None, api_base: Optional[str] = None,
                            **kwargs) -> str:
        """
        生成文本 - 使用langchain方法，stream=False

        Args:
            prompt: 提示词
            model_name: 模型名称
            **kwargs: 其他参数

        Returns:
            生成的文本内容
        """
        try:
            if not model_name:
                model_name = self.default_model

            # 优先使用传入的API配置，否则使用配置文件中的配置
            if api_key and api_base:
                # 使用workflow中的配置
                # 只提取支持的参数，避免传递不支持的参数
                supported_params = ['max_tokens', 'temperature']
                model_config = {
                    "name": model_name,
                    "api_key": api_key,
                    "api_base": api_base
                }
                # 只添加支持的参数
                for param in supported_params:
                    if param in kwargs:
                        model_config[param] = kwargs[param]
                    else:
                        # 设置默认值
                        if param == 'max_tokens':
                            model_config[param] = 4000
                        elif param == 'temperature':
                            model_config[param] = 0.7
            else:
                # 使用配置文件中的配置
                model_config = config_manager.get_model_config(model_name)
                if not model_config:
                    raise ValueError(f"未找到模型配置: {model_name}")

            # 使用langchain方法进行非流式调用
            return await self._call_langchain_api(prompt, model_config, stream=False, **kwargs)

        except Exception as e:
            logger.error(f"LLM调用失败: {e}")
            raise

    async def generate_text_with_stream(self, prompt: str, model_name: Optional[str] = None,
                                        stream_callback: Optional[Callable] = None,
                                        api_key: Optional[str] = None, api_base: Optional[str] = None,
                                        **kwargs):
        """
        流式生成文本（生成器），支持思考过程提取

        Args:
            prompt: 提示词
            model_name: 模型名称
            stream_callback: 流式回调函数，用于推送思考过程
            **kwargs: 其他参数

        Yields:
            生成的文本片段
        """
        try:
            if not model_name:
                model_name = self.default_model

            # 优先使用传入的API配置，否则使用配置文件中的配置
            if api_key and api_base:
                # 使用workflow中的配置
                # 只提取支持的参数，避免传递不支持的参数
                supported_params = ['max_tokens', 'temperature']
                model_config = {
                    "name": model_name,
                    "api_key": api_key,
                    "api_base": api_base
                }
                # 只添加支持的参数
                for param in supported_params:
                    if param in kwargs:
                        model_config[param] = kwargs[param]
                    else:
                        # 设置默认值
                        if param == 'max_tokens':
                            model_config[param] = 4000
                        elif param == 'temperature':
                            model_config[param] = 0.7
            else:
                # 使用配置文件中的配置
                model_config = config_manager.get_model_config(model_name)
                if not model_config:
                    raise ValueError(f"未找到模型配置: {model_name}")

            # 初始化状态变量
            reasoning_content = ""
            done_content = ""
            in_reasoning = False
            reasoning_buffer = ""

            # 统一使用langchain方法进行流式处理
            if "deepseek" in model_name.lower():
                # DeepSeek模型使用原有的推理过程处理（保持兼容性）
                async for chunk in self._call_deepseek_stream_api(prompt, model_config, stream_callback, **kwargs):
                    yield chunk
            else:
                # 其他模型使用通用的langchain流式处理
                async for chunk in self._call_generic_stream_api(prompt, model_config, stream_callback, reasoning_content, done_content, in_reasoning, reasoning_buffer, **kwargs):
                    yield chunk

        except Exception as e:
            logger.error(f"LLM流式调用失败: {e}")
            raise

    async def _call_langchain_api(self, prompt: str, model_config: Dict[str, Any],
                                  stream: bool = False, **kwargs) -> str:
        """
        使用langchain调用LLM API - 统一的调用方法

        Args:
            prompt: 提示词
            model_config: 模型配置
            stream: 是否流式调用
            **kwargs: 其他参数

        Returns:
            生成的文本内容
        """
        try:
            # 为Qwen3模型在非流式调用时设置推理模式
            model_name = model_config.get("name", "").lower()
            if "qwen3" in model_name and not stream:
                # 非流式调用时，为Qwen3模型关闭推理模式
                kwargs["enable_thinking"] = False
                logger.debug(
                    f"为Qwen3模型在非流式调用时关闭推理模式: {model_config.get('name')}")

            # 获取或创建LLM实例（使用缓存优化）
            llm = self._get_cached_llm_instance(model_config, **kwargs)

            # 准备消息
            messages = [{"role": "user", "content": prompt}]

            if stream:
                # 流式调用
                response_gen = llm.stream(messages)
                result = ""
                for chunk in response_gen:
                    content = chunk.content if hasattr(
                        chunk, 'content') else str(chunk)
                    result += content
                return result
            else:
                # 非流式调用 - 支持配置控制使用 ainvoke 或 invoke
                from app.utils.config_manager import config_manager
                use_ainvoke = config_manager.get(
                    "model_service.use_ainvoke", True)

                try:
                    if use_ainvoke and hasattr(llm, 'ainvoke'):
                        # 使用 ainvoke 异步方法
                        response = await llm.ainvoke(messages)
                        logger.debug("使用 ainvoke 异步方法调用成功")
                    elif hasattr(llm, 'invoke'):
                        # 使用 invoke 同步方法
                        response = llm.invoke(messages)
                        if use_ainvoke:
                            logger.warning(
                                f"模型 {model_config.get('name')} 不支持 ainvoke，降级使用 invoke")
                        else:
                            logger.debug("根据配置使用 invoke 同步方法")
                    else:
                        raise AttributeError(f"模型实例既不支持 ainvoke 也不支持 invoke")

                    return response.content if hasattr(response, 'content') else str(response)

                except Exception as e:
                    # 如果 ainvoke 失败且配置允许，尝试降级到 invoke
                    if use_ainvoke and hasattr(llm, 'ainvoke') and hasattr(llm, 'invoke'):
                        logger.warning(f"ainvoke 调用失败，降级使用 invoke: {e}")
                        try:
                            response = llm.invoke(messages)
                            return response.content if hasattr(response, 'content') else str(response)
                        except Exception as fallback_error:
                            logger.error(f"invoke 降级调用也失败: {fallback_error}")
                            raise
                    else:
                        raise

        except Exception as e:
            logger.error(f"LangChain API调用失败: {e}")
            raise

    async def _call_deepseek_stream_api(self, prompt: str, model_config: Dict[str, Any],
                                        stream_callback: Optional[Callable] = None, **kwargs):
        """调用DeepSeek流式API"""
        if not self.session:
            raise RuntimeError("LLM管理器未初始化，请使用异步上下文管理器")

        url = f"{model_config['api_base']}/chat/completions"
        headers = {
            "Authorization": f"Bearer {model_config['api_key']}",
            "Content-Type": "application/json"
        }

        data = {
            "model": model_config["name"],
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": kwargs.get("max_tokens", model_config.get("max_tokens", 4000)),
            "temperature": kwargs.get("temperature", model_config.get("temperature", 0.7)),
            "stream": True
        }

        accumulated_content = ""
        reasoning_content = ""
        done_content = ""
        in_reasoning = False
        reasoning_buffer = ""

        # 减少初始等待时间，提高响应速度
        logger.debug("开始DeepSeek流式API调用")  # 改为DEBUG级别
        # 移除不必要的等待时间

        async with self.session.post(url, headers=headers, json=data) as response:
            if response.status != 200:
                error_text = await response.text()
                raise Exception(
                    f"DeepSeek流式API调用失败: {response.status} - {error_text}")

            logger.debug("DeepSeek流式连接建立成功，开始接收数据...")  # 改为DEBUG级别
            async for line in response.content:
                line = line.decode('utf-8').strip()
                if line.startswith('data: '):
                    data_str = line[6:]
                    if data_str == '[DONE]':
                        logger.debug("收到DeepSeek流式完成标记")  # 改为DEBUG级别
                        break

                    try:
                        chunk_data = json.loads(data_str)
                        if 'choices' in chunk_data and chunk_data['choices']:
                            delta = chunk_data['choices'][0].get('delta', {})
                            if 'content' in delta:
                                content_chunk = delta['content']
                                accumulated_content += content_chunk

                                # 处理推理内容（DeepSeek特有）
                                if hasattr(chunk_data, 'additional_kwargs') and 'reasoning_content' in chunk_data.get('additional_kwargs', {}):
                                    reasoning_delta = chunk_data['additional_kwargs'].get(
                                        'reasoning_content', '')
                                    if reasoning_delta:
                                        reasoning_content += reasoning_delta
                                        if stream_callback:
                                            # 立即推送，不等待
                                            await stream_callback({
                                                "type": "thinking",
                                                "content": reasoning_delta,
                                                "step": "reasoning",
                                                "accumulated": reasoning_content,
                                                "timestamp": asyncio.get_event_loop().time()
                                            })
                                            # 减少等待时间，提高响应速度
                                            await asyncio.sleep(0.005)
                                        yield reasoning_delta
                                        continue

                                # 处理<think></think>标签
                                if "<think>" in content_chunk:
                                    in_reasoning = True
                                    content_parts = content_chunk.split(
                                        "<think>")
                                    if len(content_parts) > 1:
                                        done_content += content_parts[0]
                                        reasoning_buffer = content_parts[1]
                                    continue

                                if "</think>" in content_chunk and in_reasoning:
                                    in_reasoning = False
                                    content_parts = content_chunk.split(
                                        "</think>")
                                    reasoning_buffer += content_parts[0]
                                    if reasoning_buffer:
                                        reasoning_content += reasoning_buffer
                                        if stream_callback:
                                            # 立即推送，不等待
                                            await stream_callback({
                                                "type": "thinking",
                                                "content": reasoning_buffer,
                                                "step": "thinking_complete",
                                                "accumulated": reasoning_content,
                                                "timestamp": asyncio.get_event_loop().time()
                                            })
                                            # 减少等待时间，提高响应速度
                                            await asyncio.sleep(0.005)
                                        yield reasoning_buffer
                                        reasoning_buffer = ""
                                    if len(content_parts) > 1:
                                        done_content += content_parts[1]
                                    continue

                                if in_reasoning:
                                    reasoning_buffer += content_chunk
                                    if stream_callback:
                                        # 立即推送，不等待
                                        await stream_callback({
                                            "type": "thinking",
                                            "content": content_chunk,
                                            "step": "thinking",
                                            "accumulated": reasoning_content + reasoning_buffer,
                                            "timestamp": asyncio.get_event_loop().time()
                                        })
                                        # 减少等待时间，提高响应速度
                                        await asyncio.sleep(0.005)
                                    yield content_chunk
                                    continue

                                # 处理普通内容
                                done_content += content_chunk
                                yield content_chunk

                    except json.JSONDecodeError:
                        continue

            # 确保所有推理内容都被推送
            if reasoning_buffer and stream_callback:
                logger.debug("推送剩余的推理内容...")  # 改为DEBUG级别
                # await asyncio.sleep(0.1)  # 等待一小段时间
                await stream_callback({
                    "type": "thinking",
                    "content": reasoning_buffer,
                    "step": "thinking_complete",
                    "accumulated": reasoning_content + reasoning_buffer,
                    "timestamp": asyncio.get_event_loop().time()
                })
                logger.debug("剩余推理内容推送完成")  # 改为DEBUG级别

    async def _call_generic_stream_api(self, prompt: str, model_config: Dict[str, Any],
                                       stream_callback: Optional[Callable], reasoning_content: str,
                                       done_content: str, in_reasoning: bool, reasoning_buffer: str, **kwargs):
        """
        通用模型的流式API调用，支持思考过程提取
        """
        try:
            # 使用LangChain的流式调用（使用缓存优化）
            llm = self._get_cached_llm_instance(model_config, **kwargs)

            # 设置流式回调处理器
            streaming_handler = StreamingCallbackHandler()
            original_callbacks = []
            if hasattr(llm, 'callbacks'):
                original_callbacks = llm.callbacks
            llm.callbacks = [streaming_handler]

            # 准备消息
            messages = [{"role": "user", "content": prompt}]

            try:
                response_gen = llm.stream(messages)

                # 使用普通for循环，因为llm.stream返回的是普通生成器
                for chunk in response_gen:
                    # print("origin_chunk", chunk)
                    # 处理推理内容（DeepSeek特有）
                    if hasattr(chunk, 'additional_kwargs') and 'reasoning_content' in chunk.additional_kwargs:
                        reasoning_delta = chunk.additional_kwargs.get(
                            'reasoning_content', '')
                        if reasoning_delta:
                            reasoning_content += reasoning_delta
                            if stream_callback:
                                # 立即推送，不等待
                                await stream_callback({
                                    "type": "thinking",
                                    "content": reasoning_delta,
                                    "step": "reasoning",
                                    "accumulated": reasoning_content,
                                    "timestamp": asyncio.get_event_loop().time()
                                })
                            yield reasoning_delta
                            continue

                    # 处理Qwen3推理内容
                    if hasattr(chunk, 'additional_kwargs') and 'thinking' in chunk.additional_kwargs:
                        thinking_delta = chunk.additional_kwargs.get(
                            'thinking', '')
                        if thinking_delta:
                            reasoning_content += thinking_delta
                            if stream_callback:
                                # 立即推送，不等待
                                await stream_callback({
                                    "type": "thinking",
                                    "content": thinking_delta,
                                    "step": "qwen_thinking",
                                    "accumulated": reasoning_content,
                                    "timestamp": asyncio.get_event_loop().time()
                                })
                            yield thinking_delta
                            continue

                    # 获取内容
                    content = chunk.content if hasattr(
                        chunk, 'content') else str(chunk)
                    if not content:
                        continue

                    # 处理<think></think>标签
                    if "<think>" in content:
                        in_reasoning = True
                        content_parts = content.split("<think>")
                        if len(content_parts) > 1:
                            done_content += content_parts[0]
                            reasoning_buffer = content_parts[1]
                        continue

                    if "</think>" in content and in_reasoning:
                        in_reasoning = False
                        content_parts = content.split("</think>")
                        reasoning_buffer += content_parts[0]
                        if reasoning_buffer:
                            reasoning_content += reasoning_buffer
                            if stream_callback:
                                # 立即推送，不等待
                                await stream_callback({
                                    "type": "thinking",
                                    "content": reasoning_buffer,
                                    "step": "thinking_complete",
                                    "accumulated": reasoning_content,
                                    "timestamp": asyncio.get_event_loop().time()
                                })
                            yield reasoning_buffer
                            reasoning_buffer = ""
                        if len(content_parts) > 1:
                            done_content += content_parts[1]
                        continue

                    if in_reasoning:
                        reasoning_buffer += content
                        if stream_callback:
                            # 立即推送，不等待
                            await stream_callback({
                                "type": "thinking",
                                "content": content,
                                "step": "thinking",
                                "accumulated": reasoning_content + reasoning_buffer,
                                "timestamp": asyncio.get_event_loop().time()
                            })
                        yield content
                        continue

                    # 处理普通内容
                    done_content += content
                    # 对于普通内容，不推送到流式回调，只yield
                    yield content

                # 确保所有推理内容都被推送
                if reasoning_buffer and stream_callback:
                    await stream_callback({
                        "type": "thinking",
                        "content": reasoning_buffer,
                        "step": "thinking_complete",
                        "accumulated": reasoning_content + reasoning_buffer,
                        "timestamp": asyncio.get_event_loop().time()
                    })

            finally:
                # 恢复原始callbacks
                try:
                    llm.callbacks = original_callbacks
                except Exception as e:
                    logger.warning(f"恢复callbacks时出错：{str(e)}")

        except Exception as e:
            logger.error(f"通用流式API调用失败: {e}")
            raise

    def _get_cached_llm_instance(self, model_config: Dict[str, Any], **kwargs) -> Any:
        """
        获取缓存的LLM实例，如果不存在则创建新实例

        Args:
            model_config: 模型配置
            **kwargs: 其他参数

        Returns:
            LLM实例
        """
        # 生成缓存键，包含关键配置信息
        cache_key = self._generate_cache_key(model_config, **kwargs)

        # 检查缓存
        if cache_key in self._llm_cache:
            logger.debug(f"使用缓存的LLM实例: {cache_key}")
            return self._llm_cache[cache_key]

        # 创建新实例
        logger.debug(f"创建新的LLM实例: {cache_key}")
        llm_instance = self._create_llm_instance(model_config, **kwargs)

        # 缓存实例（限制缓存大小，避免内存泄漏）
        if len(self._llm_cache) >= 10:  # 最多缓存10个实例
            # 移除最旧的缓存项
            oldest_key = next(iter(self._llm_cache))
            del self._llm_cache[oldest_key]
            logger.debug(f"移除最旧的缓存实例: {oldest_key}")

        self._llm_cache[cache_key] = llm_instance
        return llm_instance

    def _generate_cache_key(self, model_config: Dict[str, Any], **kwargs) -> str:
        """
        生成缓存键

        Args:
            model_config: 模型配置
            **kwargs: 其他参数

        Returns:
            缓存键字符串
        """
        # 提取关键配置信息用于生成缓存键
        key_parts = [
            model_config.get("name", ""),
            model_config.get("api_base", ""),
            str(model_config.get("temperature", 0.7)),
            str(model_config.get("max_tokens", 4000)),
            str(kwargs.get("enable_thinking", False))
        ]
        return "|".join(key_parts)

    def _clear_llm_cache(self):
        """清理LLM缓存"""
        if self._llm_cache:
            logger.debug(f"清理LLM缓存，共{len(self._llm_cache)}个实例")
            self._llm_cache.clear()

    def _create_llm_instance(self, model_config: Dict[str, Any], **kwargs) -> Any:
        """
        创建LLM实例
        """
        # 添加调试日志
        logger.debug(f"创建LLM实例，接收到的kwargs: {kwargs}")

        # 过滤掉可能冲突的参数，避免重复传递
        # 包括模型特定参数和可能不被支持的参数
        excluded_params = [
            'temperature', 'max_tokens', 'model', 'api_key', 'base_url',
            'top_k', 'top_p', 'frequency_penalty', 'presence_penalty',
            'stop', 'n', 'logit_bias', 'user', "enable_thinking",
            # 常见拼写错误的参数名
            "enalbe_thinking", "enable_thinkng", "thinking_enable"
        ]
        filtered_kwargs = {k: v for k,
                           v in kwargs.items() if k not in excluded_params}

        # 添加调试日志
        logger.debug(f"过滤后的kwargs: {filtered_kwargs}")

        # 检查是否有未过滤的thinking相关参数
        thinking_params = [
            k for k in filtered_kwargs.keys() if 'thinking' in k.lower()]
        if thinking_params:
            logger.warning(f"发现未过滤的thinking相关参数: {thinking_params}")
            # 进一步过滤
            filtered_kwargs = {
                k: v for k, v in filtered_kwargs.items() if 'thinking' not in k.lower()}

        # 根据配置文件判断是否为离线模式
        mode = get_model_service_mode()
        model_name = model_config.get("name", "").lower()

        if mode == "offline":
            # 离线模式一律用ChatDeepSeek
            return ChatDeepSeek(
                model=model_config.get("model_name", "deepseek-chat"),
                temperature=model_config.get("temperature", 0.7),
                max_tokens=model_config.get("max_tokens", 2048),
                api_key=model_config.get("api_key"),
                api_base=model_config.get("api_base"),
                **filtered_kwargs
            )

        # 在线模式下按原有逻辑
        if "gpt" in model_name or "openai" in model_name:
            return ChatOpenAI(
                model=model_config.get("model_name", "gpt-3.5-turbo"),
                temperature=model_config.get("temperature", 0.7),
                max_tokens=model_config.get("max_tokens", 2048),
                api_key=model_config.get("api_key"),
                base_url=model_config.get("api_base"),
                **filtered_kwargs
            )
        elif "qwen3" in model_name:
            # 检查是否启用推理功能
            enable_thinking = kwargs.get("enable_thinking", False)

            # 构建extra_body参数
            extra_body = {}
            if enable_thinking:
                extra_body = {"chat_template_kwargs": {
                    "enable_thinking": True}}
            else:
                extra_body = {"chat_template_kwargs": {
                    "enable_thinking": False}}

            # 添加调试日志
            logger.debug(
                f"为Qwen3创建ChatDeepSeek实例，filtered_kwargs: {filtered_kwargs}")

            return ChatDeepSeek(
                model=model_config.get("model_name", "qwen-turbo"),
                temperature=model_config.get("temperature", 0.7),
                max_tokens=model_config.get("max_tokens", 2048),
                api_key=model_config.get("api_key"),
                extra_body=extra_body,
                api_base=model_config.get("api_base"),
                **filtered_kwargs
            )
        else:
            # 默认使用ChatDeepSeek
            return ChatDeepSeek(
                model=model_config.get("model_name", "gpt-3.5-turbo"),
                temperature=model_config.get("temperature", 0.7),
                max_tokens=model_config.get("max_tokens", 2048),
                api_key=model_config.get("api_key"),
                api_base=model_config.get("api_base"),
                **filtered_kwargs
            )


# 便捷函数
async def generate_text(prompt: str, model_name: Optional[str] = None,
                        api_key: Optional[str] = None, api_base: Optional[str] = None, **kwargs) -> str:
    """便捷函数：生成文本"""
    async with LLMManager() as llm:
        return await llm.generate_text(prompt, model_name, api_key, api_base, **kwargs)


async def generate_text_stream(prompt: str, model_name: Optional[str] = None,
                               stream_callback: Optional[Callable] = None,
                               api_key: Optional[str] = None, api_base: Optional[str] = None, **kwargs):
    """便捷函数：流式生成文本"""
    async with LLMManager() as llm:
        async for chunk in llm.generate_text_with_stream(prompt, model_name, stream_callback, api_key, api_base, **kwargs):
            # print(chunk)
            yield chunk
