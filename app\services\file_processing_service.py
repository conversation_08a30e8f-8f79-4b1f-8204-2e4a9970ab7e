from typing import List, Dict, Any, Optional
import asyncio
import time
import base64
import re
from loguru import logger
from app.core.state_manager import task_manager, TaskStatus


class FileProcessingService:
    """文件处理服务"""
    
    def __init__(self):
        self.logger = logger
        self.supported_formats = {
            'text': ['.txt', '.md', '.csv'],
            'document': ['.doc', '.docx', '.pdf'],
            'image': ['.jpg', '.jpeg', '.png', '.gif'],
            'archive': ['.zip', '.rar', '.7z']
        }
    
    async def validate_file(
        self,
        file_data: str,
        filename: str,
        max_size_mb: int = 50,
        task_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        验证文件
        
        Args:
            file_data: Base64编码的文件数据
            filename: 文件名
            max_size_mb: 最大文件大小(MB)
            task_id: 任务ID
            
        Returns:
            验证结果
        """
        try:
            self.logger.info(f"开始验证文件: {filename}")
            
            validation_issues = []
            
            # 检查文件扩展名
            file_ext = self._get_file_extension(filename)
            if not self._is_supported_format(file_ext):
                validation_issues.append(f"不支持的文件格式: {file_ext}")
            
            # 检查文件大小
            try:
                file_size = len(base64.b64decode(file_data))
                file_size_mb = file_size / (1024 * 1024)
                
                if file_size_mb > max_size_mb:
                    validation_issues.append(f"文件过大: {file_size_mb:.2f}MB > {max_size_mb}MB")
            except Exception as e:
                validation_issues.append(f"文件数据解码失败: {str(e)}")
            
            # 检查文件名
            if not self._is_valid_filename(filename):
                validation_issues.append("文件名包含非法字符")
            
            # 基本安全检查
            if self._has_security_issues(filename, file_data):
                validation_issues.append("文件存在安全风险")
            
            is_valid = len(validation_issues) == 0
            
            file_info = {
                "filename": filename,
                "file_extension": file_ext,
                "file_size_bytes": file_size if 'file_size' in locals() else 0,
                "file_size_mb": file_size_mb if 'file_size_mb' in locals() else 0,
                "file_type": self._get_file_type(file_ext)
            }
            
            result = {
                "is_valid": is_valid,
                "file_info": file_info,
                "validation_issues": validation_issues
            }
            
            self.logger.info(f"文件验证完成: {filename}, 有效: {is_valid}")
            return result
            
        except Exception as e:
            self.logger.error(f"文件验证失败: {e}")
            raise
    
    async def process_file(
        self,
        file_data: str,
        filename: str,
        processing_options: Dict[str, Any],
        task_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """处理文件"""
        
        try:
            self.logger.info(f"开始处理文件: {filename}")
            
            # 更新任务状态
            if task_id:
                await task_manager.update_task_status(
                    task_id,
                    TaskStatus.PROCESSING,
                    step_info={
                        "step": "decoding_file",
                        "progress": 20,
                        "detail": "解码文件数据"
                    }
                )
            
            # 解码文件数据
            try:
                decoded_data = base64.b64decode(file_data)
            except Exception as e:
                raise ValueError(f"文件数据解码失败: {str(e)}")
            
            # 更新任务状态
            if task_id:
                await task_manager.update_task_status(
                    task_id,
                    TaskStatus.PROCESSING,
                    step_info={
                        "step": "extracting_content",
                        "progress": 50,
                        "detail": "提取文件内容"
                    }
                )
            
            # 提取内容
            file_ext = self._get_file_extension(filename)
            extracted_content = await self._extract_file_content(
                decoded_data, file_ext, processing_options
            )
            
            # 更新任务状态
            if task_id:
                await task_manager.update_task_status(
                    task_id,
                    TaskStatus.PROCESSING,
                    step_info={
                        "step": "processing_content",
                        "progress": 80,
                        "detail": "处理文件内容"
                    }
                )
            
            # 处理内容
            processed_content = await self._process_content(
                extracted_content, processing_options
            )
            
            # 生成处理报告
            processing_report = {
                "original_filename": filename,
                "file_size": len(decoded_data),
                "content_length": len(processed_content),
                "processing_options": processing_options,
                "processing_time": time.time(),
                "content_type": self._detect_content_type(processed_content)
            }
            
            result = {
                "processed_content": processed_content,
                "processing_report": processing_report
            }
            
            self.logger.info(f"文件处理完成: {filename}")
            return result
            
        except Exception as e:
            self.logger.error(f"文件处理失败: {e}")
            raise
    
    async def segment_content(
        self,
        content: str,
        segment_type: str = "paragraph",
        max_segment_length: int = 1000,
        task_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """分段内容"""
        
        try:
            self.logger.info(f"开始分段内容: {segment_type}")
            
            segments = []
            
            if segment_type == "paragraph":
                # 按段落分段
                paragraphs = content.split('\n\n')
                for para in paragraphs:
                    if para.strip():
                        if len(para) <= max_segment_length:
                            segments.append(para.strip())
                        else:
                            # 长段落进一步分割
                            sub_segments = self._split_long_text(para, max_segment_length)
                            segments.extend(sub_segments)
            
            elif segment_type == "sentence":
                # 按句子分段
                sentences = re.split(r'[.!?。！？]', content)
                current_segment = ""
                
                for sentence in sentences:
                    sentence = sentence.strip()
                    if not sentence:
                        continue
                    
                    if len(current_segment + sentence) <= max_segment_length:
                        current_segment += sentence + "。"
                    else:
                        if current_segment:
                            segments.append(current_segment.strip())
                        current_segment = sentence + "。"
                
                if current_segment:
                    segments.append(current_segment.strip())
            
            elif segment_type == "fixed_length":
                # 固定长度分段
                for i in range(0, len(content), max_segment_length):
                    segment = content[i:i + max_segment_length]
                    segments.append(segment)
            
            else:
                # 智能分段（简化实现）
                segments = self._smart_segment(content, max_segment_length)
            
            result = {
                "segments": segments,
                "segment_count": len(segments),
                "segmentation_strategy": segment_type
            }
            
            self.logger.info(f"内容分段完成: {len(segments)} 个片段")
            return result
            
        except Exception as e:
            self.logger.error(f"内容分段失败: {e}")
            raise
    
    async def extract_metadata(
        self,
        file_data: str,
        filename: str,
        task_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """提取文件元数据"""
        
        try:
            self.logger.info(f"开始提取元数据: {filename}")
            
            # 解码文件数据
            decoded_data = base64.b64decode(file_data)
            
            # 基本元数据
            metadata = {
                "filename": filename,
                "file_size": len(decoded_data),
                "file_extension": self._get_file_extension(filename),
                "file_type": self._get_file_type(self._get_file_extension(filename)),
                "created_at": time.time()
            }
            
            # 内容分析
            file_ext = self._get_file_extension(filename)
            if file_ext in ['.txt', '.md']:
                content = decoded_data.decode('utf-8', errors='ignore')
                content_analysis = {
                    "character_count": len(content),
                    "word_count": len(content.split()),
                    "line_count": len(content.split('\n')),
                    "paragraph_count": len([p for p in content.split('\n\n') if p.strip()]),
                    "language": self._detect_language(content)
                }
            else:
                content_analysis = {
                    "binary_file": True,
                    "mime_type": self._detect_mime_type(decoded_data)
                }
            
            result = {
                "metadata": metadata,
                "content_analysis": content_analysis
            }
            
            self.logger.info(f"元数据提取完成: {filename}")
            return result
            
        except Exception as e:
            self.logger.error(f"元数据提取失败: {e}")
            raise
    
    async def convert_file(
        self,
        file_data: str,
        filename: str,
        target_format: str,
        task_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """转换文件格式"""
        
        try:
            self.logger.info(f"开始转换文件: {filename} -> {target_format}")
            
            # 解码原文件
            decoded_data = base64.b64decode(file_data)
            source_ext = self._get_file_extension(filename)
            
            # 简化的格式转换实现
            if source_ext == '.txt' and target_format == 'md':
                # 文本转Markdown
                content = decoded_data.decode('utf-8', errors='ignore')
                converted_content = self._convert_txt_to_md(content)
                converted_filename = filename.replace('.txt', '.md')
                
            elif source_ext == '.md' and target_format == 'txt':
                # Markdown转文本
                content = decoded_data.decode('utf-8', errors='ignore')
                converted_content = self._convert_md_to_txt(content)
                converted_filename = filename.replace('.md', '.txt')
                
            else:
                # 其他转换（简化实现）
                converted_content = decoded_data.decode('utf-8', errors='ignore')
                converted_filename = f"{filename}.{target_format}"
            
            # 编码转换后的内容
            converted_data = base64.b64encode(
                converted_content.encode('utf-8')
            ).decode('utf-8')
            
            conversion_report = {
                "source_format": source_ext,
                "target_format": target_format,
                "original_size": len(decoded_data),
                "converted_size": len(converted_content.encode('utf-8')),
                "conversion_time": time.time()
            }
            
            result = {
                "converted_filename": converted_filename,
                "converted_data": converted_data,
                "conversion_report": conversion_report
            }
            
            self.logger.info(f"文件转换完成: {converted_filename}")
            return result
            
        except Exception as e:
            self.logger.error(f"文件转换失败: {e}")
            raise
    
    async def batch_process_files(
        self,
        files: List[Dict[str, Any]],
        processing_options: Dict[str, Any],
        batch_id: str
    ) -> Dict[str, Any]:
        """批量处理文件"""
        
        start_time = time.time()
        results = []
        successful_files = 0
        failed_files = 0
        
        # 并发处理文件
        tasks = []
        for file_info in files:
            task = self.process_file(
                file_data=file_info.get("file_data"),
                filename=file_info.get("filename"),
                processing_options=processing_options
            )
            tasks.append(task)
        
        # 等待所有任务完成
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        for i, result in enumerate(batch_results):
            if isinstance(result, Exception):
                failed_files += 1
                results.append({
                    "filename": files[i].get("filename"),
                    "status": "failed",
                    "error": str(result)
                })
            else:
                successful_files += 1
                results.append({
                    "filename": files[i].get("filename"),
                    "status": "success",
                    "result": result
                })
        
        processing_time = time.time() - start_time
        
        return {
            "successful_files": successful_files,
            "failed_files": failed_files,
            "processing_time": processing_time,
            "results": results
        }
    
    def _get_file_extension(self, filename: str) -> str:
        """获取文件扩展名"""
        return filename.lower().split('.')[-1] if '.' in filename else ''
    
    def _is_supported_format(self, file_ext: str) -> bool:
        """检查是否支持的格式"""
        for formats in self.supported_formats.values():
            if f'.{file_ext}' in formats:
                return True
        return False
    
    def _get_file_type(self, file_ext: str) -> str:
        """获取文件类型"""
        for file_type, formats in self.supported_formats.items():
            if f'.{file_ext}' in formats:
                return file_type
        return 'unknown'
    
    def _is_valid_filename(self, filename: str) -> bool:
        """验证文件名"""
        invalid_chars = ['<', '>', ':', '"', '|', '?', '*']
        return not any(char in filename for char in invalid_chars)
    
    def _has_security_issues(self, filename: str, file_data: str) -> bool:
        """检查安全问题"""
        # 简化的安全检查
        suspicious_extensions = ['.exe', '.bat', '.cmd', '.scr']
        file_ext = self._get_file_extension(filename)
        return f'.{file_ext}' in suspicious_extensions
    
    async def _extract_file_content(
        self,
        decoded_data: bytes,
        file_ext: str,
        processing_options: Dict[str, Any]
    ) -> str:
        """提取文件内容"""
        
        if file_ext in ['txt', 'md', 'csv']:
            return decoded_data.decode('utf-8', errors='ignore')
        else:
            # 其他格式的简化处理
            return f"[{file_ext.upper()} 文件内容]"
    
    async def _process_content(
        self,
        content: str,
        processing_options: Dict[str, Any]
    ) -> str:
        """处理内容"""
        
        processed = content
        
        # 清理空白字符
        if processing_options.get('clean_whitespace', True):
            processed = re.sub(r'\s+', ' ', processed).strip()
        
        # 移除特殊字符
        if processing_options.get('remove_special_chars', False):
            processed = re.sub(r'[^\w\s\u4e00-\u9fff]', '', processed)
        
        return processed
    
    def _split_long_text(self, text: str, max_length: int) -> List[str]:
        """分割长文本"""
        segments = []
        words = text.split()
        current_segment = ""
        
        for word in words:
            if len(current_segment + " " + word) <= max_length:
                current_segment += " " + word if current_segment else word
            else:
                if current_segment:
                    segments.append(current_segment.strip())
                current_segment = word
        
        if current_segment:
            segments.append(current_segment.strip())
        
        return segments
    
    def _smart_segment(self, content: str, max_length: int) -> List[str]:
        """智能分段"""
        # 简化的智能分段实现
        paragraphs = content.split('\n\n')
        segments = []
        
        for para in paragraphs:
            if len(para) <= max_length:
                segments.append(para.strip())
            else:
                sub_segments = self._split_long_text(para, max_length)
                segments.extend(sub_segments)
        
        return segments
    
    def _detect_content_type(self, content: str) -> str:
        """检测内容类型"""
        if re.search(r'[a-zA-Z]', content):
            return "mixed" if re.search(r'[\u4e00-\u9fff]', content) else "english"
        elif re.search(r'[\u4e00-\u9fff]', content):
            return "chinese"
        else:
            return "unknown"
    
    def _detect_language(self, content: str) -> str:
        """检测语言"""
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', content))
        english_chars = len(re.findall(r'[a-zA-Z]', content))
        
        if chinese_chars > english_chars:
            return "chinese"
        elif english_chars > chinese_chars:
            return "english"
        else:
            return "mixed"
    
    def _detect_mime_type(self, data: bytes) -> str:
        """检测MIME类型"""
        # 简化的MIME类型检测
        if data.startswith(b'\x89PNG'):
            return "image/png"
        elif data.startswith(b'\xff\xd8\xff'):
            return "image/jpeg"
        elif data.startswith(b'%PDF'):
            return "application/pdf"
        else:
            return "application/octet-stream"
    
    def _convert_txt_to_md(self, content: str) -> str:
        """文本转Markdown"""
        lines = content.split('\n')
        md_lines = []
        
        for line in lines:
            line = line.strip()
            if not line:
                md_lines.append('')
            elif len(line) < 50 and not line.endswith('.'):
                # 可能是标题
                md_lines.append(f"## {line}")
            else:
                md_lines.append(line)
        
        return '\n'.join(md_lines)
    
    def _convert_md_to_txt(self, content: str) -> str:
        """Markdown转文本"""
        # 移除Markdown标记
        text = re.sub(r'^#+\s*', '', content, flags=re.MULTILINE)  # 标题
        text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)  # 粗体
        text = re.sub(r'\*(.*?)\*', r'\1', text)  # 斜体
        text = re.sub(r'`(.*?)`', r'\1', text)  # 代码
        text = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', text)  # 链接
        
        return text


# 创建全局服务实例
file_processing_service = FileProcessingService()
