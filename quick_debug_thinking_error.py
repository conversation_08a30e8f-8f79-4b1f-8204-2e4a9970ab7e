#!/usr/bin/env python3
"""
快速排查enable_thinking拼写错误的脚本
"""

import re
import os
import glob

def search_files_for_typo():
    """搜索项目文件中的拼写错误"""
    print("🔍 搜索项目文件中的 'enalbe_thinking' 拼写错误...")
    print("="*60)
    
    # 要搜索的文件类型
    file_patterns = [
        "**/*.py",
        "**/*.yaml", 
        "**/*.yml",
        "**/*.json",
        "**/*.md"
    ]
    
    # 要搜索的拼写错误模式
    typo_patterns = [
        r"enalbe_thinking",
        r"enable_thinkng", 
        r"thinking_enable",
        r"enabel_thinking",
        r"enable_thinkin"
    ]
    
    found_issues = []
    
    for pattern in file_patterns:
        for file_path in glob.glob(pattern, recursive=True):
            # 跳过一些不需要检查的目录
            if any(skip in file_path for skip in ['.git', '__pycache__', 'node_modules', '.venv', 'venv']):
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    
                for typo_pattern in typo_patterns:
                    matches = re.finditer(typo_pattern, content, re.IGNORECASE)
                    for match in matches:
                        # 找到匹配的行号
                        line_num = content[:match.start()].count('\n') + 1
                        line_content = content.split('\n')[line_num - 1].strip()
                        
                        found_issues.append({
                            'file': file_path,
                            'line': line_num,
                            'typo': match.group(),
                            'content': line_content
                        })
                        
            except Exception as e:
                print(f"   ⚠️  无法读取文件 {file_path}: {e}")
    
    if found_issues:
        print(f"❌ 发现 {len(found_issues)} 个拼写错误:")
        for issue in found_issues:
            print(f"   📁 文件: {issue['file']}")
            print(f"   📍 行号: {issue['line']}")
            print(f"   🔤 错误: {issue['typo']}")
            print(f"   📝 内容: {issue['content']}")
            print()
    else:
        print("✅ 未发现拼写错误")
    
    return found_issues

def check_common_sources():
    """检查常见的错误来源"""
    print("🔍 检查常见的错误来源...")
    print("="*60)
    
    # 检查配置文件
    config_files = [
        "config.yaml",
        "config.yml", 
        "app/config.yaml",
        "settings.yaml"
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"📋 检查配置文件: {config_file}")
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if 'thinking' in content.lower():
                        lines = content.split('\n')
                        for i, line in enumerate(lines, 1):
                            if 'thinking' in line.lower():
                                print(f"   行 {i}: {line.strip()}")
            except Exception as e:
                print(f"   ❌ 读取失败: {e}")
            print()

def generate_fix_suggestions():
    """生成修复建议"""
    print("💡 修复建议:")
    print("="*60)
    
    suggestions = [
        "1. 检查所有调用LLM的地方，确保参数名拼写正确",
        "2. 在LLM管理器中启用DEBUG日志查看传入的参数",
        "3. 检查工作流配置中的参数名",
        "4. 检查配置文件中的参数设置",
        "5. 查看完整的错误堆栈信息确定错误来源"
    ]
    
    for suggestion in suggestions:
        print(f"   {suggestion}")
    
    print("\n🔧 临时解决方案:")
    print("   在 _create_llm_instance 方法中添加更强的参数过滤:")
    
    code_fix = '''
# 在参数过滤后添加额外的安全检查
safe_kwargs = {}
for k, v in filtered_kwargs.items():
    # 跳过任何包含 'thinking' 的参数（无论拼写如何）
    if 'thinking' not in k.lower():
        safe_kwargs[k] = v
    else:
        logger.warning(f"跳过可能有问题的参数: {k}={v}")

# 使用 safe_kwargs 而不是 filtered_kwargs
'''
    print(code_fix)

def enable_debug_logging():
    """启用调试日志的说明"""
    print("\n🔧 启用调试日志:")
    print("="*60)
    
    debug_code = '''
# 在应用启动时添加以下代码启用DEBUG日志:
import logging
logging.getLogger('app.services.llm_manager').setLevel(logging.DEBUG)

# 或者在环境变量中设置:
export LOG_LEVEL=DEBUG
'''
    print(debug_code)

def main():
    """主函数"""
    print("🚨 enable_thinking 拼写错误排查工具")
    print("="*60)
    
    # 搜索拼写错误
    issues = search_files_for_typo()
    
    # 检查常见来源
    check_common_sources()
    
    # 生成修复建议
    generate_fix_suggestions()
    
    # 启用调试日志说明
    enable_debug_logging()
    
    print("\n🎯 总结:")
    if issues:
        print(f"   发现 {len(issues)} 个拼写错误需要修复")
    else:
        print("   未发现明显的拼写错误")
        print("   建议启用DEBUG日志查看运行时参数传递")

if __name__ == "__main__":
    main()
