# Prompt格式规范指南

## 概述

本文档定义了智能命题系统中所有prompt提示词的标准格式规范，确保代码的一致性、可读性和维护性。

## 基本原则

1. **一致性**：所有prompt采用统一的markdown格式
2. **可读性**：清晰的结构层次和信息组织
3. **专业性**：规范的文档格式体现系统专业度
4. **维护性**：便于后续修改和扩展

## 格式规范

### 1. 整体结构

```markdown
## 角色与任务
[角色定义和主要任务描述]

## [信息类型]
[相关信息内容]

## 具体工作要求
[详细的工作要求和步骤]

## 输出要求
[输出格式和质量要求]

---

**[最终行动指令]：**
```

### 2. 标题层次

#### 主要部分（二级标题）
```markdown
## 角色与任务
## 原题信息  
## 命题要求
## 具体工作要求
## 输出要求
```

#### 子部分（三级标题）
```markdown
### 1. 材料生成策略
### 2. 子题命制策略
### 3. 质量保证措施
```

### 3. 信息强调

#### 关键信息加粗
```markdown
- **题目类型**：{question_type}
- **难度要求**：{difficulty}
- **知识点**：{knowledge_points}
```

#### 角色强调
```markdown
你是一个专业的**{exam_subject}命题专家**。
```

### 4. 内容包装

#### 代码块包装结构化内容
```markdown
## 命题素材

```
{material_context}
```

## JSON输出格式

```json
{
    "key": "value"
}
```
```

#### 列表格式
```markdown
## 工作要求

1. **要求类型**：具体要求描述
2. **质量标准**：质量相关要求
3. **格式规范**：格式相关要求
```

### 5. 分隔和结尾

#### 分隔线使用
```markdown
---

**请生成新的试题内容：**
```

## 具体应用示例

### 1. 基础题克隆Prompt

```markdown
## 角色与任务

你是一个专业的**试题克隆专家**。请基于以下原题进行克隆，生成一道新的试题。

## 原题信息

- **题目内容**：{original_content}
- **题目类型**：{question_type}
- **学科**：{subject}
- **受试人群**：{grade}
- **难度**：{difficulty}
- **知识点**：{knowledge_points}

## 克隆要求

1. **结构一致**：保持相同的题型结构和难度
2. **内容创新**：使用不同的具体内容
3. **知识对应**：保持相同的知识点要求
4. **质量保证**：确保试题质量不低于原题
5. **学科特色**：生成的内容要符合{subject}学科的特点

## 输出试题结构

```
{mapped_structure}
```

## 输出要求

1. **内容纯净**：不要输出任何与试题结构无关的内容
2. **严格遵循**：严格按照输出试题结构的要求生成试题

---

**请生成新的试题内容：**
```

### 2. 智能命题Prompt

```markdown
## 角色与任务

你是一个专业的**{exam_subject}命题专家**。请结合命题素材生成详细的命题规划。

## 命题素材

```
{material_context}
```

## 命题要求

**题型信息**：{question_type_context}
**考试科目**：{exam_subject}

## 具体工作要求

1. **策略制定**：制订好关于每一道试题的命制策略
2. **结构化输出**：每一道试题应当是一个单独的JSON对象

## 试题命制策略

1. **素材关联**：试题的命制要与命题素材紧密相关
2. **学科特色**：试题的命制要符合{exam_subject}学科特点

## 输出要求

1. **格式严格**：严格按照JSON格式输出
2. **策略合理**：确保试题的命制策略合理可行

### JSON输出格式

```json
{
    "question_tasks": [
        {
            "subquestion_index": 1,
            "type": "试题类型",
            "difficulty": "难度要求",
            "knowledge_points": "知识点要求",
            "strategy": "命制策略",
            "requirements": "具体命制要求"
        }
    ]
}
```

---

**输出：**
```

## 编写指南

### 1. 开始编写

1. **确定prompt类型**：明确是克隆、生成还是规划类prompt
2. **分析信息结构**：梳理需要包含的信息类型
3. **设计层次结构**：规划标题和内容的层次关系

### 2. 内容组织

1. **角色定义清晰**：明确AI的角色和主要任务
2. **信息分类明确**：将不同类型的信息分别组织
3. **要求具体明确**：工作要求和输出要求要具体可执行
4. **格式规范统一**：严格按照本规范的格式要求

### 3. 质量检查

1. **格式一致性**：检查标题、列表、强调等格式是否一致
2. **信息完整性**：确保所有必要信息都已包含
3. **逻辑清晰性**：检查内容逻辑是否清晰合理
4. **参数正确性**：确保所有模板参数格式正确

## 常见问题

### 1. 标题层次混乱
**问题**：使用了不一致的标题层次
**解决**：严格按照二级标题（主要部分）、三级标题（子部分）的规范

### 2. 强调格式不统一
**问题**：有些地方用粗体，有些地方不用
**解决**：关键信息、角色、重要参数都要使用粗体强调

### 3. 代码块使用不当
**问题**：结构化内容没有用代码块包装
**解决**：JSON格式、模板结构、长文本内容都应该用代码块包装

### 4. 分隔线缺失
**问题**：最终指令前没有分隔线
**解决**：在最终行动指令前添加`---`分隔线

## 维护建议

1. **定期检查**：定期检查现有prompt的格式一致性
2. **新增规范**：新增prompt严格按照本规范编写
3. **团队培训**：确保团队成员熟悉并遵循本规范
4. **文档更新**：根据实际使用情况及时更新本规范

## 总结

遵循本格式规范可以确保智能命题系统中所有prompt的一致性和专业性，提升代码质量和维护效率。建议所有开发人员在编写或修改prompt时严格按照本规范执行。
