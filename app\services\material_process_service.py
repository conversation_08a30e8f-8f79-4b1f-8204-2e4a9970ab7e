from typing import Optional, Dict, Any
import base64
import io
from loguru import logger
from app.models.schemas import FileType
from app.core.state_manager import task_manager, TaskStatus


class MaterialProcessService:
    """材料处理服务"""
    
    def __init__(self):
        self.logger = logger
    
    async def process_material(
        self,
        content: Optional[str] = None,
        file_data: Optional[str] = None,
        file_type: Optional[FileType] = None,
        filename: Optional[str] = None,
        task_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        处理材料内容
        
        Args:
            content: 直接文本内容
            file_data: Base64编码的文件数据
            file_type: 文件类型
            filename: 文件名
            task_id: 任务ID
            
        Returns:
            处理结果字典
        """
        try:
            self.logger.info(f"开始处理材料: {filename or 'text_content'}")
            
            # 更新任务状态
            if task_id:
                await task_manager.update_task_status(
                    task_id,
                    TaskStatus.PROCESSING,
                    step_info={
                        "step": "extracting_content",
                        "progress": 20,
                        "detail": "提取内容"
                    }
                )
            
            extracted_content = ""
            
            # 处理直接文本内容
            if content:
                extracted_content = self._process_text_content(content)
            
            # 处理文件内容
            elif file_data and file_type:
                extracted_content = await self._process_file_content(
                    file_data, file_type, filename, task_id
                )
            
            else:
                raise ValueError("必须提供content或file_data之一")
            
            # 更新任务状态
            if task_id:
                await task_manager.update_task_status(
                    task_id,
                    TaskStatus.PROCESSING,
                    step_info={
                        "step": "cleaning_content",
                        "progress": 70,
                        "detail": "清理和优化内容"
                    }
                )
            
            # 清理和优化内容
            cleaned_content = self._clean_content(extracted_content)
            
            # 更新任务状态
            if task_id:
                await task_manager.update_task_status(
                    task_id,
                    TaskStatus.PROCESSING,
                    step_info={
                        "step": "completed",
                        "progress": 100,
                        "detail": "处理完成"
                    }
                )
            
            result = {
                "extracted_content": cleaned_content,
                "original_filename": filename,
                "file_type": file_type.value if file_type else None,
                "content_length": len(cleaned_content)
            }
            
            self.logger.info(f"材料处理成功: {len(cleaned_content)} 字符")
            return result
            
        except Exception as e:
            self.logger.error(f"材料处理失败: {e}")
            
            # 更新任务状态为失败
            if task_id:
                await task_manager.update_task_status(
                    task_id,
                    TaskStatus.FAILED,
                    error_message=str(e)
                )
            
            raise
    
    def _process_text_content(self, content: str) -> str:
        """处理文本内容"""
        return content.strip()
    
    async def _process_file_content(
        self,
        file_data: str,
        file_type: FileType,
        filename: Optional[str] = None,
        task_id: Optional[str] = None
    ) -> str:
        """处理文件内容"""
        
        try:
            # 解码Base64数据
            file_bytes = base64.b64decode(file_data)
            
            # 根据文件类型提取内容
            if file_type == FileType.TXT:
                return self._extract_txt_content(file_bytes)
            elif file_type == FileType.PDF:
                return await self._extract_pdf_content(file_bytes, task_id)
            elif file_type == FileType.DOC:
                return await self._extract_doc_content(file_bytes, task_id)
            elif file_type == FileType.DOCX:
                return await self._extract_docx_content(file_bytes, task_id)
            else:
                raise ValueError(f"不支持的文件类型: {file_type}")
                
        except Exception as e:
            self.logger.error(f"文件内容提取失败: {e}")
            raise
    
    def _extract_txt_content(self, file_bytes: bytes) -> str:
        """提取TXT文件内容"""
        try:
            # 尝试多种编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
            
            for encoding in encodings:
                try:
                    return file_bytes.decode(encoding)
                except UnicodeDecodeError:
                    continue
            
            # 如果所有编码都失败，使用错误处理
            return file_bytes.decode('utf-8', errors='ignore')
            
        except Exception as e:
            self.logger.error(f"TXT文件解析失败: {e}")
            raise
    
    async def _extract_pdf_content(self, file_bytes: bytes, task_id: Optional[str] = None) -> str:
        """提取PDF文件内容"""
        try:
            # 这里应该使用PDF解析库，如PyPDF2或pdfplumber
            # 为了简化，这里返回占位符内容
            self.logger.warning("PDF解析功能需要安装相应的库")
            return "PDF内容提取功能正在开发中，请使用文本格式文件。"
            
        except Exception as e:
            self.logger.error(f"PDF文件解析失败: {e}")
            raise
    
    async def _extract_doc_content(self, file_bytes: bytes, task_id: Optional[str] = None) -> str:
        """提取DOC文件内容"""
        try:
            # 这里应该使用DOC解析库，如python-docx
            # 为了简化，这里返回占位符内容
            self.logger.warning("DOC解析功能需要安装相应的库")
            return "DOC内容提取功能正在开发中，请使用文本格式文件。"
            
        except Exception as e:
            self.logger.error(f"DOC文件解析失败: {e}")
            raise
    
    async def _extract_docx_content(self, file_bytes: bytes, task_id: Optional[str] = None) -> str:
        """提取DOCX文件内容"""
        try:
            # 这里应该使用DOCX解析库，如python-docx
            # 为了简化，这里返回占位符内容
            self.logger.warning("DOCX解析功能需要安装相应的库")
            return "DOCX内容提取功能正在开发中，请使用文本格式文件。"
            
        except Exception as e:
            self.logger.error(f"DOCX文件解析失败: {e}")
            raise
    
    def _clean_content(self, content: str) -> str:
        """清理和优化内容"""
        
        # 基本清理
        cleaned = content.strip()
        
        # 移除多余的空行
        lines = cleaned.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            if line or (cleaned_lines and cleaned_lines[-1]):  # 保留一个空行
                cleaned_lines.append(line)
        
        # 重新组合
        cleaned = '\n'.join(cleaned_lines)
        
        # 移除多余的空格
        cleaned = ' '.join(cleaned.split())
        
        return cleaned


# 创建全局服务实例
material_process_service = MaterialProcessService()
