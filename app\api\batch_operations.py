from fastapi import APIRouter, HTTPException
from app.models.schemas import StandardResponse, ErrorResponse
from app.services.batch_operations_service import batch_operations_service
from app.core.state_manager import task_manager, TaskStatus
import uuid
from datetime import datetime
from loguru import logger

router = APIRouter()


@router.post("/BatchGenerateQuestions")
async def batch_generate_questions(
    batch_config: dict,
    concurrent_limit: int = 5
):
    """
    批量生成试题
    
    功能：
    - 支持多个主题同时生成试题
    - 并发控制和资源管理
    - 实时进度跟踪
    - 批量结果统计
    """
    try:
        batch_id = str(uuid.uuid4())
        
        logger.info(f"开始批量生成试题: {batch_id}")
        
        # 调用服务批量生成
        result = await batch_operations_service.batch_generate_questions(
            batch_config=batch_config,
            concurrent_limit=concurrent_limit,
            batch_id=batch_id
        )
        
        return StandardResponse(
            code=200,
            msg="批量生成启动成功",
            data={
                "batch_id": batch_id,
                "total_tasks": result["total_tasks"],
                "estimated_time": result["estimated_time"],
                "status": "processing"
            }
        )
        
    except Exception as e:
        logger.error(f"批量生成试题失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"批量生成试题失败: {str(e)}"
        )


@router.post("/BatchProcessMaterials")
async def batch_process_materials(
    materials: list,
    processing_config: dict = None
):
    """
    批量处理材料
    
    功能：
    - 批量上传和处理多个材料
    - 统一处理配置
    - 并发处理优化
    - 错误处理和重试
    """
    try:
        batch_id = str(uuid.uuid4())
        
        logger.info(f"开始批量处理材料: {batch_id}, {len(materials)} 个材料")
        
        # 调用服务批量处理
        result = await batch_operations_service.batch_process_materials(
            materials=materials,
            processing_config=processing_config or {},
            batch_id=batch_id
        )
        
        return StandardResponse(
            code=200,
            msg="批量处理完成",
            data={
                "batch_id": batch_id,
                "total_materials": len(materials),
                "successful_materials": result["successful_materials"],
                "failed_materials": result["failed_materials"],
                "processing_time": result["processing_time"],
                "results": result["results"]
            }
        )
        
    except Exception as e:
        logger.error(f"批量处理材料失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"批量处理材料失败: {str(e)}"
        )


@router.post("/BatchOptimizeQuestions")
async def batch_optimize_questions(
    question_batches: list,
    optimization_config: dict = None
):
    """
    批量优化试题
    
    功能：
    - 批量分析试题质量
    - 统一优化标准
    - 并发优化处理
    - 优化结果汇总
    """
    try:
        batch_id = str(uuid.uuid4())
        
        logger.info(f"开始批量优化试题: {batch_id}")
        
        # 调用服务批量优化
        result = await batch_operations_service.batch_optimize_questions(
            question_batches=question_batches,
            optimization_config=optimization_config or {},
            batch_id=batch_id
        )
        
        return StandardResponse(
            code=200,
            msg="批量优化完成",
            data={
                "batch_id": batch_id,
                "total_batches": len(question_batches),
                "optimization_results": result["optimization_results"],
                "overall_statistics": result["overall_statistics"]
            }
        )
        
    except Exception as e:
        logger.error(f"批量优化试题失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"批量优化试题失败: {str(e)}"
        )


@router.get("/GetBatchStatus/{batch_id}")
async def get_batch_status(batch_id: str):
    """
    查询批量操作状态
    
    功能：
    - 实时查询批量操作进度
    - 显示详细执行状态
    - 提供性能指标
    """
    try:
        result = await batch_operations_service.get_batch_status(batch_id)
        
        if not result:
            raise HTTPException(
                status_code=404,
                detail=f"批量操作不存在: {batch_id}"
            )
        
        return StandardResponse(
            code=200,
            msg="查询成功",
            data=result
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询批量状态失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"查询批量状态失败: {str(e)}"
        )


@router.post("/CancelBatchOperation/{batch_id}")
async def cancel_batch_operation(batch_id: str, reason: str = "用户取消"):
    """
    取消批量操作
    
    功能：
    - 取消正在执行的批量操作
    - 停止所有相关任务
    - 清理资源和状态
    """
    try:
        result = await batch_operations_service.cancel_batch_operation(
            batch_id=batch_id,
            reason=reason
        )
        
        return StandardResponse(
            code=200,
            msg="取消成功",
            data=result
        )
        
    except Exception as e:
        logger.error(f"取消批量操作失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"取消批量操作失败: {str(e)}"
        )


@router.post("/BatchExportResults")
async def batch_export_results(
    batch_ids: list,
    export_format: str = "json",
    include_metadata: bool = True
):
    """
    批量导出结果
    
    功能：
    - 导出多个批量操作的结果
    - 支持多种导出格式
    - 包含元数据和统计信息
    """
    try:
        export_id = str(uuid.uuid4())
        
        logger.info(f"开始批量导出结果: {export_id}")
        
        # 调用服务批量导出
        result = await batch_operations_service.batch_export_results(
            batch_ids=batch_ids,
            export_format=export_format,
            include_metadata=include_metadata,
            export_id=export_id
        )
        
        return StandardResponse(
            code=200,
            msg="导出完成",
            data={
                "export_id": export_id,
                "export_format": export_format,
                "exported_batches": len(batch_ids),
                "export_data": result["export_data"],
                "export_metadata": result.get("export_metadata", {})
            }
        )
        
    except Exception as e:
        logger.error(f"批量导出失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"批量导出失败: {str(e)}"
        )


@router.get("/ListBatchOperations")
async def list_batch_operations(
    status: str = None,
    operation_type: str = None,
    page: int = 1,
    page_size: int = 20
):
    """
    列出批量操作
    
    功能：
    - 分页列出所有批量操作
    - 按状态和类型筛选
    - 显示操作统计信息
    """
    try:
        result = await batch_operations_service.list_batch_operations(
            status=status,
            operation_type=operation_type,
            page=page,
            page_size=page_size
        )
        
        return StandardResponse(
            code=200,
            msg="查询成功",
            data=result
        )
        
    except Exception as e:
        logger.error(f"列出批量操作失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"列出批量操作失败: {str(e)}"
        )


@router.post("/BatchRetryFailed")
async def batch_retry_failed(
    batch_id: str,
    retry_config: dict = None
):
    """
    重试失败的批量操作
    
    功能：
    - 重试批量操作中失败的任务
    - 保留成功的结果
    - 可配置重试策略
    """
    try:
        result = await batch_operations_service.batch_retry_failed(
            batch_id=batch_id,
            retry_config=retry_config or {}
        )
        
        return StandardResponse(
            code=200,
            msg="重试启动成功",
            data=result
        )
        
    except Exception as e:
        logger.error(f"批量重试失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"批量重试失败: {str(e)}"
        )


@router.post("/ScheduleBatchOperation")
async def schedule_batch_operation(
    operation_config: dict,
    schedule_time: str = None,
    recurring: bool = False
):
    """
    调度批量操作
    
    功能：
    - 定时执行批量操作
    - 支持周期性批量任务
    - 管理批量操作队列
    """
    try:
        schedule_id = str(uuid.uuid4())
        
        result = await batch_operations_service.schedule_batch_operation(
            operation_config=operation_config,
            schedule_time=schedule_time,
            recurring=recurring,
            schedule_id=schedule_id
        )
        
        return StandardResponse(
            code=200,
            msg="调度成功",
            data={
                "schedule_id": schedule_id,
                "operation_type": operation_config.get("type"),
                "schedule_time": schedule_time,
                "recurring": recurring,
                "status": "scheduled"
            }
        )
        
    except Exception as e:
        logger.error(f"调度批量操作失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"调度批量操作失败: {str(e)}"
        )


@router.get("/GetBatchStatistics")
async def get_batch_statistics(
    start_date: str = None,
    end_date: str = None,
    operation_type: str = None
):
    """
    获取批量操作统计
    
    功能：
    - 统计批量操作执行情况
    - 分析性能和成功率
    - 生成统计报告
    """
    try:
        result = await batch_operations_service.get_batch_statistics(
            start_date=start_date,
            end_date=end_date,
            operation_type=operation_type
        )
        
        return StandardResponse(
            code=200,
            msg="统计完成",
            data=result
        )
        
    except Exception as e:
        logger.error(f"获取批量统计失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取批量统计失败: {str(e)}"
        )
