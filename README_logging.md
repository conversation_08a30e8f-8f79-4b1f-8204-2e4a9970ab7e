# 生产环境日志优化使用指南

## 🎯 优化目标

解决生产环境中控制台日志输出过多的问题，通过智能的日志级别控制，在保持调试能力的同时大幅减少控制台噪音。

## 🚀 快速开始

### 1. 设置生产环境（推荐）

```bash
# 设置环境变量
export ENVIRONMENT=production

# 或者在Windows中
set ENVIRONMENT=production

# 重启应用
python main.py
```

### 2. 使用日志控制脚本

```bash
# 切换到生产环境模式
python scripts/log_control.py env prod

# 切换到开发环境模式
python scripts/log_control.py env dev

# 查看日志统计
python scripts/log_control.py stats

# 查看最新20条错误日志
python scripts/log_control.py tail --type error --lines 20

# 实时监控错误日志
python scripts/log_control.py monitor

# 清理30天前的日志
python scripts/log_control.py clean --days 30
```

## 📊 优化效果对比

### 优化前（开发环境）
```
2025-07-15 10:30:01 | INFO     | intelligent_question_service:_call_llm_with_stream:474 - 开始命题规划步骤
2025-07-15 10:30:01 | DEBUG    | state_manager:add_stream_data:137 - 添加流式数据到任务: task_123
2025-07-15 10:30:02 | INFO     | llm_manager:_call_deepseek_stream_api:301 - 开始DeepSeek流式API调用
2025-07-15 10:30:02 | INFO     | llm_manager:_call_deepseek_stream_api:310 - DeepSeek流式连接建立成功，开始接收数据...
2025-07-15 10:30:03 | DEBUG    | state_manager:add_stream_data:137 - 添加流式数据到任务: task_123
2025-07-15 10:30:03 | INFO     | intelligent_question_service:_call_llm_with_stream:505 - 命题规划步骤完成
2025-07-15 10:30:03 | INFO     | intelligent_question_service:_call_llm_with_stream:518 - 命题规划步骤完成标记已发送
... (大量详细日志)
```

### 优化后（生产环境）
```
2025-07-15 10:30:01 | WARNING  | intelligent_question_service:_generate_planning_with_retry:1174 - 命题规划尝试 1 失败: Connection timeout
2025-07-15 10:30:05 | ERROR    | llm_manager:_call_deepseek_api:208 - DeepSeek API调用失败: 500 - Internal Server Error
```

**控制台输出减少 90% 以上！**

## 🔧 配置说明

### 环境变量控制

| 环境变量 | 值 | 控制台输出 | 文件输出 | 适用场景 |
|----------|----|-----------|---------|---------| 
| `ENVIRONMENT` | `development` | DEBUG+ | DEBUG+ | 开发调试 |
| `ENVIRONMENT` | `production` | WARNING+ | INFO+ | 生产环境 |
| 未设置 | - | WARNING+ | INFO+ | 默认生产环境 |

### 日志文件说明

- `logs/app.log` - 主日志文件（INFO级别及以上）
- `logs/error.log` - 错误日志文件（ERROR级别）
- 自动轮转：每天一个文件
- 自动清理：生产环境保留30天，错误日志保留90天

## 📋 日志级别调整详情

### 保留的关键业务日志（INFO级别）
- ✅ 智能命题开始/完成
- ✅ 任务创建
- ✅ 系统启动/关闭

### 调整为DEBUG级别的日志
- 🔧 LLM模型调用详情
- 🔧 流式数据推送过程
- 🔧 连接状态变更
- 🔧 任务状态更新
- 🔧 重试成功信息
- 🔧 步骤完成标记

### 保留的警告和错误日志（WARNING/ERROR级别）
- ⚠️ 重试失败警告
- ⚠️ 解析失败警告
- ❌ API调用错误
- ❌ 系统异常错误

## 🛠️ 使用工具

### 1. 日志控制脚本

```bash
# 查看帮助
python scripts/log_control.py --help

# 查看子命令帮助
python scripts/log_control.py tail --help
```

### 2. 手动日志查看

```bash
# 查看最新日志
tail -f logs/app.log

# 只看错误日志
grep "ERROR" logs/app.log

# 统计今天的错误数量
grep "$(date +%Y-%m-%d)" logs/app.log | grep "ERROR" | wc -l

# 查看特定时间段的日志
grep "2025-07-15 10:" logs/app.log
```

### 3. 系统监控集成

```bash
# 配置logrotate（Linux）
sudo cp scripts/logrotate.conf /etc/logrotate.d/intelligent-question

# 配置systemd服务（Linux）
sudo cp scripts/intelligent-question.service /etc/systemd/system/
sudo systemctl enable intelligent-question
sudo systemctl start intelligent-question
```

## 🔍 故障排查

### 1. 如果需要临时启用详细日志

```bash
# 方法1：设置环境变量
export ENVIRONMENT=development
# 重启应用

# 方法2：使用控制脚本
python scripts/log_control.py env dev
# 重启应用
```

### 2. 查看最近的错误

```bash
# 查看最新10条错误
python scripts/log_control.py tail --type error --lines 10

# 实时监控错误
python scripts/log_control.py monitor
```

### 3. 分析日志统计

```bash
# 查看今日日志统计
python scripts/log_control.py stats
```

## 📈 性能提升

### 优化前
- 控制台输出：每分钟 100+ 条日志
- 磁盘I/O：频繁写入详细日志
- 可读性：重要信息被淹没

### 优化后
- 控制台输出：每分钟 < 10 条日志（仅警告和错误）
- 磁盘I/O：减少不必要的控制台输出
- 可读性：重要信息突出显示

## 🔄 回滚方案

如果遇到问题需要回滚：

1. **临时回滚**：
   ```bash
   export ENVIRONMENT=development
   # 重启应用
   ```

2. **永久回滚**：
   修改 `main.py` 中的默认日志级别：
   ```python
   # 临时修改为详细模式
   logger.add(sys.stdout, level="DEBUG")
   ```

## 💡 最佳实践

1. **生产环境**：始终使用 `ENVIRONMENT=production`
2. **开发环境**：使用 `ENVIRONMENT=development` 进行调试
3. **监控**：定期检查错误日志文件
4. **清理**：定期清理旧日志文件释放磁盘空间
5. **备份**：重要的错误日志应该备份到外部存储

## 📞 技术支持

如果在使用过程中遇到问题：

1. 查看 `logging_optimization.md` 了解详细技术说明
2. 使用 `python scripts/log_control.py stats` 检查日志状态
3. 检查环境变量设置是否正确
4. 确认日志文件权限是否正确

---

通过这次优化，生产环境的日志输出更加清洁和高效，同时保持了完整的调试能力。🎉
