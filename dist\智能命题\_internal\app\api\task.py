from fastapi import APIRouter
from app.models.schemas import TaskStateRequest, TaskStateResponse
from app.core.state_manager import task_manager, TaskStatus
from loguru import logger

router = APIRouter()


@router.post("/GetTaskState", response_model=TaskStateResponse)
async def get_task_state(request: TaskStateRequest):
    """查询任务状态"""
    try:
        # 支持TaskId和query_id两种参数名
        task_id = request.TaskId or getattr(request, 'query_id', None)

        if not task_id:
            return TaskStateResponse(
                Data={
                    "content": "TaskId cannot be empty",
                    "is_completed": False
                },
                code=400,
                msg="参数错误"
            )

        task_info = await task_manager.get_task_info(task_id)

        if not task_info:
            return TaskStateResponse(
                Data={
                    "content": "Task not found",
                    "is_completed": False
                },
                code=404,
                msg="任务不存在"
            )
        state_content = task_info.step_message if task_info.step_message else ""
        # 构建状态内容
        # state_content = {
        #     "TaskId": task_id,
        #     "State": task_info.status.value,
        #     "Msg": f"Task {task_info.status.value}",
        #     "CreatedAt": task_info.created_at,
        #     "UpdatedAt": task_info.updated_at
        # }

        # # 添加详细的状态信息
        # if task_info.current_step:
        #     state_content["CurrentStep"] = task_info.current_step
        # if task_info.progress is not None:
        #     state_content["Progress"] = task_info.progress
        # if task_info.step_message:
        #     state_content["StepMessage"] = task_info.step_message
        # if task_info.step_data:
        #     state_content["StepData"] = task_info.step_data

        # # 如果任务完成，添加结果信息
        # # if task_info.status == TaskStatus.COMPLETED and task_info.result:
        # #     state_content["Result"] = task_info.result
        # # elif task_info.status == TaskStatus.FAILED and task_info.error_message:
        # #     state_content["Error"] = task_info.error_message

        # 判断任务是否完成
        is_completed = task_info.status in [
            TaskStatus.COMPLETED, TaskStatus.FAILED]

        logger.info(
            f"查询任务状态: {task_id} -> {task_info.status.value} (进度: {task_info.progress}%)")

        return TaskStateResponse(
            Data={
                "content": state_content,
                "is_completed": is_completed
            },
            code=200,
            msg="查询成功"
        )

    except Exception as e:
        logger.error(f"查询任务状态失败: {e}")
        return TaskStateResponse(
            Data={
                "content": f"Internal error: {str(e)}",
                "is_completed": False
            },
            code=500,
            msg="查询失败"
        )
