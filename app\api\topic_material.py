from fastapi import APIRouter, HTTPException
from app.models.schemas import (
    TopicMaterialRequest, TopicMaterialResponse,
    StandardResponse, ErrorResponse
)
from app.services.topic_material_service import topic_material_service
from app.core.state_manager import task_manager, TaskStatus
import uuid
from datetime import datetime
from loguru import logger

router = APIRouter()


@router.post("/GenerateTopicMaterial", response_model=TopicMaterialResponse)
async def generate_topic_material(request: TopicMaterialRequest):
    """
    基于主题生成命题材料
    
    功能：
    - 根据指定主题和学科生成适合命题的材料
    - 支持异步处理和任务状态跟踪
    - 返回生成的材料内容
    """
    try:
        # 生成任务ID
        task_id = request.task_id or str(uuid.uuid4())
        
        # 创建任务
        await task_manager.create_task(task_id)
        
        # 更新任务状态
        await task_manager.update_task_status(
            task_id,
            TaskStatus.PROCESSING,
            step_info={
                "step": "generating_material",
                "progress": 0,
                "detail": f"开始为主题'{request.topic}'生成材料"
            }
        )
        
        logger.info(f"开始生成主题材料: {request.topic} - {request.subject}")
        
        # 调用服务生成材料
        material = await topic_material_service.generate_material(
            topic=request.topic,
            subject=request.subject,
            task_id=task_id
        )
        
        # 更新任务状态为完成
        await task_manager.update_task_status(
            task_id,
            TaskStatus.COMPLETED,
            result={"material": material}
        )
        
        # 构建响应
        response = TopicMaterialResponse(
            material=material,
            topic=request.topic,
            subject=request.subject,
            task_id=task_id,
            generated_at=datetime.now()
        )
        
        logger.info(f"主题材料生成完成: {task_id}")
        return response
        
    except Exception as e:
        logger.error(f"主题材料生成失败: {e}")
        
        # 更新任务状态为失败
        if 'task_id' in locals():
            await task_manager.update_task_status(
                task_id,
                TaskStatus.FAILED,
                error_message=str(e)
            )
        
        raise HTTPException(
            status_code=500,
            detail=f"主题材料生成失败: {str(e)}"
        )


@router.get("/GetTopicMaterialStatus/{task_id}")
async def get_topic_material_status(task_id: str):
    """
    查询主题材料生成任务状态
    """
    try:
        task_info = await task_manager.get_task_info(task_id)
        
        if not task_info:
            raise HTTPException(
                status_code=404,
                detail=f"任务不存在: {task_id}"
            )
        
        return StandardResponse(
            code=200,
            msg="查询成功",
            data={
                "task_id": task_id,
                "status": task_info.status.value,
                "created_at": task_info.created_at,
                "updated_at": task_info.updated_at,
                "result": task_info.result,
                "error_message": task_info.error_message
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询任务状态失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"查询任务状态失败: {str(e)}"
        )
