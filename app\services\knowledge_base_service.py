from typing import List, Dict, Any, Optional
import asyncio
import time
import uuid
import json
from loguru import logger
from app.services.llm_manager import generate_text
from app.core.state_manager import task_manager, TaskStatus


class KnowledgeBaseService:
    """知识库管理服务"""
    
    def __init__(self):
        self.logger = logger
        # 简化实现，使用内存存储
        self.knowledge_store = {}
        self.knowledge_index = {}
    
    async def upload_knowledge(
        self,
        content: Optional[str] = None,
        file_data: Optional[str] = None,
        knowledge_type: str = "文本",
        subject: str = "通用",
        tags: List[str] = None,
        task_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        上传知识内容到知识库
        
        Args:
            content: 文本内容
            file_data: Base64编码的文件数据
            knowledge_type: 知识类型
            subject: 学科
            tags: 标签列表
            task_id: 任务ID
            
        Returns:
            上传结果
        """
        try:
            self.logger.info(f"开始上传知识内容: {knowledge_type}")
            
            # 生成知识ID
            knowledge_id = str(uuid.uuid4())
            
            # 更新任务状态
            if task_id:
                await task_manager.update_task_status(
                    task_id,
                    TaskStatus.PROCESSING,
                    step_info={
                        "step": "processing_content",
                        "progress": 20,
                        "detail": "处理知识内容"
                    }
                )
            
            # 处理内容
            processed_content = await self._process_knowledge_content(
                content, file_data, task_id
            )
            
            # 更新任务状态
            if task_id:
                await task_manager.update_task_status(
                    task_id,
                    TaskStatus.PROCESSING,
                    step_info={
                        "step": "extracting_knowledge",
                        "progress": 50,
                        "detail": "提取知识点"
                    }
                )
            
            # 提取知识点
            extracted_knowledge = await self._extract_knowledge_points(
                processed_content, subject, task_id
            )
            
            # 更新任务状态
            if task_id:
                await task_manager.update_task_status(
                    task_id,
                    TaskStatus.PROCESSING,
                    step_info={
                        "step": "indexing_keywords",
                        "progress": 80,
                        "detail": "建立关键词索引"
                    }
                )
            
            # 建立关键词索引
            indexed_keywords = await self._build_keyword_index(
                processed_content, extracted_knowledge
            )
            
            # 存储知识内容
            knowledge_item = {
                "knowledge_id": knowledge_id,
                "content": processed_content,
                "knowledge_type": knowledge_type,
                "subject": subject,
                "tags": tags or [],
                "extracted_knowledge": extracted_knowledge,
                "indexed_keywords": indexed_keywords,
                "created_at": time.time(),
                "updated_at": time.time()
            }
            
            self.knowledge_store[knowledge_id] = knowledge_item
            
            # 更新索引
            self._update_search_index(knowledge_id, knowledge_item)
            
            result = {
                "knowledge_id": knowledge_id,
                "extracted_knowledge": extracted_knowledge,
                "indexed_keywords": indexed_keywords
            }
            
            self.logger.info(f"知识内容上传成功: {knowledge_id}")
            return result
            
        except Exception as e:
            self.logger.error(f"知识内容上传失败: {e}")
            
            # 更新任务状态为失败
            if task_id:
                await task_manager.update_task_status(
                    task_id,
                    TaskStatus.FAILED,
                    error_message=str(e)
                )
            
            raise
    
    async def _process_knowledge_content(
        self,
        content: Optional[str],
        file_data: Optional[str],
        task_id: Optional[str] = None
    ) -> str:
        """处理知识内容"""
        
        if content:
            return content
        elif file_data:
            # 简化实现，假设已解码文件内容
            return "从文件提取的内容..."
        else:
            raise ValueError("必须提供内容或文件数据")
    
    async def _extract_knowledge_points(
        self,
        content: str,
        subject: str,
        task_id: Optional[str] = None
    ) -> List[str]:
        """提取知识点"""
        
        prompt = f"""请从以下{subject}学科内容中提取主要知识点：

内容：
{content}

请以JSON格式返回知识点列表：
{{
    "knowledge_points": ["知识点1", "知识点2", "知识点3"]
}}"""
        
        try:
            result = await generate_text(prompt, temperature=0.3)
            
            # 尝试解析JSON
            try:
                data = json.loads(result)
                return data.get("knowledge_points", [])
            except json.JSONDecodeError:
                # 解析失败，返回基本知识点
                return [f"{subject}基础知识"]
                
        except Exception as e:
            self.logger.error(f"知识点提取失败: {e}")
            return [f"{subject}相关知识"]
    
    async def _build_keyword_index(
        self,
        content: str,
        knowledge_points: List[str]
    ) -> List[str]:
        """建立关键词索引"""
        
        # 简化实现，提取基本关键词
        keywords = []
        
        # 从内容中提取关键词（简化版）
        words = content.split()
        for word in words:
            if len(word) > 2 and word not in keywords:
                keywords.append(word)
                if len(keywords) >= 20:  # 限制关键词数量
                    break
        
        # 添加知识点作为关键词
        keywords.extend(knowledge_points)
        
        return list(set(keywords))
    
    def _update_search_index(self, knowledge_id: str, knowledge_item: Dict[str, Any]):
        """更新搜索索引"""
        
        # 简化实现，建立基本索引
        for keyword in knowledge_item["indexed_keywords"]:
            if keyword not in self.knowledge_index:
                self.knowledge_index[keyword] = []
            self.knowledge_index[keyword].append(knowledge_id)
    
    async def search_knowledge(
        self,
        query: str,
        subject: Optional[str] = None,
        knowledge_type: Optional[str] = None,
        limit: int = 10,
        task_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """搜索知识库内容"""
        
        start_time = time.time()
        
        # 简化搜索实现
        results = []
        
        # 关键词匹配
        query_words = query.split()
        matched_ids = set()
        
        for word in query_words:
            if word in self.knowledge_index:
                matched_ids.update(self.knowledge_index[word])
        
        # 筛选结果
        for knowledge_id in matched_ids:
            if knowledge_id in self.knowledge_store:
                item = self.knowledge_store[knowledge_id]
                
                # 应用筛选条件
                if subject and item["subject"] != subject:
                    continue
                if knowledge_type and item["knowledge_type"] != knowledge_type:
                    continue
                
                results.append({
                    "knowledge_id": knowledge_id,
                    "content": item["content"][:200] + "..." if len(item["content"]) > 200 else item["content"],
                    "subject": item["subject"],
                    "knowledge_type": item["knowledge_type"],
                    "tags": item["tags"],
                    "relevance_score": 0.8  # 简化的相关度分数
                })
                
                if len(results) >= limit:
                    break
        
        search_time = time.time() - start_time
        
        return {
            "results": results,
            "total_count": len(results),
            "search_time": search_time
        }
    
    async def get_knowledge(self, knowledge_id: str) -> Optional[Dict[str, Any]]:
        """获取特定知识内容"""
        
        return self.knowledge_store.get(knowledge_id)
    
    async def update_knowledge(
        self,
        knowledge_id: str,
        content: Optional[str] = None,
        tags: Optional[List[str]] = None,
        subject: Optional[str] = None,
        task_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """更新知识内容"""
        
        if knowledge_id not in self.knowledge_store:
            raise ValueError(f"知识内容不存在: {knowledge_id}")
        
        item = self.knowledge_store[knowledge_id]
        updated_fields = []
        
        if content is not None:
            item["content"] = content
            updated_fields.append("content")
            
            # 重新提取知识点和关键词
            item["extracted_knowledge"] = await self._extract_knowledge_points(
                content, item["subject"]
            )
            item["indexed_keywords"] = await self._build_keyword_index(
                content, item["extracted_knowledge"]
            )
            updated_fields.extend(["extracted_knowledge", "indexed_keywords"])
        
        if tags is not None:
            item["tags"] = tags
            updated_fields.append("tags")
        
        if subject is not None:
            item["subject"] = subject
            updated_fields.append("subject")
        
        item["updated_at"] = time.time()
        
        # 更新索引
        self._update_search_index(knowledge_id, item)
        
        return {
            "updated_fields": updated_fields,
            "reindexed": "content" in updated_fields
        }
    
    async def delete_knowledge(self, knowledge_id: str) -> Dict[str, Any]:
        """删除知识内容"""
        
        if knowledge_id in self.knowledge_store:
            del self.knowledge_store[knowledge_id]
            
            # 从索引中移除
            for keyword, ids in self.knowledge_index.items():
                if knowledge_id in ids:
                    ids.remove(knowledge_id)
            
            return {"deleted": True}
        else:
            return {"deleted": False}
    
    async def list_knowledge(
        self,
        subject: Optional[str] = None,
        knowledge_type: Optional[str] = None,
        page: int = 1,
        page_size: int = 20
    ) -> Dict[str, Any]:
        """列出知识库内容"""
        
        # 筛选知识项
        filtered_items = []
        for item in self.knowledge_store.values():
            if subject and item["subject"] != subject:
                continue
            if knowledge_type and item["knowledge_type"] != knowledge_type:
                continue
            filtered_items.append(item)
        
        # 分页
        total_count = len(filtered_items)
        total_pages = (total_count + page_size - 1) // page_size
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        
        knowledge_list = filtered_items[start_idx:end_idx]
        
        return {
            "knowledge_list": knowledge_list,
            "total_count": total_count,
            "total_pages": total_pages
        }
    
    async def batch_upload_knowledge(
        self,
        knowledge_items: List[Dict[str, Any]],
        batch_id: str,
        batch_size: int = 10
    ) -> Dict[str, Any]:
        """批量上传知识内容"""
        
        start_time = time.time()
        results = []
        successful_uploads = 0
        failed_uploads = 0
        
        # 分批处理
        for i in range(0, len(knowledge_items), batch_size):
            batch = knowledge_items[i:i + batch_size]
            
            # 并发处理批次
            tasks = []
            for item in batch:
                task = self.upload_knowledge(
                    content=item.get("content"),
                    file_data=item.get("file_data"),
                    knowledge_type=item.get("knowledge_type", "文本"),
                    subject=item.get("subject", "通用"),
                    tags=item.get("tags", [])
                )
                tasks.append(task)
            
            # 等待批次完成
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            for j, result in enumerate(batch_results):
                if isinstance(result, Exception):
                    failed_uploads += 1
                    results.append({
                        "index": i + j,
                        "status": "failed",
                        "error": str(result)
                    })
                else:
                    successful_uploads += 1
                    results.append({
                        "index": i + j,
                        "status": "success",
                        "knowledge_id": result["knowledge_id"]
                    })
        
        upload_time = time.time() - start_time
        
        return {
            "successful_uploads": successful_uploads,
            "failed_uploads": failed_uploads,
            "upload_time": upload_time,
            "results": results
        }
    
    async def analyze_knowledge_gaps(
        self,
        subject: str,
        curriculum_standards: List[str],
        task_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """分析知识库覆盖缺口"""
        
        # 简化实现
        existing_knowledge = []
        for item in self.knowledge_store.values():
            if item["subject"] == subject:
                existing_knowledge.extend(item["extracted_knowledge"])
        
        # 分析缺口
        knowledge_gaps = []
        for standard in curriculum_standards:
            if standard not in existing_knowledge:
                knowledge_gaps.append(standard)
        
        coverage_rate = (len(curriculum_standards) - len(knowledge_gaps)) / len(curriculum_standards) if curriculum_standards else 1.0
        
        return {
            "coverage_analysis": {
                "total_standards": len(curriculum_standards),
                "covered_standards": len(curriculum_standards) - len(knowledge_gaps),
                "coverage_rate": coverage_rate
            },
            "knowledge_gaps": knowledge_gaps,
            "recommendations": [
                f"建议补充以下知识点: {', '.join(knowledge_gaps[:5])}" if knowledge_gaps else "知识库覆盖完整"
            ]
        }


# 创建全局服务实例
knowledge_base_service = KnowledgeBaseService()
