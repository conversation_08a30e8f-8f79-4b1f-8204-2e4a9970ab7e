#!/usr/bin/env python3
"""
调试enable_thinking参数错误的脚本
帮助排查 "enalbe_thinking" 拼写错误问题
"""

import asyncio
import traceback
from typing import Dict, Any
from app.services.llm_manager import LLMManager

async def debug_enable_thinking_error():
    """调试enable_thinking参数错误"""
    
    print("🔍 开始调试enable_thinking参数错误...")
    print("="*60)
    
    # 测试配置
    qwen3_config = {
        "name": "qwen3-turbo",
        "api_key": "test-key",
        "api_base": "https://dashscope.aliyuncs.com/compatible-mode/v1",
        "temperature": 0.7,
        "max_tokens": 500
    }
    
    async with LLMManager() as llm_manager:
        
        # 测试1: 正确的参数名
        print("🔧 测试1: 使用正确的参数名 'enable_thinking'")
        print("-" * 50)
        
        try:
            # 测试正确的参数名
            llm_instance = llm_manager._get_cached_llm_instance(
                qwen3_config, 
                enable_thinking=False
            )
            print("   ✅ 正确参数名测试成功")
            
        except Exception as e:
            print(f"   ❌ 正确参数名测试失败: {e}")
            traceback.print_exc()
        
        print()
        
        # 测试2: 错误的参数名（应该被过滤掉）
        print("🔧 测试2: 使用错误的参数名 'enalbe_thinking'")
        print("-" * 50)
        
        try:
            # 测试错误的参数名（应该被过滤掉）
            llm_instance = llm_manager._get_cached_llm_instance(
                qwen3_config, 
                enalbe_thinking=False  # 故意拼写错误
            )
            print("   ✅ 错误参数名已被正确过滤")
            
        except Exception as e:
            print(f"   ❌ 错误参数名未被过滤，导致错误: {e}")
            traceback.print_exc()
        
        print()
        
        # 测试3: 检查参数过滤逻辑
        print("🔧 测试3: 检查参数过滤逻辑")
        print("-" * 50)
        
        # 模拟参数过滤
        test_kwargs = {
            "enable_thinking": True,
            "enalbe_thinking": False,  # 拼写错误
            "enable_thinkng": True,   # 另一种拼写错误
            "thinking_enable": False,  # 另一种拼写错误
            "temperature": 0.8,       # 应该被过滤
            "valid_param": "test"     # 不应该被过滤
        }
        
        excluded_params = [
            'temperature', 'max_tokens', 'model', 'api_key', 'base_url',
            'top_k', 'top_p', 'frequency_penalty', 'presence_penalty',
            'stop', 'n', 'logit_bias', 'user', "enable_thinking",
            # 常见拼写错误的参数名
            "enalbe_thinking", "enable_thinkng", "thinking_enable"
        ]
        
        filtered_kwargs = {k: v for k, v in test_kwargs.items() if k not in excluded_params}
        
        print(f"   原始参数: {test_kwargs}")
        print(f"   过滤后参数: {filtered_kwargs}")
        print(f"   被过滤的参数: {set(test_kwargs.keys()) - set(filtered_kwargs.keys())}")
        
        # 检查是否还有拼写错误的参数
        error_params = [k for k in filtered_kwargs.keys() if 'thinking' in k.lower()]
        if error_params:
            print(f"   ⚠️  警告: 仍有thinking相关参数未被过滤: {error_params}")
        else:
            print("   ✅ 所有thinking相关参数都被正确过滤")
        
        print()
        
        # 测试4: 模拟实际调用场景
        print("🔧 测试4: 模拟实际调用场景")
        print("-" * 50)
        
        try:
            # 模拟可能出现错误的调用
            result = await llm_manager._call_langchain_api(
                prompt="测试提示词",
                model_config=qwen3_config,
                stream=False,
                # 可能的错误参数
                enalbe_thinking=False  # 故意拼写错误
            )
            print("   ✅ 实际调用测试成功，错误参数已被过滤")
            
        except Exception as e:
            error_msg = str(e)
            if "enalbe_thinking" in error_msg:
                print(f"   ❌ 发现拼写错误参数未被过滤: {e}")
            else:
                print(f"   ⚠️  其他错误: {e}")
            traceback.print_exc()
        
        print()
        
        # 测试5: 检查缓存键生成
        print("🔧 测试5: 检查缓存键生成")
        print("-" * 50)
        
        try:
            # 测试缓存键生成是否会受到错误参数影响
            cache_key_1 = llm_manager._generate_cache_key(
                qwen3_config, 
                enable_thinking=False
            )
            
            cache_key_2 = llm_manager._generate_cache_key(
                qwen3_config, 
                enalbe_thinking=False  # 拼写错误，应该被忽略
            )
            
            print(f"   正确参数缓存键: {cache_key_1}")
            print(f"   错误参数缓存键: {cache_key_2}")
            
            if cache_key_1 == cache_key_2:
                print("   ✅ 缓存键生成不受错误参数影响")
            else:
                print("   ⚠️  缓存键生成受到错误参数影响")
                
        except Exception as e:
            print(f"   ❌ 缓存键生成测试失败: {e}")
        
        print()
        
        # 总结
        print("🎯 调试总结")
        print("="*60)
        print("1. 检查了参数过滤逻辑")
        print("2. 验证了常见拼写错误的处理")
        print("3. 测试了实际调用场景")
        print("4. 检查了缓存键生成")
        print()
        print("💡 如果仍然出现错误，请检查:")
        print("   - 调用栈中是否有其他地方传递了错误参数")
        print("   - 是否有第三方库或配置文件中的拼写错误")
        print("   - 检查完整的错误堆栈信息")

async def check_error_source():
    """检查错误来源"""
    print("\n" + "="*60)
    print("🔍 检查可能的错误来源")
    print("="*60)
    
    # 检查可能传递错误参数的地方
    potential_sources = [
        "智能命题服务中的参数传递",
        "工作流配置中的参数名",
        "配置文件中的参数设置",
        "第三方库的参数传递"
    ]
    
    for i, source in enumerate(potential_sources, 1):
        print(f"{i}. {source}")
    
    print("\n💡 建议排查步骤:")
    print("1. 检查完整的错误堆栈信息")
    print("2. 在LLM管理器中添加参数日志")
    print("3. 检查所有调用LLM的地方")
    print("4. 验证配置文件和工作流配置")

def add_debug_logging():
    """添加调试日志的建议"""
    print("\n" + "="*60)
    print("🔧 添加调试日志建议")
    print("="*60)
    
    debug_code = '''
# 在 _create_llm_instance 方法开始处添加:
logger.debug(f"创建LLM实例，接收到的kwargs: {kwargs}")

# 在参数过滤后添加:
logger.debug(f"过滤后的kwargs: {filtered_kwargs}")

# 在ChatDeepSeek调用前添加:
logger.debug(f"传递给ChatDeepSeek的参数: {filtered_kwargs}")
'''
    
    print("建议在以下位置添加调试日志:")
    print(debug_code)

async def main():
    """主函数"""
    try:
        await debug_enable_thinking_error()
        await check_error_source()
        add_debug_logging()
    except Exception as e:
        print(f"❌ 调试过程中出现错误: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
