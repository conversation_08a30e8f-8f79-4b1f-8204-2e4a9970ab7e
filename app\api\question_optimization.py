from fastapi import APIRouter, HTTPException
from app.models.schemas import (
    QuestionOptimizationRequest, QuestionOptimizationResponse,
    StandardResponse, ErrorResponse
)
from app.services.question_optimization_service import question_optimization_service
from app.core.state_manager import task_manager, TaskStatus
import uuid
from datetime import datetime
from loguru import logger

router = APIRouter()


@router.post("/AnalyzeQuestionQuality", response_model=QuestionOptimizationResponse)
async def analyze_question_quality(request: QuestionOptimizationRequest):
    """
    分析试题质量
    
    功能：
    - 评估试题的准确性、清晰度、难度匹配度
    - 检查题目格式和结构
    - 分析选项的合理性和区分度
    - 提供详细的质量报告
    """
    try:
        # 生成任务ID
        task_id = request.task_id or str(uuid.uuid4())
        
        # 创建任务
        await task_manager.create_task(task_id)
        
        # 更新任务状态
        await task_manager.update_task_status(
            task_id,
            TaskStatus.PROCESSING,
            step_info={
                "step": "analyzing_quality",
                "progress": 0,
                "detail": "开始分析试题质量"
            }
        )
        
        logger.info(f"开始分析试题质量: {task_id}")
        
        # 调用服务分析试题质量
        result = await question_optimization_service.analyze_question_quality(
            questions=request.questions,
            analysis_criteria=request.analysis_criteria,
            task_id=task_id
        )
        
        # 更新任务状态为完成
        await task_manager.update_task_status(
            task_id,
            TaskStatus.COMPLETED,
            result=result
        )
        
        # 构建响应
        response = QuestionOptimizationResponse(
            optimization_results=result["optimization_results"],
            overall_score=result["overall_score"],
            improvement_suggestions=result["improvement_suggestions"],
            task_id=task_id,
            analyzed_at=datetime.now()
        )
        
        logger.info(f"试题质量分析完成: {task_id}")
        return response
        
    except Exception as e:
        logger.error(f"试题质量分析失败: {e}")
        
        # 更新任务状态为失败
        if 'task_id' in locals():
            await task_manager.update_task_status(
                task_id,
                TaskStatus.FAILED,
                error_message=str(e)
            )
        
        raise HTTPException(
            status_code=500,
            detail=f"试题质量分析失败: {str(e)}"
        )


@router.post("/OptimizeQuestions")
async def optimize_questions(
    questions: list,
    optimization_goals: list = None,
    auto_apply: bool = False
):
    """
    优化试题
    
    功能：
    - 基于质量分析结果优化试题
    - 改进题目表述和选项设计
    - 调整难度和知识点覆盖
    - 提供优化前后对比
    """
    try:
        task_id = str(uuid.uuid4())
        
        # 创建任务
        await task_manager.create_task(task_id)
        
        logger.info(f"开始优化试题: {task_id}")
        
        # 调用服务优化试题
        result = await question_optimization_service.optimize_questions(
            questions=questions,
            optimization_goals=optimization_goals or [],
            auto_apply=auto_apply,
            task_id=task_id
        )
        
        # 更新任务状态为完成
        await task_manager.update_task_status(
            task_id,
            TaskStatus.COMPLETED,
            result=result
        )
        
        return StandardResponse(
            code=200,
            msg="试题优化完成",
            data={
                "task_id": task_id,
                "optimized_questions": result["optimized_questions"],
                "optimization_summary": result["optimization_summary"],
                "improvement_metrics": result.get("improvement_metrics", {})
            }
        )
        
    except Exception as e:
        logger.error(f"试题优化失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"试题优化失败: {str(e)}"
        )


@router.post("/ValidateQuestions")
async def validate_questions(questions: list, validation_rules: list = None):
    """
    验证试题规范性
    
    功能：
    - 检查试题格式规范
    - 验证答案正确性
    - 检查选项逻辑性
    - 识别潜在问题
    """
    try:
        task_id = str(uuid.uuid4())
        
        logger.info(f"开始验证试题: {task_id}")
        
        # 调用服务验证试题
        result = await question_optimization_service.validate_questions(
            questions=questions,
            validation_rules=validation_rules or [],
            task_id=task_id
        )
        
        return StandardResponse(
            code=200,
            msg="试题验证完成",
            data={
                "task_id": task_id,
                "validation_results": result["validation_results"],
                "overall_validity": result["overall_validity"],
                "issues_found": result.get("issues_found", [])
            }
        )
        
    except Exception as e:
        logger.error(f"试题验证失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"试题验证失败: {str(e)}"
        )


@router.post("/CompareQuestions")
async def compare_questions(
    original_questions: list,
    optimized_questions: list
):
    """
    对比试题优化效果
    
    功能：
    - 对比优化前后的试题质量
    - 分析改进程度
    - 提供详细的对比报告
    """
    try:
        task_id = str(uuid.uuid4())
        
        logger.info(f"开始对比试题: {task_id}")
        
        # 调用服务对比试题
        result = await question_optimization_service.compare_questions(
            original_questions=original_questions,
            optimized_questions=optimized_questions,
            task_id=task_id
        )
        
        return StandardResponse(
            code=200,
            msg="试题对比完成",
            data={
                "task_id": task_id,
                "comparison_report": result["comparison_report"],
                "improvement_score": result["improvement_score"],
                "detailed_analysis": result.get("detailed_analysis", {})
            }
        )
        
    except Exception as e:
        logger.error(f"试题对比失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"试题对比失败: {str(e)}"
        )


@router.get("/GetOptimizationStatus/{task_id}")
async def get_optimization_status(task_id: str):
    """
    查询优化任务状态
    """
    try:
        task_info = await task_manager.get_task_info(task_id)
        
        if not task_info:
            raise HTTPException(
                status_code=404,
                detail=f"任务不存在: {task_id}"
            )
        
        return StandardResponse(
            code=200,
            msg="查询成功",
            data={
                "task_id": task_id,
                "status": task_info.status.value,
                "created_at": task_info.created_at,
                "updated_at": task_info.updated_at,
                "result": task_info.result,
                "error_message": task_info.error_message
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询任务状态失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"查询任务状态失败: {str(e)}"
        )


@router.post("/GenerateOptimizationReport")
async def generate_optimization_report(
    questions: list,
    analysis_results: dict = None
):
    """
    生成优化报告
    
    功能：
    - 生成详细的试题质量报告
    - 包含统计分析和可视化数据
    - 提供改进建议和最佳实践
    """
    try:
        task_id = str(uuid.uuid4())
        
        logger.info(f"开始生成优化报告: {task_id}")
        
        # 调用服务生成报告
        result = await question_optimization_service.generate_optimization_report(
            questions=questions,
            analysis_results=analysis_results,
            task_id=task_id
        )
        
        return StandardResponse(
            code=200,
            msg="优化报告生成完成",
            data={
                "task_id": task_id,
                "report": result["report"],
                "statistics": result["statistics"],
                "recommendations": result.get("recommendations", [])
            }
        )
        
    except Exception as e:
        logger.error(f"优化报告生成失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"优化报告生成失败: {str(e)}"
        )
