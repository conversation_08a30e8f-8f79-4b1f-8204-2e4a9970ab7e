from fastapi import APIRouter, HTTPException, UploadFile, File
from app.models.schemas import (
    KnowledgeBaseRequest, KnowledgeBaseResponse,
    StandardResponse, ErrorResponse
)
from app.services.knowledge_base_service import knowledge_base_service
from app.core.state_manager import task_manager, TaskStatus
import uuid
from datetime import datetime
from loguru import logger

router = APIRouter()


@router.post("/UploadKnowledge", response_model=KnowledgeBaseResponse)
async def upload_knowledge(request: KnowledgeBaseRequest):
    """
    上传知识内容到知识库
    
    功能：
    - 支持文本内容和文件上传
    - 自动提取和索引知识点
    - 建立知识关联关系
    - 支持批量上传
    """
    try:
        # 生成任务ID
        task_id = request.task_id or str(uuid.uuid4())
        
        # 创建任务
        await task_manager.create_task(task_id)
        
        # 更新任务状态
        await task_manager.update_task_status(
            task_id,
            TaskStatus.PROCESSING,
            step_info={
                "step": "uploading_knowledge",
                "progress": 0,
                "detail": "开始上传知识内容"
            }
        )
        
        logger.info(f"开始上传知识内容: {task_id}")
        
        # 调用服务上传知识
        result = await knowledge_base_service.upload_knowledge(
            content=request.content,
            file_data=request.file_data,
            knowledge_type=request.knowledge_type,
            subject=request.subject,
            tags=request.tags,
            task_id=task_id
        )
        
        # 更新任务状态为完成
        await task_manager.update_task_status(
            task_id,
            TaskStatus.COMPLETED,
            result=result
        )
        
        # 构建响应
        response = KnowledgeBaseResponse(
            knowledge_id=result["knowledge_id"],
            extracted_knowledge=result["extracted_knowledge"],
            indexed_keywords=result["indexed_keywords"],
            task_id=task_id,
            uploaded_at=datetime.now()
        )
        
        logger.info(f"知识内容上传完成: {task_id}")
        return response
        
    except Exception as e:
        logger.error(f"知识内容上传失败: {e}")
        
        # 更新任务状态为失败
        if 'task_id' in locals():
            await task_manager.update_task_status(
                task_id,
                TaskStatus.FAILED,
                error_message=str(e)
            )
        
        raise HTTPException(
            status_code=500,
            detail=f"知识内容上传失败: {str(e)}"
        )


@router.post("/SearchKnowledge")
async def search_knowledge(
    query: str,
    subject: str = None,
    knowledge_type: str = None,
    limit: int = 10
):
    """
    搜索知识库内容
    
    功能：
    - 支持关键词搜索
    - 支持语义搜索
    - 按学科和类型筛选
    - 返回相关度排序结果
    """
    try:
        task_id = str(uuid.uuid4())
        
        logger.info(f"开始搜索知识库: {query}")
        
        # 调用服务搜索知识
        result = await knowledge_base_service.search_knowledge(
            query=query,
            subject=subject,
            knowledge_type=knowledge_type,
            limit=limit,
            task_id=task_id
        )
        
        return StandardResponse(
            code=200,
            msg="搜索完成",
            data={
                "task_id": task_id,
                "query": query,
                "results": result["results"],
                "total_count": result["total_count"],
                "search_time": result.get("search_time", 0)
            }
        )
        
    except Exception as e:
        logger.error(f"知识库搜索失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"知识库搜索失败: {str(e)}"
        )


@router.get("/GetKnowledge/{knowledge_id}")
async def get_knowledge(knowledge_id: str):
    """
    获取特定知识内容
    """
    try:
        result = await knowledge_base_service.get_knowledge(knowledge_id)
        
        if not result:
            raise HTTPException(
                status_code=404,
                detail=f"知识内容不存在: {knowledge_id}"
            )
        
        return StandardResponse(
            code=200,
            msg="获取成功",
            data=result
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取知识内容失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取知识内容失败: {str(e)}"
        )


@router.post("/UpdateKnowledge/{knowledge_id}")
async def update_knowledge(
    knowledge_id: str,
    content: str = None,
    tags: list = None,
    subject: str = None
):
    """
    更新知识内容
    
    功能：
    - 更新知识内容
    - 修改标签和分类
    - 重新索引关键词
    """
    try:
        task_id = str(uuid.uuid4())
        
        logger.info(f"开始更新知识内容: {knowledge_id}")
        
        # 调用服务更新知识
        result = await knowledge_base_service.update_knowledge(
            knowledge_id=knowledge_id,
            content=content,
            tags=tags,
            subject=subject,
            task_id=task_id
        )
        
        return StandardResponse(
            code=200,
            msg="更新成功",
            data={
                "task_id": task_id,
                "knowledge_id": knowledge_id,
                "updated_fields": result["updated_fields"],
                "reindexed": result.get("reindexed", False)
            }
        )
        
    except Exception as e:
        logger.error(f"知识内容更新失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"知识内容更新失败: {str(e)}"
        )


@router.delete("/DeleteKnowledge/{knowledge_id}")
async def delete_knowledge(knowledge_id: str):
    """
    删除知识内容
    """
    try:
        result = await knowledge_base_service.delete_knowledge(knowledge_id)
        
        return StandardResponse(
            code=200,
            msg="删除成功",
            data={
                "knowledge_id": knowledge_id,
                "deleted": result["deleted"]
            }
        )
        
    except Exception as e:
        logger.error(f"知识内容删除失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"知识内容删除失败: {str(e)}"
        )


@router.get("/ListKnowledge")
async def list_knowledge(
    subject: str = None,
    knowledge_type: str = None,
    page: int = 1,
    page_size: int = 20
):
    """
    列出知识库内容
    
    功能：
    - 分页列出知识内容
    - 按学科和类型筛选
    - 支持排序
    """
    try:
        result = await knowledge_base_service.list_knowledge(
            subject=subject,
            knowledge_type=knowledge_type,
            page=page,
            page_size=page_size
        )
        
        return StandardResponse(
            code=200,
            msg="获取成功",
            data={
                "knowledge_list": result["knowledge_list"],
                "total_count": result["total_count"],
                "page": page,
                "page_size": page_size,
                "total_pages": result["total_pages"]
            }
        )
        
    except Exception as e:
        logger.error(f"获取知识列表失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取知识列表失败: {str(e)}"
        )


@router.post("/BatchUploadKnowledge")
async def batch_upload_knowledge(
    knowledge_items: list,
    batch_size: int = 10
):
    """
    批量上传知识内容
    
    功能：
    - 支持批量上传多个知识项
    - 并发处理提高效率
    - 提供批量处理统计
    """
    try:
        batch_id = str(uuid.uuid4())
        
        logger.info(f"开始批量上传知识: {batch_id}, {len(knowledge_items)} 项")
        
        # 调用服务批量上传
        result = await knowledge_base_service.batch_upload_knowledge(
            knowledge_items=knowledge_items,
            batch_id=batch_id,
            batch_size=batch_size
        )
        
        return StandardResponse(
            code=200,
            msg="批量上传完成",
            data={
                "batch_id": batch_id,
                "total_items": len(knowledge_items),
                "successful_uploads": result["successful_uploads"],
                "failed_uploads": result["failed_uploads"],
                "upload_time": result["upload_time"],
                "results": result["results"]
            }
        )
        
    except Exception as e:
        logger.error(f"批量上传失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"批量上传失败: {str(e)}"
        )


@router.post("/AnalyzeKnowledgeGaps")
async def analyze_knowledge_gaps(
    subject: str,
    curriculum_standards: list = None
):
    """
    分析知识库覆盖缺口
    
    功能：
    - 分析知识库在特定学科的覆盖情况
    - 识别知识点缺口
    - 提供补充建议
    """
    try:
        task_id = str(uuid.uuid4())
        
        logger.info(f"开始分析知识缺口: {subject}")
        
        # 调用服务分析缺口
        result = await knowledge_base_service.analyze_knowledge_gaps(
            subject=subject,
            curriculum_standards=curriculum_standards or [],
            task_id=task_id
        )
        
        return StandardResponse(
            code=200,
            msg="分析完成",
            data={
                "task_id": task_id,
                "subject": subject,
                "coverage_analysis": result["coverage_analysis"],
                "knowledge_gaps": result["knowledge_gaps"],
                "recommendations": result.get("recommendations", [])
            }
        )
        
    except Exception as e:
        logger.error(f"知识缺口分析失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"知识缺口分析失败: {str(e)}"
        )
