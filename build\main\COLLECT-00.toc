([('智能命题—0710.exe',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\build\\main\\智能命题—0710.exe',
   'EXECUTABLE'),
  ('python311.dll', 'D:\\Miniconda\\envs\\give_tag\\python311.dll', 'BINARY'),
  ('select.pyd',
   'D:\\Miniconda\\envs\\give_tag\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_lzma.pyd', 'D:\\Miniconda\\envs\\give_tag\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\Miniconda\\envs\\give_tag\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\Miniconda\\envs\\give_tag\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\Miniconda\\envs\\give_tag\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'D:\\Miniconda\\envs\\give_tag\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\Miniconda\\envs\\give_tag\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\Miniconda\\envs\\give_tag\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\Miniconda\\envs\\give_tag\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'D:\\Miniconda\\envs\\give_tag\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_queue.pyd',
   'D:\\Miniconda\\envs\\give_tag\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\Miniconda\\envs\\give_tag\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'D:\\Miniconda\\envs\\give_tag\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\Miniconda\\envs\\give_tag\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'D:\\Miniconda\\envs\\give_tag\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('pydantic_core\\_pydantic_core.cp311-win_amd64.pyd',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\pydantic_core\\_pydantic_core.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_zoneinfo.pyd',
   'D:\\Miniconda\\envs\\give_tag\\DLLs\\_zoneinfo.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp311-win_amd64.pyd',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\yaml\\_yaml.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('zstandard\\_cffi.cp311-win_amd64.pyd',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\zstandard\\_cffi.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('zstandard\\backend_c.cp311-win_amd64.pyd',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\zstandard\\backend_c.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp311-win_amd64.pyd',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\charset_normalizer\\md.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'D:\\Miniconda\\envs\\give_tag\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('orjson\\orjson.cp311-win_amd64.pyd',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\orjson\\orjson.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('jiter\\jiter.cp311-win_amd64.pyd',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\jiter\\jiter.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('websockets\\speedups.cp311-win_amd64.pyd',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets\\speedups.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('regex\\_regex.cp311-win_amd64.pyd',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\regex\\_regex.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('tiktoken\\_tiktoken.cp311-win_amd64.pyd',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\tiktoken\\_tiktoken.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('yarl\\_quoting_c.cp311-win_amd64.pyd',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\yarl\\_quoting_c.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('propcache\\_helpers_c.cp311-win_amd64.pyd',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\propcache\\_helpers_c.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('multidict\\_multidict.cp311-win_amd64.pyd',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\multidict\\_multidict.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('aiohttp\\_http_writer.cp311-win_amd64.pyd',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\_http_writer.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('aiohttp\\_http_parser.cp311-win_amd64.pyd',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\_http_parser.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('frozenlist\\_frozenlist.cp311-win_amd64.pyd',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\frozenlist\\_frozenlist.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('aiohttp\\_websocket\\mask.cp311-win_amd64.pyd',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\_websocket\\mask.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('aiohttp\\_websocket\\reader_c.cp311-win_amd64.pyd',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\aiohttp\\_websocket\\reader_c.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('watchfiles\\_rust_notify.cp311-win_amd64.pyd',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\watchfiles\\_rust_notify.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('httptools\\parser\\url_parser.cp311-win_amd64.pyd',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httptools\\parser\\url_parser.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('httptools\\parser\\parser.cp311-win_amd64.pyd',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\httptools\\parser\\parser.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('zlib.dll', 'D:\\Miniconda\\envs\\give_tag\\zlib.dll', 'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'D:\\Miniconda\\envs\\give_tag\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('liblzma.dll',
   'D:\\Miniconda\\envs\\give_tag\\Library\\bin\\liblzma.dll',
   'BINARY'),
  ('LIBBZ2.dll',
   'D:\\Miniconda\\envs\\give_tag\\Library\\bin\\LIBBZ2.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'D:\\Miniconda\\envs\\give_tag\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('ffi.dll', 'D:\\Miniconda\\envs\\give_tag\\Library\\bin\\ffi.dll', 'BINARY'),
  ('libssl-3-x64.dll',
   'D:\\Miniconda\\envs\\give_tag\\Library\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('libexpat.dll',
   'D:\\Miniconda\\envs\\give_tag\\Library\\bin\\libexpat.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('python3.dll', 'D:\\Miniconda\\envs\\give_tag\\python3.dll', 'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('ucrtbase.dll', 'D:\\Miniconda\\envs\\give_tag\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\Miniconda\\envs\\give_tag\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('app\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\__init__.py',
   'DATA'),
  ('app\\__pycache__\\__init__.cpython-310.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\__pycache__\\__init__.cpython-310.pyc',
   'DATA'),
  ('app\\__pycache__\\__init__.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\__pycache__\\__init__.cpython-311.pyc',
   'DATA'),
  ('app\\api\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\api\\__init__.py',
   'DATA'),
  ('app\\api\\__pycache__\\__init__.cpython-310.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\api\\__pycache__\\__init__.cpython-310.pyc',
   'DATA'),
  ('app\\api\\__pycache__\\__init__.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\api\\__pycache__\\__init__.cpython-311.pyc',
   'DATA'),
  ('app\\api\\__pycache__\\agent.cpython-310.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\api\\__pycache__\\agent.cpython-310.pyc',
   'DATA'),
  ('app\\api\\__pycache__\\agent.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\api\\__pycache__\\agent.cpython-311.pyc',
   'DATA'),
  ('app\\api\\__pycache__\\stream.cpython-310.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\api\\__pycache__\\stream.cpython-310.pyc',
   'DATA'),
  ('app\\api\\__pycache__\\stream.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\api\\__pycache__\\stream.cpython-311.pyc',
   'DATA'),
  ('app\\api\\__pycache__\\task.cpython-310.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\api\\__pycache__\\task.cpython-310.pyc',
   'DATA'),
  ('app\\api\\__pycache__\\task.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\api\\__pycache__\\task.cpython-311.pyc',
   'DATA'),
  ('app\\api\\agent.py',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\api\\agent.py',
   'DATA'),
  ('app\\api\\stream.py',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\api\\stream.py',
   'DATA'),
  ('app\\api\\task.py',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\api\\task.py',
   'DATA'),
  ('app\\core\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\core\\__init__.py',
   'DATA'),
  ('app\\core\\__pycache__\\__init__.cpython-310.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\core\\__pycache__\\__init__.cpython-310.pyc',
   'DATA'),
  ('app\\core\\__pycache__\\__init__.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\core\\__pycache__\\__init__.cpython-311.pyc',
   'DATA'),
  ('app\\core\\__pycache__\\state_manager.cpython-310.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\core\\__pycache__\\state_manager.cpython-310.pyc',
   'DATA'),
  ('app\\core\\__pycache__\\state_manager.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\core\\__pycache__\\state_manager.cpython-311.pyc',
   'DATA'),
  ('app\\core\\errors.py',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\core\\errors.py',
   'DATA'),
  ('app\\core\\state_manager.py',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\core\\state_manager.py',
   'DATA'),
  ('app\\models\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\models\\__init__.py',
   'DATA'),
  ('app\\models\\__pycache__\\__init__.cpython-310.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\models\\__pycache__\\__init__.cpython-310.pyc',
   'DATA'),
  ('app\\models\\__pycache__\\__init__.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\models\\__pycache__\\__init__.cpython-311.pyc',
   'DATA'),
  ('app\\models\\__pycache__\\schemas.cpython-310.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\models\\__pycache__\\schemas.cpython-310.pyc',
   'DATA'),
  ('app\\models\\__pycache__\\schemas.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\models\\__pycache__\\schemas.cpython-311.pyc',
   'DATA'),
  ('app\\models\\schemas.py',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\models\\schemas.py',
   'DATA'),
  ('app\\services\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\services\\__init__.py',
   'DATA'),
  ('app\\services\\__pycache__\\__init__.cpython-310.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\services\\__pycache__\\__init__.cpython-310.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\__init__.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\services\\__pycache__\\__init__.cpython-311.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\intelligent_question_service.cpython-310.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\services\\__pycache__\\intelligent_question_service.cpython-310.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\intelligent_question_service.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\services\\__pycache__\\intelligent_question_service.cpython-311.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\llm_manager.cpython-310.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\services\\__pycache__\\llm_manager.cpython-310.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\llm_manager.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\services\\__pycache__\\llm_manager.cpython-311.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\question_cloner.cpython-310.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\services\\__pycache__\\question_cloner.cpython-310.pyc',
   'DATA'),
  ('app\\services\\__pycache__\\question_cloner.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\services\\__pycache__\\question_cloner.cpython-311.pyc',
   'DATA'),
  ('app\\services\\intelligent_question_service.py',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\services\\intelligent_question_service.py',
   'DATA'),
  ('app\\services\\llm_manager.py',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\services\\llm_manager.py',
   'DATA'),
  ('app\\services\\question_cloner.py',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\services\\question_cloner.py',
   'DATA'),
  ('app\\utils\\__init__.py',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\utils\\__init__.py',
   'DATA'),
  ('app\\utils\\__pycache__\\__init__.cpython-310.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\utils\\__pycache__\\__init__.cpython-310.pyc',
   'DATA'),
  ('app\\utils\\__pycache__\\__init__.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\utils\\__pycache__\\__init__.cpython-311.pyc',
   'DATA'),
  ('app\\utils\\__pycache__\\config_manager.cpython-310.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\utils\\__pycache__\\config_manager.cpython-310.pyc',
   'DATA'),
  ('app\\utils\\__pycache__\\config_manager.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\utils\\__pycache__\\config_manager.cpython-311.pyc',
   'DATA'),
  ('app\\utils\\__pycache__\\parameter_mapper.cpython-310.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\utils\\__pycache__\\parameter_mapper.cpython-310.pyc',
   'DATA'),
  ('app\\utils\\__pycache__\\parameter_mapper.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\utils\\__pycache__\\parameter_mapper.cpython-311.pyc',
   'DATA'),
  ('app\\utils\\__pycache__\\ques_business_info.cpython-310.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\utils\\__pycache__\\ques_business_info.cpython-310.pyc',
   'DATA'),
  ('app\\utils\\__pycache__\\ques_business_info.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\utils\\__pycache__\\ques_business_info.cpython-311.pyc',
   'DATA'),
  ('app\\utils\\__pycache__\\ques_props_extractor.cpython-310.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\utils\\__pycache__\\ques_props_extractor.cpython-310.pyc',
   'DATA'),
  ('app\\utils\\__pycache__\\ques_props_extractor.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\utils\\__pycache__\\ques_props_extractor.cpython-311.pyc',
   'DATA'),
  ('app\\utils\\__pycache__\\question_parser.cpython-310.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\utils\\__pycache__\\question_parser.cpython-310.pyc',
   'DATA'),
  ('app\\utils\\__pycache__\\question_parser.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\utils\\__pycache__\\question_parser.cpython-311.pyc',
   'DATA'),
  ('app\\utils\\__pycache__\\workflow_utils.cpython-310.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\utils\\__pycache__\\workflow_utils.cpython-310.pyc',
   'DATA'),
  ('app\\utils\\__pycache__\\workflow_utils.cpython-311.pyc',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\utils\\__pycache__\\workflow_utils.cpython-311.pyc',
   'DATA'),
  ('app\\utils\\config_manager.py',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\utils\\config_manager.py',
   'DATA'),
  ('app\\utils\\parameter_mapper.py',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\utils\\parameter_mapper.py',
   'DATA'),
  ('app\\utils\\ques_business_info.py',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\utils\\ques_business_info.py',
   'DATA'),
  ('app\\utils\\ques_props_extractor.py',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\utils\\ques_props_extractor.py',
   'DATA'),
  ('app\\utils\\question_parser.py',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\utils\\question_parser.py',
   'DATA'),
  ('app\\utils\\workflow_utils.py',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\app\\utils\\workflow_utils.py',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('attrs-25.3.0.dist-info\\METADATA',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\attrs-25.3.0.dist-info\\METADATA',
   'DATA'),
  ('urllib3-2.5.0.dist-info\\REQUESTED',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3-2.5.0.dist-info\\REQUESTED',
   'DATA'),
  ('wheel-0.45.1.dist-info\\LICENSE.txt',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('wheel-0.45.1.dist-info\\METADATA',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\wheel-0.45.1.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('click-8.2.1.dist-info\\METADATA',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\click-8.2.1.dist-info\\METADATA',
   'DATA'),
  ('websockets-15.0.1.dist-info\\METADATA',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets-15.0.1.dist-info\\METADATA',
   'DATA'),
  ('urllib3-2.5.0.dist-info\\WHEEL',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3-2.5.0.dist-info\\WHEEL',
   'DATA'),
  ('websockets-15.0.1.dist-info\\top_level.txt',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets-15.0.1.dist-info\\top_level.txt',
   'DATA'),
  ('urllib3-2.5.0.dist-info\\licenses\\LICENSE.txt',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3-2.5.0.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('attrs-25.3.0.dist-info\\WHEEL',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\attrs-25.3.0.dist-info\\WHEEL',
   'DATA'),
  ('websockets-15.0.1.dist-info\\REQUESTED',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets-15.0.1.dist-info\\REQUESTED',
   'DATA'),
  ('websockets-15.0.1.dist-info\\WHEEL',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets-15.0.1.dist-info\\WHEEL',
   'DATA'),
  ('websockets-15.0.1.dist-info\\entry_points.txt',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets-15.0.1.dist-info\\entry_points.txt',
   'DATA'),
  ('urllib3-2.5.0.dist-info\\RECORD',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3-2.5.0.dist-info\\RECORD',
   'DATA'),
  ('wheel-0.45.1.dist-info\\entry_points.txt',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\wheel-0.45.1.dist-info\\entry_points.txt',
   'DATA'),
  ('attrs-25.3.0.dist-info\\INSTALLER',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\attrs-25.3.0.dist-info\\INSTALLER',
   'DATA'),
  ('wheel-0.45.1.dist-info\\RECORD',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\wheel-0.45.1.dist-info\\RECORD',
   'DATA'),
  ('urllib3-2.5.0.dist-info\\METADATA',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3-2.5.0.dist-info\\METADATA',
   'DATA'),
  ('websockets-15.0.1.dist-info\\INSTALLER',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets-15.0.1.dist-info\\INSTALLER',
   'DATA'),
  ('click-8.2.1.dist-info\\WHEEL',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\click-8.2.1.dist-info\\WHEEL',
   'DATA'),
  ('attrs-25.3.0.dist-info\\REQUESTED',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\attrs-25.3.0.dist-info\\REQUESTED',
   'DATA'),
  ('click-8.2.1.dist-info\\RECORD',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\click-8.2.1.dist-info\\RECORD',
   'DATA'),
  ('websockets-15.0.1.dist-info\\RECORD',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets-15.0.1.dist-info\\RECORD',
   'DATA'),
  ('websockets-15.0.1.dist-info\\LICENSE',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\websockets-15.0.1.dist-info\\LICENSE',
   'DATA'),
  ('wheel-0.45.1.dist-info\\WHEEL',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\wheel-0.45.1.dist-info\\WHEEL',
   'DATA'),
  ('click-8.2.1.dist-info\\REQUESTED',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\click-8.2.1.dist-info\\REQUESTED',
   'DATA'),
  ('attrs-25.3.0.dist-info\\RECORD',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\attrs-25.3.0.dist-info\\RECORD',
   'DATA'),
  ('click-8.2.1.dist-info\\INSTALLER',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\click-8.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('urllib3-2.5.0.dist-info\\INSTALLER',
   'D:\\Miniconda\\envs\\give_tag\\Lib\\site-packages\\urllib3-2.5.0.dist-info\\INSTALLER',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\工作\\智能命题\\build\\main\\base_library.zip',
   'DATA')],)
