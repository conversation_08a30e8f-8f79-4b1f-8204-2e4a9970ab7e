#!/usr/bin/env python3
"""
智能命题性能测试脚本
用于验证优化后的性能提升效果
"""

import asyncio
import time
import json
import aiohttp
from typing import Dict, Any
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PerformanceTest:
    """性能测试类"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_basic_question_generation(self) -> Dict[str, Any]:
        """测试基础题型生成性能"""
        logger.info("开始测试基础题型生成性能...")
        
        # 测试数据
        test_data = {
            "QuesPost": {
                "ExamProjectName": "性能测试项目",
                "ExamSubjectName": "语文",
                "AiQuesTypePost": {
                    "QuesCount": 3,  # 生成3道题测试并发性能
                    "BaseName": "选择题",
                    "QuesTypeName": "单选题",
                    "Elements": []
                }
            },
            "WorkflowPost": {
                "NodeList": [
                    {
                        "NodeName": "智能命题节点",
                        "NodeType": "llm",
                        "NodeContent": [
                            {
                                "NodePrompt": {
                                    "function": "智能命题",
                                    "总体任务": "生成高质量试题"
                                },
                                "NodeModel": {
                                    "ModelName": "deepseek-r1",
                                    "ModelArgs": {
                                        "TopP": "0.7",
                                        "TopK": "5",
                                        "Temperature": "0.7",
                                        "MaxToken": "4000",
                                        "ApiKey": "test-key",
                                        "ChatUrl": "http://localhost:8082/v1"
                                    }
                                }
                            }
                        ]
                    }
                ]
            },
            "AgentPost": {
                "NodeContent": [],
                "ChatContent": ""
            },
            "TaskName": "智能命题",
            "TaskId": f"perf_test_{int(time.time())}"
        }
        
        start_time = time.time()
        
        try:
            async with self.session.post(
                f"{self.base_url}/AgentGen",
                json=test_data,
                timeout=aiohttp.ClientTimeout(total=300)
            ) as response:
                result = await response.json()
                end_time = time.time()
                
                duration = end_time - start_time
                
                return {
                    "test_type": "基础题型生成",
                    "duration": duration,
                    "success": response.status == 200,
                    "question_count": 3,
                    "avg_time_per_question": duration / 3,
                    "response": result
                }
                
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            return {
                "test_type": "基础题型生成",
                "duration": duration,
                "success": False,
                "error": str(e),
                "question_count": 3
            }
    
    async def test_composite_question_generation(self) -> Dict[str, Any]:
        """测试组合题生成性能"""
        logger.info("开始测试组合题生成性能...")
        
        # 测试数据
        test_data = {
            "QuesPost": {
                "ExamProjectName": "性能测试项目",
                "ExamSubjectName": "语文",
                "AiQuesTypePost": {
                    "QuesCount": 1,
                    "BaseName": "阅读理解",
                    "QuesTypeName": "组合题",
                    "Elements": [
                        {
                            "ElementType": "langMaterial",
                            "ElementText": []
                        }
                    ],
                    "Childs": [
                        {
                            "BaseName": "选择题",
                            "QuesTypeName": "单选题",
                            "QuesCount": 2
                        },
                        {
                            "BaseName": "简答题",
                            "QuesTypeName": "简答题",
                            "QuesCount": 1
                        }
                    ]
                }
            },
            "WorkflowPost": {
                "NodeList": [
                    {
                        "NodeName": "智能命题节点",
                        "NodeType": "llm",
                        "NodeContent": [
                            {
                                "NodePrompt": {
                                    "function": "智能命题",
                                    "总体任务": "生成高质量组合题"
                                },
                                "NodeModel": {
                                    "ModelName": "deepseek-r1",
                                    "ModelArgs": {
                                        "TopP": "0.7",
                                        "TopK": "5",
                                        "Temperature": "0.7",
                                        "MaxToken": "4000",
                                        "ApiKey": "test-key",
                                        "ChatUrl": "http://localhost:8082/v1"
                                    }
                                }
                            }
                        ]
                    }
                ]
            },
            "AgentPost": {
                "NodeContent": [],
                "ChatContent": ""
            },
            "TaskName": "智能命题",
            "TaskId": f"perf_test_composite_{int(time.time())}"
        }
        
        start_time = time.time()
        
        try:
            async with self.session.post(
                f"{self.base_url}/AgentGen",
                json=test_data,
                timeout=aiohttp.ClientTimeout(total=600)
            ) as response:
                result = await response.json()
                end_time = time.time()
                
                duration = end_time - start_time
                
                return {
                    "test_type": "组合题生成",
                    "duration": duration,
                    "success": response.status == 200,
                    "subquestion_count": 3,
                    "avg_time_per_subquestion": duration / 3,
                    "response": result
                }
                
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            return {
                "test_type": "组合题生成",
                "duration": duration,
                "success": False,
                "error": str(e),
                "subquestion_count": 3
            }
    
    async def run_performance_tests(self) -> Dict[str, Any]:
        """运行所有性能测试"""
        logger.info("开始运行性能测试套件...")
        
        results = {
            "test_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "tests": []
        }
        
        # 测试基础题型生成
        basic_result = await self.test_basic_question_generation()
        results["tests"].append(basic_result)
        
        # 等待一段时间再测试组合题
        await asyncio.sleep(2)
        
        # 测试组合题生成
        composite_result = await self.test_composite_question_generation()
        results["tests"].append(composite_result)
        
        # 计算总体统计
        total_duration = sum(test["duration"] for test in results["tests"])
        success_count = sum(1 for test in results["tests"] if test["success"])
        
        results["summary"] = {
            "total_tests": len(results["tests"]),
            "successful_tests": success_count,
            "total_duration": total_duration,
            "average_duration": total_duration / len(results["tests"]),
            "success_rate": success_count / len(results["tests"]) * 100
        }
        
        return results

async def main():
    """主函数"""
    logger.info("启动智能命题性能测试...")
    
    async with PerformanceTest() as tester:
        results = await tester.run_performance_tests()
        
        # 输出结果
        print("\n" + "="*50)
        print("智能命题性能测试结果")
        print("="*50)
        
        for test in results["tests"]:
            print(f"\n测试类型: {test['test_type']}")
            print(f"执行时间: {test['duration']:.2f}秒")
            print(f"测试结果: {'成功' if test['success'] else '失败'}")
            
            if test['success']:
                if 'question_count' in test:
                    print(f"题目数量: {test['question_count']}")
                    print(f"平均每题时间: {test['avg_time_per_question']:.2f}秒")
                elif 'subquestion_count' in test:
                    print(f"子题数量: {test['subquestion_count']}")
                    print(f"平均每子题时间: {test['avg_time_per_subquestion']:.2f}秒")
            else:
                print(f"错误信息: {test.get('error', '未知错误')}")
        
        print(f"\n总体统计:")
        print(f"总测试数: {results['summary']['total_tests']}")
        print(f"成功测试数: {results['summary']['successful_tests']}")
        print(f"成功率: {results['summary']['success_rate']:.1f}%")
        print(f"总耗时: {results['summary']['total_duration']:.2f}秒")
        print(f"平均耗时: {results['summary']['average_duration']:.2f}秒")
        
        # 保存结果到文件
        with open(f"performance_test_results_{int(time.time())}.json", "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info("性能测试完成，结果已保存到文件")

if __name__ == "__main__":
    asyncio.run(main())
