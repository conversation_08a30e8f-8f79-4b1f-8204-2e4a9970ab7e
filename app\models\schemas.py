"""
数据模型定义
使用Pydantic定义API请求和响应的数据结构
"""

from typing import List, Dict, Any, Optional, Union
from pydantic import BaseModel, Field, validator
from enum import Enum
from datetime import datetime
import base64


class TaskName(str, Enum):
    """任务类型枚举"""
    INTELLIGENT_QUESTION = "智能命题"
    QUESTION_CLONE = "智能克隆"
    MODEL_INTERACTION = "模型交互"


class ElementType(str, Enum):
    """元素类型枚举"""
    LANG_MATERIAL = "langMaterial"


class Element(BaseModel):
    """题目元素"""
    ElementType: ElementType
    ElementText: List[str] = Field(default_factory=list)


class QuesPropSource(BaseModel):
    """题目属性来源"""
    Id: str
    text: str
    remark: str = ""


class QuesProp(BaseModel):
    """题目属性"""
    QuesPropName: str
    QuesPropId: str
    QuesPropSource: List[QuesPropSource]
    QuesPropArr: List[str]
    SelectQuesPropText: str = ""
    NotifyType: str
    ValueObj: Optional[Any] = None
    SelectQuesPropRemark: str = ""


class QuesTypePost(BaseModel):
    """题目类型"""
    QuesPrompt: Dict[str, Any] = Field(default_factory=dict)
    Elements: List[Element] = Field(default_factory=list)
    BaseName: str
    QuesTypeId: str
    QuesTypeName: str
    ChoiceCount: int = 0
    QuesCount: int = 1
    QuesStr: Optional[str] = None
    QuesProps: List[QuesProp] = Field(default_factory=list)
    Childs: List[Any] = Field(default_factory=list)


class QuesPost(BaseModel):
    """试题业务信息"""
    ExamProjectName: str
    ExamSubjectName: str
    AiAssignSupplement: str
    AiQuesTypePost: QuesTypePost


class ModelArgs(BaseModel):
    """模型参数"""
    TopP: str
    TopK: str
    Temperature: str
    MaxToken: str
    ApiKey: str
    ChatUrl: str


class NodeModel(BaseModel):
    """节点模型配置"""
    ModelName: str
    ModelArgs: ModelArgs


class NodePrompt(BaseModel):
    """节点提示词"""
    function: Optional[str] = None
    workflow: Optional[str] = None
    总体任务: Optional[str] = None
    总体要求: Optional[str] = None


class NodeContent(BaseModel):
    """节点内容"""
    NodePrompt: NodePrompt
    NodeModel: Optional[NodeModel] = None


class WorkflowNode(BaseModel):
    """工作流节点"""
    NodeName: str
    NodeType: str
    NodeContent: List[NodeContent]


class WorkflowPost(BaseModel):
    """工作流配置"""
    NodeList: List[WorkflowNode]


class AgentPost(BaseModel):
    """任务角色信息"""
    NodeContent: List[NodeContent]
    ChatContent: str = ""


class AgentGenRequest(BaseModel):
    """AgentGen接口请求模型"""
    QuesPost: Dict
    WorkflowPost: Dict
    AgentPost: Dict
    TaskName: str
    TaskId: str


class StreamRequest(BaseModel):
    """流式查询请求"""
    TaskId: Optional[str] = None
    query_id: Optional[str] = None  # 兼容旧版本参数名


class StreamResponse(BaseModel):
    """流式响应"""
    type: str
    timestamp: float
    message: str
    data: Optional[Dict[str, Any]] = None


class AgentGenResponse(BaseModel):
    """AgentGen接口响应模型"""
    Data: Dict[str, Any] = Field(default_factory=dict)
    code: int = 200
    msg: str = "success"


# 响应状态枚举
class ResponseState(str, Enum):
    SUCCESS = "success"
    PROCESSING = "processing"
    FAILED = "failed"


class TaskStateRequest(BaseModel):
    """任务状态查询请求"""
    TaskId: Optional[str] = None
    query_id: Optional[str] = None  # 兼容旧版本参数名


class TaskStateResponse(BaseModel):
    """任务状态响应"""
    Data: Dict[str, Any] = Field(default_factory=dict)  # 状态信息包装在Data字段中
    code: int = 200
    msg: str = "查询成功"


# ==================== 增强的数据模型 ====================

class WorkflowType(str, Enum):
    """工作流类型枚举"""
    QUICK = "quick"
    TOPIC = "topic"
    MATERIAL = "material"
    KNOWLEDGE = "knowledge"


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class WorkflowStep(str, Enum):
    """工作流步骤枚举"""
    MATERIAL_GENERATION = "material_generation"
    TASK_PLANNING = "task_planning"
    QUESTION_EXECUTION = "question_execution"
    OPTIMIZATION = "optimization"
    PROCESSING = "processing"
    UPLOAD = "upload"
    SEARCH = "search"


class FileType(str, Enum):
    """支持的文件类型枚举"""
    PDF = "pdf"
    DOC = "doc"
    DOCX = "docx"
    TXT = "txt"


class DifficultyLevel(str, Enum):
    """难度等级枚举"""
    EASY = "易"
    MEDIUM = "中"
    HARD = "难"


class StorageStrategy(str, Enum):
    """存储策略枚举"""
    DEFAULT = "default"
    SEMANTIC = "semantic"
    HIERARCHICAL = "hierarchical"


# ==================== 主题命题相关模型 ====================

class TopicMaterialRequest(BaseModel):
    """主题材料生成请求"""
    topic: str = Field(..., description="主题或知识点", min_length=1, max_length=200)
    subject: str = Field(..., description="学科", min_length=1, max_length=50)
    task_id: Optional[str] = Field(None, description="任务ID")
    
    @validator('topic', 'subject')
    def validate_not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('字段不能为空')
        return v.strip()


class TopicMaterialResponse(BaseModel):
    """主题材料生成响应"""
    material: str = Field(..., description="生成的命题材料")
    topic: str = Field(..., description="原始主题")
    subject: str = Field(..., description="学科")
    task_id: str = Field(..., description="任务ID")
    generated_at: datetime = Field(default_factory=datetime.now, description="生成时间")


# ==================== 材料命题相关模型 ====================

class MaterialProcessRequest(BaseModel):
    """材料处理请求"""
    content: Optional[str] = Field(None, description="文本内容", max_length=50000)
    file_data: Optional[str] = Field(None, description="Base64编码的文件数据")
    file_type: Optional[FileType] = Field(None, description="文件类型")
    filename: Optional[str] = Field(None, description="文件名", max_length=255)
    task_id: Optional[str] = Field(None, description="任务ID")
    
    @validator('file_data')
    def validate_base64(cls, v):
        if v:
            try:
                base64.b64decode(v)
            except Exception:
                raise ValueError('无效的Base64编码')
        return v
    
    def __init__(self, **data):
        super().__init__(**data)
        # 确保至少提供content或file_data之一
        if not self.content and not self.file_data:
            raise ValueError('必须提供content或file_data之一')


class MaterialProcessResponse(BaseModel):
    """材料处理响应"""
    extracted_content: str = Field(..., description="提取的内容")
    original_filename: Optional[str] = Field(None, description="原始文件名")
    file_type: Optional[FileType] = Field(None, description="文件类型")
    content_length: int = Field(..., description="内容长度")
    task_id: str = Field(..., description="任务ID")
    processed_at: datetime = Field(default_factory=datetime.now, description="处理时间")


# ==================== 任务规划相关模型 ====================

class StructuralElement(BaseModel):
    """结构化元素"""
    element_type: str = Field(..., description="元素类型")
    element_content: List[str] = Field(default_factory=list, description="元素内容")
    is_required: bool = Field(True, description="是否必需")


class QuestionTaskPlan(BaseModel):
    """命题任务规划"""
    question_type: str = Field(..., description="题目类型", min_length=1)
    quantity: int = Field(..., description="题目数量", ge=1, le=50)
    difficulty: DifficultyLevel = Field(..., description="难度等级")
    structural_elements: Dict[str, Any] = Field(default_factory=dict, description="结构化元素")
    knowledge_points: List[str] = Field(default_factory=list, description="知识点")
    special_requirements: Optional[str] = Field(None, description="特殊要求", max_length=500)
    
    @validator('knowledge_points')
    def validate_knowledge_points(cls, v):
        if len(v) > 10:
            raise ValueError('知识点数量不能超过10个')
        return v


class TaskPlanRequest(BaseModel):
    """任务规划请求"""
    material: str = Field(..., description="命题材料", min_length=1)
    question_tasks: List[QuestionTaskPlan] = Field(..., description="命题任务列表", min_items=1, max_items=10)
    task_id: Optional[str] = Field(None, description="任务ID")
    
    @validator('question_tasks')
    def validate_total_questions(cls, v):
        total = sum(task.quantity for task in v)
        if total > 100:
            raise ValueError('总题目数量不能超过100题')
        return v


class TaskPlanResponse(BaseModel):
    """任务规划响应"""
    task_plan: List[Dict[str, Any]] = Field(..., description="格式化的任务规划")
    total_questions: int = Field(..., description="总题目数量")
    estimated_time: int = Field(..., description="预估完成时间(分钟)")
    task_id: str = Field(..., description="任务ID")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")


# ==================== 试题执行相关模型 ====================

class QuestionExecutionRequest(BaseModel):
    """试题执行请求"""
    task_plan: List[Dict[str, Any]] = Field(..., description="任务规划", min_items=1)
    task_id: Optional[str] = Field(None, description="任务ID")
    stream_response: bool = Field(True, description="是否流式响应")


class GeneratedQuestion(BaseModel):
    """生成的试题"""
    question_id: str = Field(..., description="题目ID")
    question_type: str = Field(..., description="题目类型")
    question_content: str = Field(..., description="题目内容")
    options: Optional[List[str]] = Field(None, description="选项")
    answer: str = Field(..., description="答案")
    explanation: Optional[str] = Field(None, description="解析")
    difficulty: DifficultyLevel = Field(..., description="难度等级")
    knowledge_points: List[str] = Field(default_factory=list, description="知识点")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")


class QuestionExecutionResponse(BaseModel):
    """试题执行响应"""
    questions: List[GeneratedQuestion] = Field(..., description="生成的试题")
    execution_summary: Dict[str, Any] = Field(default_factory=dict, description="执行摘要")
    task_id: str = Field(..., description="任务ID")
    completed_at: datetime = Field(default_factory=datetime.now, description="完成时间")


# ==================== 试题优化相关模型 ====================

class OptimizationRequest(BaseModel):
    """优化建议请求"""
    questions: List[Dict[str, Any]] = Field(..., description="待优化的试题", min_items=1, max_items=50)
    subject: str = Field(..., description="学科", min_length=1)
    knowledge_points: List[str] = Field(default_factory=list, description="知识点")
    optimization_focus: List[str] = Field(default_factory=list, description="优化重点")
    task_id: Optional[str] = Field(None, description="任务ID")


class OptimizationSuggestion(BaseModel):
    """优化建议"""
    question_id: str = Field(..., description="题目ID")
    suggestion_type: str = Field(..., description="建议类型")
    current_issue: str = Field(..., description="当前问题")
    suggested_improvement: str = Field(..., description="改进建议")
    priority: str = Field(..., description="优先级", regex="^(high|medium|low)$")
    example: Optional[str] = Field(None, description="示例")


class OptimizationResponse(BaseModel):
    """优化建议响应"""
    suggestions: List[OptimizationSuggestion] = Field(..., description="优化建议列表")
    overall_assessment: Dict[str, Any] = Field(default_factory=dict, description="整体评估")
    task_id: str = Field(..., description="任务ID")
    analyzed_at: datetime = Field(default_factory=datetime.now, description="分析时间")


# ==================== 知识库管理相关模型 ====================

class KnowledgeUploadRequest(BaseModel):
    """知识库上传请求"""
    segment_length: int = Field(1000, description="分段长度", ge=100, le=5000)
    storage_strategy: StorageStrategy = Field(StorageStrategy.DEFAULT, description="存储策略")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    task_id: Optional[str] = Field(None, description="任务ID")


class KnowledgeDocument(BaseModel):
    """知识库文档模型"""
    id: str = Field(..., description="文档ID")
    filename: str = Field(..., description="文件名")
    file_type: FileType = Field(..., description="文件类型")
    upload_time: datetime = Field(default_factory=datetime.now, description="上传时间")
    content_segments: List[str] = Field(..., description="内容分段")
    segment_count: int = Field(..., description="分段数量")
    total_length: int = Field(..., description="总长度")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    
    @validator('content_segments')
    def validate_segments(cls, v):
        if len(v) > 1000:
            raise ValueError('分段数量不能超过1000')
        return v


class KnowledgeSegment(BaseModel):
    """知识片段模型"""
    id: str = Field(..., description="片段ID")
    document_id: str = Field(..., description="文档ID")
    content: str = Field(..., description="内容")
    segment_index: int = Field(..., description="分段索引", ge=0)
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")


class KnowledgeSearchRequest(BaseModel):
    """知识库搜索请求"""
    query: str = Field(..., description="搜索查询", min_length=1, max_length=200)
    limit: int = Field(10, description="返回数量限制", ge=1, le=100)
    document_ids: Optional[List[str]] = Field(None, description="指定文档ID")
    metadata_filters: Dict[str, Any] = Field(default_factory=dict, description="元数据过滤器")


class KnowledgeSearchResult(BaseModel):
    """知识库搜索结果"""
    segment_id: str = Field(..., description="片段ID")
    document_id: str = Field(..., description="文档ID")
    document_name: str = Field(..., description="文档名称")
    content: str = Field(..., description="内容")
    relevance_score: float = Field(..., description="相关性分数", ge=0.0, le=1.0)
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")


class KnowledgeSearchResponse(BaseModel):
    """知识库搜索响应"""
    results: List[KnowledgeSearchResult] = Field(..., description="搜索结果")
    total_count: int = Field(..., description="总结果数")
    query: str = Field(..., description="原始查询")
    search_time: float = Field(..., description="搜索耗时(秒)")


class KnowledgeUploadResponse(BaseModel):
    """知识库上传响应"""
    uploaded_documents: List[KnowledgeDocument] = Field(..., description="上传的文档")
    total_segments: int = Field(..., description="总分段数")
    upload_summary: Dict[str, Any] = Field(default_factory=dict, description="上传摘要")
    task_id: str = Field(..., description="任务ID")


# ==================== 增强的任务管理模型 ====================

class EnhancedTaskInfo(BaseModel):
    """增强的任务信息模型"""
    task_id: str = Field(..., description="任务ID")
    task_type: WorkflowType = Field(..., description="任务类型")
    status: TaskStatus = Field(..., description="任务状态")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    workflow_step: WorkflowStep = Field(..., description="工作流步骤")
    progress: int = Field(0, description="进度百分比", ge=0, le=100)
    result: Optional[Dict[str, Any]] = Field(None, description="结果数据")
    error_message: Optional[str] = Field(None, description="错误信息")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="任务元数据")
    
    @validator('updated_at', always=True)
    def set_updated_at(cls, v):
        return datetime.now()


class EnhancedTaskStateRequest(BaseModel):
    """增强的任务状态查询请求"""
    task_id: str = Field(..., description="任务ID")
    include_result: bool = Field(True, description="是否包含结果数据")
    include_metadata: bool = Field(False, description="是否包含元数据")


class EnhancedTaskStateResponse(BaseModel):
    """增强的任务状态响应"""
    task_info: EnhancedTaskInfo = Field(..., description="任务信息")
    code: int = Field(200, description="响应代码")
    msg: str = Field("查询成功", description="响应消息")


# ==================== 文件处理相关模型 ====================

class FileValidationResult(BaseModel):
    """文件验证结果"""
    is_valid: bool = Field(..., description="是否有效")
    file_type: Optional[FileType] = Field(None, description="检测到的文件类型")
    file_size: int = Field(..., description="文件大小(字节)")
    validation_errors: List[str] = Field(default_factory=list, description="验证错误")
    warnings: List[str] = Field(default_factory=list, description="警告信息")


class FileProcessingResult(BaseModel):
    """文件处理结果"""
    success: bool = Field(..., description="处理是否成功")
    extracted_content: Optional[str] = Field(None, description="提取的内容")
    content_length: int = Field(0, description="内容长度")
    processing_time: float = Field(..., description="处理耗时(秒)")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="处理元数据")
    errors: List[str] = Field(default_factory=list, description="处理错误")


class ContentSegmentationRequest(BaseModel):
    """内容分段请求"""
    content: str = Field(..., description="待分段内容", min_length=1)
    segment_length: int = Field(1000, description="分段长度", ge=100, le=5000)
    overlap_length: int = Field(100, description="重叠长度", ge=0, le=500)
    preserve_structure: bool = Field(True, description="是否保持结构")
    
    @validator('overlap_length')
    def validate_overlap(cls, v, values):
        if 'segment_length' in values and v >= values['segment_length']:
            raise ValueError('重叠长度必须小于分段长度')
        return v


class ContentSegmentationResponse(BaseModel):
    """内容分段响应"""
    segments: List[str] = Field(..., description="分段结果")
    segment_count: int = Field(..., description="分段数量")
    total_length: int = Field(..., description="总长度")
    average_segment_length: float = Field(..., description="平均分段长度")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="分段元数据")


# ==================== 通用响应模型 ====================

class StandardResponse(BaseModel):
    """标准响应模型"""
    code: int = Field(200, description="响应代码")
    msg: str = Field("success", description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")


class ErrorResponse(BaseModel):
    """错误响应模型"""
    code: int = Field(..., description="错误代码")
    msg: str = Field(..., description="错误消息")
    error_type: str = Field(..., description="错误类型")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")
    timestamp: datetime = Field(default_factory=datetime.now, description="错误时间")
    task_id: Optional[str] = Field(None, description="相关任务ID")


# ==================== 批量操作模型 ====================

class BatchOperationRequest(BaseModel):
    """批量操作请求"""
    operation_type: str = Field(..., description="操作类型")
    items: List[Dict[str, Any]] = Field(..., description="操作项目", min_items=1, max_items=100)
    batch_id: Optional[str] = Field(None, description="批次ID")
    parallel_processing: bool = Field(True, description="是否并行处理")


class BatchOperationResult(BaseModel):
    """批量操作结果"""
    batch_id: str = Field(..., description="批次ID")
    total_items: int = Field(..., description="总项目数")
    successful_items: int = Field(..., description="成功项目数")
    failed_items: int = Field(..., description="失败项目数")
    results: List[Dict[str, Any]] = Field(..., description="详细结果")
    processing_time: float = Field(..., description="处理耗时(秒)")
    errors: List[str] = Field(default_factory=list, description="错误信息")
