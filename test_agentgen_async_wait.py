#!/usr/bin/env python3
"""
测试 AgentGen 异步等待功能
验证接口既能立即创建任务供QueryStream查询，又能等待完成并返回结果，且不阻塞其他请求
"""

import asyncio
import sys
import os
import time
import json
from unittest.mock import AsyncMock, patch

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.api.agent import agent_gen
from app.models.schemas import AgentGenRequest
from app.core.state_manager import task_manager, TaskStatus


async def test_immediate_task_availability():
    """测试任务立即可用性"""
    print("=" * 60)
    print("测试任务立即可用性")
    print("=" * 60)
    
    # 模拟快速LLM调用
    async def mock_fast_llm(*args, **kwargs):
        await asyncio.sleep(0.1)  # 模拟100ms处理时间
        return "模拟生成的试题内容"
    
    test_request = AgentGenRequest(
        QuesPost={
            "ExamSubjectName": "语文",
            "AiQuesTypePost": {
                "QuesTypeName": "单选题",
                "QuesCount": 1
            }
        },
        WorkflowPost={},
        AgentPost={},
        TaskName="智能命题",
        TaskId="test-immediate-availability"
    )
    
    # 使用mock加速测试
    with patch('app.services.intelligent_question_service.IntelligentQuestionService._call_llm_non_stream', side_effect=mock_fast_llm):
        print("1. 开始调用 AgentGen...")
        start_time = time.time()
        
        # 在另一个任务中检查任务是否立即可用
        async def check_task_availability():
            await asyncio.sleep(0.01)  # 等待10ms
            task_info = await task_manager.get_task_info("test-immediate-availability")
            if task_info:
                print(f"   ✅ 任务在10ms内即可查询，状态: {task_info.status.value}")
                return True
            else:
                print("   ❌ 任务在10ms内不可查询")
                return False
        
        # 并发执行AgentGen调用和任务可用性检查
        agent_task = asyncio.create_task(agent_gen(test_request))
        check_task = asyncio.create_task(check_task_availability())
        
        # 等待检查完成
        task_available = await check_task
        
        # 等待AgentGen完成
        response = await agent_task
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"2. AgentGen 完成，总耗时: {duration:.3f}秒")
        print(f"3. 返回结果类型: {type(response.Data)}")
        print(f"4. 响应状态: {response.msg}")
        
        return task_available and response.code == 200


async def test_non_blocking_behavior():
    """测试非阻塞行为"""
    print("\n" + "=" * 60)
    print("测试非阻塞行为")
    print("=" * 60)
    
    # 模拟慢速LLM调用
    async def mock_slow_llm(*args, **kwargs):
        await asyncio.sleep(1.0)  # 模拟1秒处理时间
        return "模拟生成的试题内容"
    
    # 创建多个并发请求
    requests = []
    for i in range(3):
        request = AgentGenRequest(
            QuesPost={
                "ExamSubjectName": "语文",
                "AiQuesTypePost": {
                    "QuesTypeName": "单选题",
                    "QuesCount": 1
                }
            },
            WorkflowPost={},
            AgentPost={},
            TaskName="智能命题",
            TaskId=f"test-non-blocking-{i+1}"
        )
        requests.append(request)
    
    with patch('app.services.intelligent_question_service.IntelligentQuestionService._call_llm_non_stream', side_effect=mock_slow_llm):
        print("1. 启动3个并发AgentGen请求...")
        start_time = time.time()
        
        # 并发执行多个AgentGen调用
        tasks = [agent_gen(req) for req in requests]
        responses = await asyncio.gather(*tasks)
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        print(f"2. 所有请求完成，总耗时: {total_duration:.3f}秒")
        
        # 检查结果
        success_count = sum(1 for resp in responses if resp.code == 200)
        print(f"3. 成功完成: {success_count}/3 个请求")
        
        # 如果是串行处理，应该需要约3秒；如果是并发处理，应该约1秒
        expected_concurrent_time = 1.2  # 允许一些误差
        if total_duration < expected_concurrent_time:
            print("   ✅ 确认为并发处理（非阻塞）")
            return True
        else:
            print(f"   ❌ 可能存在阻塞，耗时过长: {total_duration:.3f}秒")
            return False


async def test_task_lifecycle_with_stream():
    """测试任务生命周期与流式查询的配合"""
    print("\n" + "=" * 60)
    print("测试任务生命周期与流式查询的配合")
    print("=" * 60)
    
    # 模拟分阶段的LLM调用
    call_count = 0
    async def mock_staged_llm(*args, **kwargs):
        nonlocal call_count
        call_count += 1
        await asyncio.sleep(0.2)  # 每次调用200ms
        return f"模拟生成的内容 - 第{call_count}次调用"
    
    test_request = AgentGenRequest(
        QuesPost={
            "ExamSubjectName": "语文",
            "AiQuesTypePost": {
                "QuesTypeName": "单选题",
                "QuesCount": 1
            }
        },
        WorkflowPost={},
        AgentPost={},
        TaskName="智能命题",
        TaskId="test-lifecycle-stream"
    )
    
    with patch('app.services.intelligent_question_service.IntelligentQuestionService._call_llm_non_stream', side_effect=mock_staged_llm):
        print("1. 启动AgentGen请求...")
        
        # 模拟流式查询任务
        async def simulate_stream_query():
            await asyncio.sleep(0.05)  # 等待50ms后开始查询
            
            for i in range(10):  # 查询10次
                task_info = await task_manager.get_task_info("test-lifecycle-stream")
                if task_info:
                    print(f"   流式查询 {i+1}: 状态={task_info.status.value}, 消息={task_info.step_message or '无'}")
                    
                    if task_info.status in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
                        print("   流式查询检测到任务完成")
                        break
                else:
                    print(f"   流式查询 {i+1}: 任务不存在")
                
                await asyncio.sleep(0.1)  # 每100ms查询一次
        
        # 并发执行AgentGen和流式查询
        agent_task = asyncio.create_task(agent_gen(test_request))
        stream_task = asyncio.create_task(simulate_stream_query())
        
        # 等待两个任务完成
        response, _ = await asyncio.gather(agent_task, stream_task)
        
        print(f"2. AgentGen完成，响应码: {response.code}")
        print(f"3. 最终结果包含数据: {'AiQuesTypePost' in response.Data}")
        
        return response.code == 200


async def main():
    """主测试函数"""
    print("AgentGen 异步等待功能测试")
    print("验证：立即创建任务 + 异步等待完成 + 返回结果 + 不阻塞")
    
    # 测试1: 任务立即可用性
    test1_success = await test_immediate_task_availability()
    
    # 测试2: 非阻塞行为
    test2_success = await test_non_blocking_behavior()
    
    # 测试3: 任务生命周期与流式查询配合
    test3_success = await test_task_lifecycle_with_stream()
    
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    print(f"1. 任务立即可用性: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"2. 非阻塞行为: {'✅ 通过' if test2_success else '❌ 失败'}")
    print(f"3. 生命周期配合: {'✅ 通过' if test3_success else '❌ 失败'}")
    
    if test1_success and test2_success and test3_success:
        print("\n🎉 所有测试通过！AgentGen接口完美满足需求：")
        print("   ✅ 立即创建任务，前端可立即调用QueryStream")
        print("   ✅ 异步等待任务完成，返回最终结果")
        print("   ✅ 不阻塞其他请求，支持真正的并发")
    else:
        print("\n❌ 部分测试失败，需要进一步优化")
    
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
