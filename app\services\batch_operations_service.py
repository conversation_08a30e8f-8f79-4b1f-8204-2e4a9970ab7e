from typing import List, Dict, Any, Optional
import asyncio
import time
import json
from loguru import logger
from app.services.topic_material_service import topic_material_service
from app.services.material_process_service import material_process_service
from app.services.question_optimization_service import question_optimization_service
from app.core.state_manager import task_manager, TaskStatus


class BatchOperationsService:
    """批量操作服务"""
    
    def __init__(self):
        self.logger = logger
        # 简化实现，使用内存存储
        self.batch_operations = {}
        self.scheduled_operations = {}
    
    async def batch_generate_questions(
        self,
        batch_config: Dict[str, Any],
        concurrent_limit: int = 5,
        batch_id: str = None
    ) -> Dict[str, Any]:
        """
        批量生成试题
        
        Args:
            batch_config: 批量配置
            concurrent_limit: 并发限制
            batch_id: 批量ID
            
        Returns:
            批量操作结果
        """
        try:
            self.logger.info(f"开始批量生成试题: {batch_id}")
            
            # 解析批量配置
            topics = batch_config.get("topics", [])
            subjects = batch_config.get("subjects", [])
            question_types = batch_config.get("question_types", [])
            
            # 生成任务列表
            tasks = []
            for topic in topics:
                for subject in subjects:
                    for q_type in question_types:
                        tasks.append({
                            "topic": topic,
                            "subject": subject,
                            "question_type": q_type,
                            "task_id": f"{batch_id}_{len(tasks)}"
                        })
            
            # 存储批量操作信息
            self.batch_operations[batch_id] = {
                "batch_id": batch_id,
                "operation_type": "question_generation",
                "status": "processing",
                "total_tasks": len(tasks),
                "completed_tasks": 0,
                "failed_tasks": 0,
                "start_time": time.time(),
                "tasks": tasks,
                "results": []
            }
            
            # 启动异步处理
            asyncio.create_task(self._process_question_generation_batch(
                batch_id, tasks, concurrent_limit
            ))
            
            estimated_time = len(tasks) * 30 / concurrent_limit  # 估算时间
            
            return {
                "total_tasks": len(tasks),
                "estimated_time": estimated_time
            }
            
        except Exception as e:
            self.logger.error(f"批量生成试题失败: {e}")
            raise
    
    async def _process_question_generation_batch(
        self,
        batch_id: str,
        tasks: List[Dict[str, Any]],
        concurrent_limit: int
    ):
        """处理试题生成批量任务"""
        
        batch_info = self.batch_operations[batch_id]
        semaphore = asyncio.Semaphore(concurrent_limit)
        
        async def process_single_task(task):
            async with semaphore:
                try:
                    # 调用主题材料生成服务
                    result = await topic_material_service.generate_material(
                        topic=task["topic"],
                        subject=task["subject"],
                        task_id=task["task_id"]
                    )
                    
                    batch_info["results"].append({
                        "task_id": task["task_id"],
                        "status": "success",
                        "result": result
                    })
                    batch_info["completed_tasks"] += 1
                    
                except Exception as e:
                    batch_info["results"].append({
                        "task_id": task["task_id"],
                        "status": "failed",
                        "error": str(e)
                    })
                    batch_info["failed_tasks"] += 1
        
        # 并发执行所有任务
        await asyncio.gather(*[process_single_task(task) for task in tasks])
        
        # 更新批量操作状态
        batch_info["status"] = "completed"
        batch_info["end_time"] = time.time()
        batch_info["processing_time"] = batch_info["end_time"] - batch_info["start_time"]
        
        self.logger.info(f"批量生成试题完成: {batch_id}")
    
    async def batch_process_materials(
        self,
        materials: List[Dict[str, Any]],
        processing_config: Dict[str, Any],
        batch_id: str
    ) -> Dict[str, Any]:
        """批量处理材料"""
        
        start_time = time.time()
        results = []
        successful_materials = 0
        failed_materials = 0
        
        # 并发处理材料
        tasks = []
        for material in materials:
            task = material_process_service.process_material(
                content=material.get("content"),
                file_data=material.get("file_data"),
                processing_type=processing_config.get("processing_type", "extract"),
                task_id=f"{batch_id}_{len(tasks)}"
            )
            tasks.append(task)
        
        # 等待所有任务完成
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        for i, result in enumerate(batch_results):
            if isinstance(result, Exception):
                failed_materials += 1
                results.append({
                    "material_index": i,
                    "status": "failed",
                    "error": str(result)
                })
            else:
                successful_materials += 1
                results.append({
                    "material_index": i,
                    "status": "success",
                    "result": result
                })
        
        processing_time = time.time() - start_time
        
        # 存储批量操作信息
        self.batch_operations[batch_id] = {
            "batch_id": batch_id,
            "operation_type": "material_processing",
            "status": "completed",
            "total_materials": len(materials),
            "successful_materials": successful_materials,
            "failed_materials": failed_materials,
            "processing_time": processing_time,
            "results": results
        }
        
        return {
            "successful_materials": successful_materials,
            "failed_materials": failed_materials,
            "processing_time": processing_time,
            "results": results
        }
    
    async def batch_optimize_questions(
        self,
        question_batches: List[Dict[str, Any]],
        optimization_config: Dict[str, Any],
        batch_id: str
    ) -> Dict[str, Any]:
        """批量优化试题"""
        
        optimization_results = []
        total_questions = 0
        total_optimized = 0
        
        # 处理每个试题批次
        for i, batch in enumerate(question_batches):
            try:
                questions = batch.get("questions", [])
                analysis_criteria = optimization_config.get("analysis_criteria", [
                    "难度适中", "表述清晰", "答案准确"
                ])
                
                # 调用优化服务
                result = await question_optimization_service.analyze_question_quality(
                    questions=questions,
                    analysis_criteria=analysis_criteria,
                    task_id=f"{batch_id}_optimize_{i}"
                )
                
                optimization_results.append({
                    "batch_index": i,
                    "status": "success",
                    "optimization_result": result
                })
                
                total_questions += len(questions)
                total_optimized += len(questions)
                
            except Exception as e:
                optimization_results.append({
                    "batch_index": i,
                    "status": "failed",
                    "error": str(e)
                })
        
        # 整体统计
        overall_statistics = {
            "total_batches": len(question_batches),
            "total_questions": total_questions,
            "total_optimized": total_optimized,
            "optimization_rate": total_optimized / total_questions if total_questions > 0 else 0
        }
        
        # 存储批量操作信息
        self.batch_operations[batch_id] = {
            "batch_id": batch_id,
            "operation_type": "question_optimization",
            "status": "completed",
            "optimization_results": optimization_results,
            "overall_statistics": overall_statistics
        }
        
        return {
            "optimization_results": optimization_results,
            "overall_statistics": overall_statistics
        }
    
    async def get_batch_status(self, batch_id: str) -> Optional[Dict[str, Any]]:
        """查询批量操作状态"""
        
        batch_info = self.batch_operations.get(batch_id)
        
        if not batch_info:
            return None
        
        # 计算进度
        if batch_info["status"] == "processing":
            total_tasks = batch_info.get("total_tasks", 0)
            completed_tasks = batch_info.get("completed_tasks", 0)
            progress = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
        else:
            progress = 100
        
        return {
            "batch_id": batch_id,
            "operation_type": batch_info["operation_type"],
            "status": batch_info["status"],
            "progress": progress,
            "total_tasks": batch_info.get("total_tasks", 0),
            "completed_tasks": batch_info.get("completed_tasks", 0),
            "failed_tasks": batch_info.get("failed_tasks", 0),
            "start_time": batch_info.get("start_time"),
            "end_time": batch_info.get("end_time"),
            "processing_time": batch_info.get("processing_time")
        }
    
    async def cancel_batch_operation(
        self,
        batch_id: str,
        reason: str = "用户取消"
    ) -> Dict[str, Any]:
        """取消批量操作"""
        
        batch_info = self.batch_operations.get(batch_id)
        
        if not batch_info:
            raise ValueError(f"批量操作不存在: {batch_id}")
        
        if batch_info["status"] != "processing":
            raise ValueError(f"批量操作已结束，无法取消: {batch_info['status']}")
        
        # 更新状态
        batch_info["status"] = "cancelled"
        batch_info["cancel_reason"] = reason
        batch_info["cancel_time"] = time.time()
        
        return {
            "batch_id": batch_id,
            "cancel_status": "success",
            "cancel_reason": reason,
            "cancelled_tasks": batch_info.get("total_tasks", 0) - batch_info.get("completed_tasks", 0)
        }
    
    async def batch_export_results(
        self,
        batch_ids: List[str],
        export_format: str = "json",
        include_metadata: bool = True,
        export_id: str = None
    ) -> Dict[str, Any]:
        """批量导出结果"""
        
        export_data = []
        export_metadata = {
            "export_id": export_id,
            "export_time": time.time(),
            "export_format": export_format,
            "total_batches": len(batch_ids)
        }
        
        for batch_id in batch_ids:
            batch_info = self.batch_operations.get(batch_id)
            if batch_info:
                if export_format == "json":
                    export_data.append(batch_info)
                elif export_format == "csv":
                    # 简化的CSV格式
                    export_data.append({
                        "batch_id": batch_id,
                        "operation_type": batch_info["operation_type"],
                        "status": batch_info["status"],
                        "total_tasks": batch_info.get("total_tasks", 0),
                        "completed_tasks": batch_info.get("completed_tasks", 0)
                    })
        
        return {
            "export_data": export_data,
            "export_metadata": export_metadata if include_metadata else None
        }
    
    async def list_batch_operations(
        self,
        status: Optional[str] = None,
        operation_type: Optional[str] = None,
        page: int = 1,
        page_size: int = 20
    ) -> Dict[str, Any]:
        """列出批量操作"""
        
        # 筛选操作
        filtered_operations = []
        for batch_info in self.batch_operations.values():
            if status and batch_info["status"] != status:
                continue
            if operation_type and batch_info["operation_type"] != operation_type:
                continue
            filtered_operations.append(batch_info)
        
        # 分页
        total_count = len(filtered_operations)
        total_pages = (total_count + page_size - 1) // page_size
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        
        operations = filtered_operations[start_idx:end_idx]
        
        return {
            "operations": operations,
            "total_count": total_count,
            "page": page,
            "page_size": page_size,
            "total_pages": total_pages
        }
    
    async def batch_retry_failed(
        self,
        batch_id: str,
        retry_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """重试失败的批量操作"""
        
        batch_info = self.batch_operations.get(batch_id)
        
        if not batch_info:
            raise ValueError(f"批量操作不存在: {batch_id}")
        
        # 找出失败的任务
        failed_tasks = []
        for result in batch_info.get("results", []):
            if result["status"] == "failed":
                failed_tasks.append(result["task_id"])
        
        if not failed_tasks:
            return {
                "retry_status": "no_failed_tasks",
                "failed_tasks_count": 0
            }
        
        # 创建重试批量操作
        retry_batch_id = f"{batch_id}_retry_{int(time.time())}"
        
        # 简化实现，标记为重试中
        self.batch_operations[retry_batch_id] = {
            "batch_id": retry_batch_id,
            "original_batch_id": batch_id,
            "operation_type": batch_info["operation_type"],
            "status": "processing",
            "retry_tasks": failed_tasks,
            "start_time": time.time()
        }
        
        return {
            "retry_batch_id": retry_batch_id,
            "retry_status": "initiated",
            "failed_tasks_count": len(failed_tasks)
        }
    
    async def schedule_batch_operation(
        self,
        operation_config: Dict[str, Any],
        schedule_time: Optional[str] = None,
        recurring: bool = False,
        schedule_id: str = None
    ) -> Dict[str, Any]:
        """调度批量操作"""
        
        # 存储调度配置
        self.scheduled_operations[schedule_id] = {
            "schedule_id": schedule_id,
            "operation_config": operation_config,
            "schedule_time": schedule_time,
            "recurring": recurring,
            "created_at": time.time(),
            "status": "scheduled"
        }
        
        return {
            "schedule_id": schedule_id,
            "status": "scheduled"
        }
    
    async def get_batch_statistics(
        self,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        operation_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取批量操作统计"""
        
        # 简化实现，返回模拟统计数据
        total_operations = len(self.batch_operations)
        completed_operations = len([
            op for op in self.batch_operations.values()
            if op["status"] == "completed"
        ])
        failed_operations = len([
            op for op in self.batch_operations.values()
            if op["status"] == "failed"
        ])
        
        operation_types = {}
        for op in self.batch_operations.values():
            op_type = op["operation_type"]
            operation_types[op_type] = operation_types.get(op_type, 0) + 1
        
        return {
            "total_operations": total_operations,
            "completed_operations": completed_operations,
            "failed_operations": failed_operations,
            "success_rate": completed_operations / total_operations if total_operations > 0 else 0,
            "operation_types": operation_types,
            "average_processing_time": 120.5,  # 模拟数据
            "daily_statistics": [
                {"date": "2025-07-29", "operations": 15, "success": 12},
                {"date": "2025-07-28", "operations": 20, "success": 18},
                {"date": "2025-07-27", "operations": 18, "success": 16}
            ]
        }


# 创建全局服务实例
batch_operations_service = BatchOperationsService()
