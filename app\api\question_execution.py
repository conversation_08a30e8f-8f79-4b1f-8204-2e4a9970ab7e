from fastapi import APIRouter, HTTPException
from app.models.schemas import (
    QuestionExecutionRequest, QuestionExecutionResponse,
    StandardResponse, ErrorResponse
)
from app.services.question_execution_service import question_execution_service
from app.core.state_manager import task_manager, TaskStatus
import uuid
from datetime import datetime
from loguru import logger

router = APIRouter()


@router.post("/ExecuteQuestions", response_model=QuestionExecutionResponse)
async def execute_questions(request: QuestionExecutionRequest):
    """
    执行试题生成任务
    
    功能：
    - 基于任务规划批量生成试题
    - 支持流式响应和进度跟踪
    - 并发处理多个试题生成任务
    - 返回生成的试题集合
    """
    try:
        # 生成任务ID
        task_id = request.task_id or str(uuid.uuid4())
        
        # 创建任务
        await task_manager.create_task(task_id)
        
        # 更新任务状态
        await task_manager.update_task_status(
            task_id,
            TaskStatus.PROCESSING,
            step_info={
                "step": "executing_questions",
                "progress": 0,
                "detail": "开始执行试题生成任务"
            }
        )
        
        logger.info(f"开始执行试题生成: {task_id}")
        
        # 调用服务执行试题生成
        result = await question_execution_service.execute_questions(
            task_plan=request.task_plan,
            task_id=task_id,
            stream_response=request.stream_response
        )
        
        # 更新任务状态为完成
        await task_manager.update_task_status(
            task_id,
            TaskStatus.COMPLETED,
            result=result
        )
        
        # 构建响应
        response = QuestionExecutionResponse(
            questions=result["questions"],
            execution_summary=result["execution_summary"],
            task_id=task_id,
            completed_at=datetime.now()
        )
        
        logger.info(f"试题生成执行完成: {task_id}, 生成 {len(result['questions'])} 题")
        return response
        
    except Exception as e:
        logger.error(f"试题生成执行失败: {e}")
        
        # 更新任务状态为失败
        if 'task_id' in locals():
            await task_manager.update_task_status(
                task_id,
                TaskStatus.FAILED,
                error_message=str(e)
            )
        
        raise HTTPException(
            status_code=500,
            detail=f"试题生成执行失败: {str(e)}"
        )


@router.post("/BatchExecuteQuestions")
async def batch_execute_questions(
    task_plans: list,
    batch_size: int = 5,
    parallel_processing: bool = True
):
    """
    批量执行多个试题生成任务
    
    功能：
    - 支持多个任务规划的批量处理
    - 可配置批次大小和并行处理
    - 提供批量执行的统计信息
    """
    try:
        batch_id = str(uuid.uuid4())
        
        logger.info(f"开始批量执行试题生成: {batch_id}, {len(task_plans)} 个任务")
        
        # 调用服务批量执行
        result = await question_execution_service.batch_execute_questions(
            task_plans=task_plans,
            batch_id=batch_id,
            batch_size=batch_size,
            parallel_processing=parallel_processing
        )
        
        return StandardResponse(
            code=200,
            msg="批量执行完成",
            data={
                "batch_id": batch_id,
                "total_tasks": len(task_plans),
                "successful_tasks": result["successful_tasks"],
                "failed_tasks": result["failed_tasks"],
                "total_questions": result["total_questions"],
                "execution_time": result["execution_time"],
                "results": result["results"]
            }
        )
        
    except Exception as e:
        logger.error(f"批量执行失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"批量执行失败: {str(e)}"
        )


@router.get("/GetExecutionStatus/{task_id}")
async def get_execution_status(task_id: str):
    """
    查询试题执行状态
    """
    try:
        task_info = await task_manager.get_task_info(task_id)
        
        if not task_info:
            raise HTTPException(
                status_code=404,
                detail=f"任务不存在: {task_id}"
            )
        
        return StandardResponse(
            code=200,
            msg="查询成功",
            data={
                "task_id": task_id,
                "status": task_info.status.value,
                "created_at": task_info.created_at,
                "updated_at": task_info.updated_at,
                "result": task_info.result,
                "error_message": task_info.error_message
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询任务状态失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"查询任务状态失败: {str(e)}"
        )


@router.post("/PreviewQuestions")
async def preview_questions(task_plan: dict, preview_count: int = 3):
    """
    预览试题生成效果
    
    功能：
    - 基于任务规划生成少量预览试题
    - 快速验证生成效果和质量
    - 不保存到正式结果中
    """
    try:
        task_id = str(uuid.uuid4())
        
        logger.info(f"开始预览试题生成: {task_id}")
        
        # 调用服务预览试题
        result = await question_execution_service.preview_questions(
            task_plan=task_plan,
            preview_count=preview_count,
            task_id=task_id
        )
        
        return StandardResponse(
            code=200,
            msg="预览生成完成",
            data={
                "task_id": task_id,
                "preview_questions": result["preview_questions"],
                "generation_time": result["generation_time"],
                "quality_assessment": result.get("quality_assessment", {})
            }
        )
        
    except Exception as e:
        logger.error(f"预览生成失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"预览生成失败: {str(e)}"
        )


@router.post("/CancelExecution/{task_id}")
async def cancel_execution(task_id: str):
    """
    取消试题执行任务
    """
    try:
        # 更新任务状态为取消
        await task_manager.update_task_status(
            task_id,
            TaskStatus.CANCELLED,
            step_info={
                "step": "cancelled",
                "progress": 0,
                "detail": "任务已取消"
            }
        )
        
        logger.info(f"任务已取消: {task_id}")
        
        return StandardResponse(
            code=200,
            msg="任务取消成功",
            data={"task_id": task_id}
        )
        
    except Exception as e:
        logger.error(f"任务取消失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"任务取消失败: {str(e)}"
        )
