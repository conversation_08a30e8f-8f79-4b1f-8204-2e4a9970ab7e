from fastapi import APIRouter, HTTPException, UploadFile, File
from app.models.schemas import StandardResponse, ErrorResponse
from app.services.file_processing_service import file_processing_service
from app.core.state_manager import task_manager, TaskStatus
import uuid
from datetime import datetime
from loguru import logger

router = APIRouter()


@router.post("/ValidateFile")
async def validate_file(
    file_data: str,
    filename: str,
    max_size_mb: int = 50
):
    """
    验证文件
    
    功能：
    - 检查文件格式和大小
    - 验证文件完整性
    - 检测恶意文件
    - 返回验证结果
    """
    try:
        task_id = str(uuid.uuid4())
        
        logger.info(f"开始验证文件: {filename}")
        
        # 调用服务验证文件
        result = await file_processing_service.validate_file(
            file_data=file_data,
            filename=filename,
            max_size_mb=max_size_mb,
            task_id=task_id
        )
        
        return StandardResponse(
            code=200,
            msg="文件验证完成",
            data={
                "task_id": task_id,
                "filename": filename,
                "is_valid": result["is_valid"],
                "file_info": result["file_info"],
                "validation_issues": result.get("validation_issues", [])
            }
        )
        
    except Exception as e:
        logger.error(f"文件验证失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"文件验证失败: {str(e)}"
        )


@router.post("/ProcessFile")
async def process_file(
    file_data: str,
    filename: str,
    processing_options: dict = None
):
    """
    处理文件
    
    功能：
    - 提取文件内容
    - 转换文件格式
    - 优化文件结构
    - 生成处理报告
    """
    try:
        task_id = str(uuid.uuid4())
        
        # 创建任务
        await task_manager.create_task(task_id)
        
        logger.info(f"开始处理文件: {filename}")
        
        # 调用服务处理文件
        result = await file_processing_service.process_file(
            file_data=file_data,
            filename=filename,
            processing_options=processing_options or {},
            task_id=task_id
        )
        
        # 更新任务状态为完成
        await task_manager.update_task_status(
            task_id,
            TaskStatus.COMPLETED,
            result=result
        )
        
        return StandardResponse(
            code=200,
            msg="文件处理完成",
            data={
                "task_id": task_id,
                "filename": filename,
                "processed_content": result["processed_content"],
                "processing_report": result["processing_report"]
            }
        )
        
    except Exception as e:
        logger.error(f"文件处理失败: {e}")
        
        # 更新任务状态为失败
        if 'task_id' in locals():
            await task_manager.update_task_status(
                task_id,
                TaskStatus.FAILED,
                error_message=str(e)
            )
        
        raise HTTPException(
            status_code=500,
            detail=f"文件处理失败: {str(e)}"
        )


@router.post("/SegmentContent")
async def segment_content(
    content: str,
    segment_type: str = "paragraph",
    max_segment_length: int = 1000
):
    """
    分段内容
    
    功能：
    - 智能分段文本内容
    - 支持多种分段策略
    - 保持语义完整性
    - 返回分段结果
    """
    try:
        task_id = str(uuid.uuid4())
        
        logger.info(f"开始分段内容: {segment_type}")
        
        # 调用服务分段内容
        result = await file_processing_service.segment_content(
            content=content,
            segment_type=segment_type,
            max_segment_length=max_segment_length,
            task_id=task_id
        )
        
        return StandardResponse(
            code=200,
            msg="内容分段完成",
            data={
                "task_id": task_id,
                "segments": result["segments"],
                "segment_count": result["segment_count"],
                "segmentation_strategy": result["segmentation_strategy"]
            }
        )
        
    except Exception as e:
        logger.error(f"内容分段失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"内容分段失败: {str(e)}"
        )


@router.post("/ExtractMetadata")
async def extract_metadata(
    file_data: str,
    filename: str
):
    """
    提取文件元数据
    
    功能：
    - 提取文件基本信息
    - 分析文件结构
    - 识别内容特征
    - 生成元数据报告
    """
    try:
        task_id = str(uuid.uuid4())
        
        logger.info(f"开始提取元数据: {filename}")
        
        # 调用服务提取元数据
        result = await file_processing_service.extract_metadata(
            file_data=file_data,
            filename=filename,
            task_id=task_id
        )
        
        return StandardResponse(
            code=200,
            msg="元数据提取完成",
            data={
                "task_id": task_id,
                "filename": filename,
                "metadata": result["metadata"],
                "content_analysis": result.get("content_analysis", {})
            }
        )
        
    except Exception as e:
        logger.error(f"元数据提取失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"元数据提取失败: {str(e)}"
        )


@router.post("/ConvertFile")
async def convert_file(
    file_data: str,
    filename: str,
    target_format: str
):
    """
    转换文件格式
    
    功能：
    - 支持多种格式转换
    - 保持内容完整性
    - 优化输出质量
    - 提供转换报告
    """
    try:
        task_id = str(uuid.uuid4())
        
        logger.info(f"开始转换文件: {filename} -> {target_format}")
        
        # 调用服务转换文件
        result = await file_processing_service.convert_file(
            file_data=file_data,
            filename=filename,
            target_format=target_format,
            task_id=task_id
        )
        
        return StandardResponse(
            code=200,
            msg="文件转换完成",
            data={
                "task_id": task_id,
                "original_filename": filename,
                "converted_filename": result["converted_filename"],
                "converted_data": result["converted_data"],
                "conversion_report": result.get("conversion_report", {})
            }
        )
        
    except Exception as e:
        logger.error(f"文件转换失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"文件转换失败: {str(e)}"
        )


@router.post("/BatchProcessFiles")
async def batch_process_files(
    files: list,
    processing_options: dict = None
):
    """
    批量处理文件
    
    功能：
    - 并发处理多个文件
    - 统一处理配置
    - 提供批量处理统计
    - 支持错误恢复
    """
    try:
        batch_id = str(uuid.uuid4())
        
        logger.info(f"开始批量处理文件: {batch_id}, {len(files)} 个文件")
        
        # 调用服务批量处理
        result = await file_processing_service.batch_process_files(
            files=files,
            processing_options=processing_options or {},
            batch_id=batch_id
        )
        
        return StandardResponse(
            code=200,
            msg="批量处理完成",
            data={
                "batch_id": batch_id,
                "total_files": len(files),
                "successful_files": result["successful_files"],
                "failed_files": result["failed_files"],
                "processing_time": result["processing_time"],
                "results": result["results"]
            }
        )
        
    except Exception as e:
        logger.error(f"批量处理失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"批量处理失败: {str(e)}"
        )


@router.get("/GetProcessingStatus/{task_id}")
async def get_processing_status(task_id: str):
    """
    查询处理状态
    """
    try:
        task_info = await task_manager.get_task_info(task_id)
        
        if not task_info:
            raise HTTPException(
                status_code=404,
                detail=f"任务不存在: {task_id}"
            )
        
        return StandardResponse(
            code=200,
            msg="查询成功",
            data={
                "task_id": task_id,
                "status": task_info.status.value,
                "created_at": task_info.created_at,
                "updated_at": task_info.updated_at,
                "result": task_info.result,
                "error_message": task_info.error_message
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询处理状态失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"查询处理状态失败: {str(e)}"
        )
