"""
数据模型定义
使用Pydantic定义API请求和响应的数据结构
"""

from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from enum import Enum


class TaskName(str, Enum):
    """任务类型枚举"""
    INTELLIGENT_QUESTION = "智能命题"
    QUESTION_CLONE = "智能克隆"
    MODEL_INTERACTION = "模型交互"


class ElementType(str, Enum):
    """元素类型枚举"""
    LANG_MATERIAL = "langMaterial"


class Element(BaseModel):
    """题目元素"""
    ElementType: ElementType
    ElementText: List[str] = Field(default_factory=list)


class QuesPropSource(BaseModel):
    """题目属性来源"""
    Id: str
    text: str
    remark: str = ""


class QuesProp(BaseModel):
    """题目属性"""
    QuesPropName: str
    QuesPropId: str
    QuesPropSource: List[QuesPropSource]
    QuesPropArr: List[str]
    SelectQuesPropText: str = ""
    NotifyType: str
    ValueObj: Optional[Any] = None
    SelectQuesPropRemark: str = ""


class QuesTypePost(BaseModel):
    """题目类型"""
    QuesPrompt: Dict[str, Any] = Field(default_factory=dict)
    Elements: List[Element] = Field(default_factory=list)
    BaseName: str
    QuesTypeId: str
    QuesTypeName: str
    ChoiceCount: int = 0
    QuesCount: int = 1
    QuesStr: Optional[str] = None
    QuesProps: List[QuesProp] = Field(default_factory=list)
    Childs: List[Any] = Field(default_factory=list)


class QuesPost(BaseModel):
    """试题业务信息"""
    ExamProjectName: str
    ExamSubjectName: str
    AiAssignSupplement: str
    AiQuesTypePost: QuesTypePost


class ModelArgs(BaseModel):
    """模型参数"""
    TopP: str
    TopK: str
    Temperature: str
    MaxToken: str
    ApiKey: str
    ChatUrl: str


class NodeModel(BaseModel):
    """节点模型配置"""
    ModelName: str
    ModelArgs: ModelArgs


class NodePrompt(BaseModel):
    """节点提示词"""
    function: Optional[str] = None
    workflow: Optional[str] = None
    总体任务: Optional[str] = None
    总体要求: Optional[str] = None


class NodeContent(BaseModel):
    """节点内容"""
    NodePrompt: NodePrompt
    NodeModel: Optional[NodeModel] = None


class WorkflowNode(BaseModel):
    """工作流节点"""
    NodeName: str
    NodeType: str
    NodeContent: List[NodeContent]


class WorkflowPost(BaseModel):
    """工作流配置"""
    NodeList: List[WorkflowNode]


class AgentPost(BaseModel):
    """任务角色信息"""
    NodeContent: List[NodeContent]
    ChatContent: str = ""


class AgentGenRequest(BaseModel):
    """AgentGen接口请求模型"""
    QuesPost: Dict
    WorkflowPost: Dict
    AgentPost: Dict
    TaskName: str
    TaskId: str


class StreamRequest(BaseModel):
    """流式查询请求"""
    TaskId: Optional[str] = None
    query_id: Optional[str] = None  # 兼容旧版本参数名


class StreamResponse(BaseModel):
    """流式响应"""
    type: str
    timestamp: float
    message: str
    data: Optional[Dict[str, Any]] = None


class AgentGenResponse(BaseModel):
    """AgentGen接口响应模型"""
    Data: Dict[str, Any] = Field(default_factory=dict)
    code: int = 200
    msg: str = "success"


# 响应状态枚举
class ResponseState(str, Enum):
    SUCCESS = "success"
    PROCESSING = "processing"
    FAILED = "failed"


class TaskStateRequest(BaseModel):
    """任务状态查询请求"""
    TaskId: Optional[str] = None
    query_id: Optional[str] = None  # 兼容旧版本参数名


class TaskStateResponse(BaseModel):
    """任务状态响应"""
    Data: Dict[str, Any] = Field(default_factory=dict)  # 状态信息包装在Data字段中
    code: int = 200
    msg: str = "查询成功"
