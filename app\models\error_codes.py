"""
错误代码定义
为增强的问题生成工作流定义标准化错误代码
"""

from enum import IntEnum


class ErrorCodes(IntEnum):
    """标准化错误代码"""
    
    # 通用错误 (4000-4099)
    INVALID_REQUEST = 4000
    MISSING_REQUIRED_FIELD = 4001
    INVALID_FIELD_VALUE = 4002
    REQUEST_VALIDATION_FAILED = 4003
    UNSUPPORTED_OPERATION = 4004
    RATE_LIMIT_EXCEEDED = 4005
    
    # 主题生成错误 (4100-4199)
    TOPIC_MATERIAL_GENERATION_FAILED = 4101
    TOPIC_TASK_PLAN_CREATION_FAILED = 4102
    TOPIC_QUESTION_EXECUTION_FAILED = 4103
    TOPIC_OPTIMIZATION_FAILED = 4104
    INVALID_TOPIC_FORMAT = 4105
    TOPIC_TOO_BROAD = 4106
    TOPIC_TOO_NARROW = 4107
    SUBJECT_NOT_SUPPORTED = 4108
    
    # 材料处理错误 (4200-4299)
    MATERIAL_PROCESSING_FAILED = 4201
    UNSUPPORTED_FILE_FORMAT = 4202
    FILE_PARSING_ERROR = 4203
    MATERIAL_EXTRACTION_FAILED = 4204
    FILE_TOO_LARGE = 4205
    FILE_CORRUPTED = 4206
    ENCODING_DETECTION_FAILED = 4207
    CONTENT_TOO_SHORT = 4208
    CONTENT_TOO_LONG = 4209
    INVALID_BASE64_DATA = 4210
    
    # 知识库操作错误 (4300-4399)
    KNOWLEDGE_UPLOAD_FAILED = 4301
    KNOWLEDGE_SEARCH_FAILED = 4302
    DOCUMENT_NOT_FOUND = 4303
    DOCUMENT_DELETE_FAILED = 4304
    KNOWLEDGE_STORAGE_ERROR = 4305
    SEGMENTATION_FAILED = 4306
    INDEX_CREATION_FAILED = 4307
    SEARCH_INDEX_ERROR = 4308
    DOCUMENT_ALREADY_EXISTS = 4309
    STORAGE_QUOTA_EXCEEDED = 4310
    
    # 任务管理错误 (4400-4499)
    TASK_NOT_FOUND = 4401
    TASK_ALREADY_COMPLETED = 4402
    TASK_CANCELLED = 4403
    TASK_TIMEOUT = 4404
    INVALID_TASK_STATE = 4405
    TASK_DEPENDENCY_FAILED = 4406
    CONCURRENT_TASK_LIMIT_EXCEEDED = 4407
    TASK_QUEUE_FULL = 4408
    
    # 试题生成错误 (4500-4599)
    QUESTION_GENERATION_FAILED = 4501
    INVALID_QUESTION_TYPE = 4502
    QUESTION_QUANTITY_EXCEEDED = 4503
    DIFFICULTY_LEVEL_INVALID = 4504
    STRUCTURAL_ELEMENTS_INVALID = 4505
    KNOWLEDGE_POINTS_INVALID = 4506
    OPTIMIZATION_ANALYSIS_FAILED = 4507
    QUESTION_VALIDATION_FAILED = 4508
    
    # 系统错误 (5000-5099)
    INTERNAL_SERVER_ERROR = 5000
    LLM_SERVICE_UNAVAILABLE = 5001
    DATABASE_CONNECTION_ERROR = 5002
    STORAGE_SERVICE_ERROR = 5003
    CACHE_SERVICE_ERROR = 5004
    EXTERNAL_API_ERROR = 5005
    CONFIGURATION_ERROR = 5006
    RESOURCE_EXHAUSTED = 5007
    SERVICE_TEMPORARILY_UNAVAILABLE = 5008
    
    # 流处理错误 (5100-5199)
    STREAM_INITIALIZATION_FAILED = 5101
    STREAM_CONNECTION_LOST = 5102
    STREAM_DATA_CORRUPTION = 5103
    STREAM_BUFFER_OVERFLOW = 5104
    STREAM_TIMEOUT = 5105


class ErrorMessages:
    """错误消息映射"""
    
    MESSAGES = {
        # 通用错误消息
        ErrorCodes.INVALID_REQUEST: "请求格式无效",
        ErrorCodes.MISSING_REQUIRED_FIELD: "缺少必需字段",
        ErrorCodes.INVALID_FIELD_VALUE: "字段值无效",
        ErrorCodes.REQUEST_VALIDATION_FAILED: "请求验证失败",
        ErrorCodes.UNSUPPORTED_OPERATION: "不支持的操作",
        ErrorCodes.RATE_LIMIT_EXCEEDED: "请求频率超限",
        
        # 主题生成错误消息
        ErrorCodes.TOPIC_MATERIAL_GENERATION_FAILED: "主题材料生成失败",
        ErrorCodes.TOPIC_TASK_PLAN_CREATION_FAILED: "主题任务规划创建失败",
        ErrorCodes.TOPIC_QUESTION_EXECUTION_FAILED: "主题试题执行失败",
        ErrorCodes.TOPIC_OPTIMIZATION_FAILED: "主题试题优化失败",
        ErrorCodes.INVALID_TOPIC_FORMAT: "主题格式无效",
        ErrorCodes.TOPIC_TOO_BROAD: "主题范围过于宽泛",
        ErrorCodes.TOPIC_TOO_NARROW: "主题范围过于狭窄",
        ErrorCodes.SUBJECT_NOT_SUPPORTED: "不支持的学科",
        
        # 材料处理错误消息
        ErrorCodes.MATERIAL_PROCESSING_FAILED: "材料处理失败",
        ErrorCodes.UNSUPPORTED_FILE_FORMAT: "不支持的文件格式",
        ErrorCodes.FILE_PARSING_ERROR: "文件解析错误",
        ErrorCodes.MATERIAL_EXTRACTION_FAILED: "材料提取失败",
        ErrorCodes.FILE_TOO_LARGE: "文件过大",
        ErrorCodes.FILE_CORRUPTED: "文件已损坏",
        ErrorCodes.ENCODING_DETECTION_FAILED: "编码检测失败",
        ErrorCodes.CONTENT_TOO_SHORT: "内容过短",
        ErrorCodes.CONTENT_TOO_LONG: "内容过长",
        ErrorCodes.INVALID_BASE64_DATA: "无效的Base64数据",
        
        # 知识库操作错误消息
        ErrorCodes.KNOWLEDGE_UPLOAD_FAILED: "知识库上传失败",
        ErrorCodes.KNOWLEDGE_SEARCH_FAILED: "知识库搜索失败",
        ErrorCodes.DOCUMENT_NOT_FOUND: "文档未找到",
        ErrorCodes.DOCUMENT_DELETE_FAILED: "文档删除失败",
        ErrorCodes.KNOWLEDGE_STORAGE_ERROR: "知识库存储错误",
        ErrorCodes.SEGMENTATION_FAILED: "内容分段失败",
        ErrorCodes.INDEX_CREATION_FAILED: "索引创建失败",
        ErrorCodes.SEARCH_INDEX_ERROR: "搜索索引错误",
        ErrorCodes.DOCUMENT_ALREADY_EXISTS: "文档已存在",
        ErrorCodes.STORAGE_QUOTA_EXCEEDED: "存储配额超限",
        
        # 任务管理错误消息
        ErrorCodes.TASK_NOT_FOUND: "任务未找到",
        ErrorCodes.TASK_ALREADY_COMPLETED: "任务已完成",
        ErrorCodes.TASK_CANCELLED: "任务已取消",
        ErrorCodes.TASK_TIMEOUT: "任务超时",
        ErrorCodes.INVALID_TASK_STATE: "无效的任务状态",
        ErrorCodes.TASK_DEPENDENCY_FAILED: "任务依赖失败",
        ErrorCodes.CONCURRENT_TASK_LIMIT_EXCEEDED: "并发任务数量超限",
        ErrorCodes.TASK_QUEUE_FULL: "任务队列已满",
        
        # 试题生成错误消息
        ErrorCodes.QUESTION_GENERATION_FAILED: "试题生成失败",
        ErrorCodes.INVALID_QUESTION_TYPE: "无效的试题类型",
        ErrorCodes.QUESTION_QUANTITY_EXCEEDED: "试题数量超限",
        ErrorCodes.DIFFICULTY_LEVEL_INVALID: "无效的难度等级",
        ErrorCodes.STRUCTURAL_ELEMENTS_INVALID: "无效的结构化元素",
        ErrorCodes.KNOWLEDGE_POINTS_INVALID: "无效的知识点",
        ErrorCodes.OPTIMIZATION_ANALYSIS_FAILED: "优化分析失败",
        ErrorCodes.QUESTION_VALIDATION_FAILED: "试题验证失败",
        
        # 系统错误消息
        ErrorCodes.INTERNAL_SERVER_ERROR: "内部服务器错误",
        ErrorCodes.LLM_SERVICE_UNAVAILABLE: "LLM服务不可用",
        ErrorCodes.DATABASE_CONNECTION_ERROR: "数据库连接错误",
        ErrorCodes.STORAGE_SERVICE_ERROR: "存储服务错误",
        ErrorCodes.CACHE_SERVICE_ERROR: "缓存服务错误",
        ErrorCodes.EXTERNAL_API_ERROR: "外部API错误",
        ErrorCodes.CONFIGURATION_ERROR: "配置错误",
        ErrorCodes.RESOURCE_EXHAUSTED: "资源耗尽",
        ErrorCodes.SERVICE_TEMPORARILY_UNAVAILABLE: "服务暂时不可用",
        
        # 流处理错误消息
        ErrorCodes.STREAM_INITIALIZATION_FAILED: "流初始化失败",
        ErrorCodes.STREAM_CONNECTION_LOST: "流连接丢失",
        ErrorCodes.STREAM_DATA_CORRUPTION: "流数据损坏",
        ErrorCodes.STREAM_BUFFER_OVERFLOW: "流缓冲区溢出",
        ErrorCodes.STREAM_TIMEOUT: "流处理超时",
    }
    
    @classmethod
    def get_message(cls, error_code: ErrorCodes) -> str:
        """获取错误消息"""
        return cls.MESSAGES.get(error_code, "未知错误")
    
    @classmethod
    def get_error_type(cls, error_code: ErrorCodes) -> str:
        """根据错误代码获取错误类型"""
        if 4000 <= error_code < 4100:
            return "ValidationError"
        elif 4100 <= error_code < 4200:
            return "TopicGenerationError"
        elif 4200 <= error_code < 4300:
            return "MaterialProcessingError"
        elif 4300 <= error_code < 4400:
            return "KnowledgeBaseError"
        elif 4400 <= error_code < 4500:
            return "TaskManagementError"
        elif 4500 <= error_code < 4600:
            return "QuestionGenerationError"
        elif 5000 <= error_code < 5100:
            return "SystemError"
        elif 5100 <= error_code < 5200:
            return "StreamProcessingError"
        else:
            return "UnknownError"