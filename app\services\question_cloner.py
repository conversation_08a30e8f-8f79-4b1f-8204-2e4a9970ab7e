"""
试题克隆服务
实现基于原题结构的智能克隆功能
"""

import asyncio
from typing import Dict, Any, List, Optional, Callable
from app.utils.question_parser import parse_question_structure, extract_question_prompt
from app.utils.config_manager import config_manager
from app.services.llm_manager import generate_text, generate_text_stream
from app.core.state_manager import create_stream_data, TaskStatus
from loguru import logger
from app.utils.parameter_mapper import parameter_mapper


class QuestionCloner:
    """试题克隆器"""

    def __init__(self):
        self.clone_strategies = {
            "full": self._clone_full_question,
            "content_only": self._clone_content_only,
            "structure_only": self._clone_structure_only
        }

    async def clone_question(self, original_question: Dict[str, Any],
                             strategy: str = "full",
                             clone_options: Optional[Dict[str, Any]] = None,
                             status_callback: Optional[Callable] = None,
                             exam_subject_name: str = None,
                             workflow_model_config: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        克隆试题

        Args:
            original_question: 原始试题数据（AiQuesTypePost结构）
            strategy: 克隆策略 ("full", "content_only", "structure_only")
            clone_options: 克隆选项配置
            status_callback: 状态更新回调函数，用于更新任务状态

        Returns:
            克隆后的试题数据数组
        """
        if not exam_subject_name:
            exam_subject_name = original_question.get("ExamSubjectName", "语文")
        try:
            logger.info(f"开始克隆试题，策略: {strategy}")

            # 更新状态：开始克隆
            if status_callback:
                await status_callback(TaskStatus.PROCESSING, "开始克隆试题", {
                    "step": "开始克隆",
                    "progress": 0,
                    "detail": f"克隆策略: {strategy}"
                })

            # 解析原题结构
            question_structure = parse_question_structure(original_question)

            # 更新状态：解析完成
            if status_callback:
                await status_callback(TaskStatus.PROCESSING, "解析原题结构完成", {
                    "step": "解析结构",
                    "progress": 10,
                    "detail": "原题结构解析完成"
                })

            # 判断是否为组合题
            is_composite = self._is_composite_question(original_question)
            question_count = original_question.get("QuesCount", 1)

            # 更新状态：判断题型
            if status_callback:
                question_type = "组合题" if is_composite else "基础题"
                await status_callback(TaskStatus.PROCESSING, f"识别题型: {question_type}", {
                    "step": "识别题型",
                    "progress": 20,
                    "detail": f"题型: {question_type}"
                })

            result_list = []
            if is_composite and question_count > 1:
                # 组合题且数量大于1，异步并发生成多道组合题
                async def clone_single_composite(i):
                    try:
                        # 可根据需要为每道题设置不同参数（如有）
                        cloned_question = await self._clone_composite_question(
                            original_question, question_structure, clone_options, status_callback, exam_subject_name, workflow_model_config
                        )
                        return cloned_question
                    except Exception as e:
                        logger.error(f"组合题{i+1}克隆失败: {e}")
                        # 返回错误信息，不中断整个流程
                        return {"error": f"组合题{i+1}克隆失败: {str(e)}"}

                # 创建异步任务列表
                tasks = [clone_single_composite(i)
                         for i in range(question_count)]
                result_list = await asyncio.gather(*tasks)
            else:
                if is_composite:
                    cloned_question = await self._clone_composite_question(
                        original_question, question_structure, clone_options, status_callback, exam_subject_name, workflow_model_config
                    )
                else:
                    clone_func = self.clone_strategies.get(
                        strategy, self._clone_full_question)
                    # 执行克隆
                    cloned_question = await clone_func(original_question, question_structure, clone_options, status_callback, exam_subject_name, workflow_model_config)
                result_list.append(cloned_question)

            # 更新状态：克隆完成
            if status_callback:
                await status_callback(TaskStatus.COMPLETED, "试题克隆完成", {
                    "step": "克隆完成",
                    "progress": 100,
                    "detail": "试题克隆成功"
                })

            logger.info("试题克隆完成")
            return result_list

        except Exception as e:
            logger.error(f"试题克隆失败: {e}")
            # 更新状态：克隆失败
            if status_callback:
                await status_callback(TaskStatus.FAILED, f"试题克隆失败: {str(e)}", {
                    "step": "克隆失败",
                    "progress": 0,
                    "detail": str(e)
                })
            raise

    async def _clone_full_question(self, original: Dict[str, Any],
                                   structure: Dict[str, Any],
                                   options: Optional[Dict[str, Any]] = None,
                                   status_callback: Optional[Callable] = None,
                                   exam_subject_name: str = "语文",
                                   workflow_model_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """完全克隆：保持结构，生成新内容"""
        cloned = original.copy()

        # 更新状态：开始克隆内容
        if status_callback:
            await status_callback(TaskStatus.PROCESSING, "开始克隆试题内容", {
                "step": "克隆内容",
                "progress": 30,
                "detail": "生成新的试题内容"
            })

        if "Elements" in cloned:
            cloned["Elements"] = await self._clone_elements_with_llm(cloned["Elements"], structure, status_callback, exam_subject_name, workflow_model_config)

            # 更新状态：Elements克隆完成
            if status_callback:
                await status_callback(TaskStatus.PROCESSING, "试题元素克隆完成", {
                    "step": "克隆元素",
                    "progress": 50,
                    "detail": "试题元素克隆完成"
                })

        if "QuesStr" in cloned:
            cloned["QuesStr"] = await self._generate_cloned_content(
                cloned,
                exam_subject_name,
                options,
                status_callback,
                workflow_model_config
            )

            # 更新状态：主题目克隆完成
            if status_callback:
                await status_callback(TaskStatus.PROCESSING, "主题目克隆完成", {
                    "step": "克隆主题目",
                    "progress": 70,
                    "detail": "主题目内容克隆完成"
                })

        if "Childs" in cloned and cloned["Childs"]:
            cloned["Childs"] = await self._clone_children(cloned["Childs"], status_callback, exam_subject_name, workflow_model_config)

            # 更新状态：子题目克隆完成
            if status_callback:
                await status_callback(TaskStatus.PROCESSING, "子题目克隆完成", {
                    "step": "克隆子题目",
                    "progress": 90,
                    "detail": "子题目克隆完成"
                })

        return cloned

    async def _clone_content_only(self, original: Dict[str, Any],
                                  structure: Dict[str, Any],
                                  options: Optional[Dict[str, Any]] = None,
                                  status_callback: Optional[Callable] = None,
                                  exam_subject_name: str = "语文",
                                  workflow_model_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        cloned = original.copy()

        # 更新状态：开始克隆内容
        if status_callback:
            await status_callback(TaskStatus.PROCESSING, "开始克隆试题内容", {
                "step": "克隆内容",
                "progress": 30,
                "detail": "仅克隆试题内容"
            })

        cloned["QuesStr"] = await self._generate_cloned_content(
            cloned,
            exam_subject_name,
            options,
            status_callback,
            workflow_model_config
        )

        # 更新状态：内容克隆完成
        if status_callback:
            await status_callback(TaskStatus.PROCESSING, "试题内容克隆完成", {
                "step": "克隆内容完成",
                "progress": 90,
                "detail": "试题内容克隆完成"
            })

        return cloned

    async def _clone_structure_only(self, original: Dict[str, Any],
                                    structure: Dict[str, Any],
                                    options: Optional[Dict[str, Any]] = None,
                                    status_callback: Optional[Callable] = None,
                                    exam_subject_name: str = "语文",
                                    workflow_model_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        cloned = original.copy()

        # 更新状态：开始克隆结构
        if status_callback:
            await status_callback(TaskStatus.PROCESSING, "开始克隆试题结构", {
                "step": "克隆结构",
                "progress": 30,
                "detail": "仅克隆试题结构"
            })

        cloned["QuesStr"] = ""
        if "Elements" in cloned:
            for element in cloned["Elements"]:
                if "ElementText" in element:
                    element["ElementText"] = []
        if "Childs" in cloned and cloned["Childs"]:
            cloned["Childs"] = await self._clone_children_structure_only(cloned["Childs"], exam_subject_name, workflow_model_config)

        # 更新状态：结构克隆完成
        if status_callback:
            await status_callback(TaskStatus.PROCESSING, "试题结构克隆完成", {
                "step": "克隆结构完成",
                "progress": 90,
                "detail": "试题结构克隆完成"
            })

        return cloned

    def _is_composite_question(self, question: Dict[str, Any]) -> bool:
        """判断是否为组合题"""
        # 检查是否有子题目
        if "Childs" in question and question["Childs"]:
            return True

        # 检查Elements中是否有材料
        if "Elements" in question:
            for element in question["Elements"]:
                element_type = element.get("ElementType", "")
                if element_type in parameter_mapper.PARAMETER_MAPPING:
                    mapped_label = parameter_mapper.PARAMETER_MAPPING[element_type]
                    if mapped_label == "【试题材料】":  # 使用映射表中的标准标签
                        return True

        return False

    async def _clone_composite_question(self, original: Dict[str, Any],
                                        structure: Dict[str, Any],
                                        options: Optional[Dict[str,
                                                               Any]] = None,
                                        status_callback: Optional[Callable] = None,
                                        exam_subject_name: str = "语文",
                                        workflow_model_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        logger.info("开始克隆组合题")

        # 更新状态：开始克隆组合题
        if status_callback:
            await status_callback(TaskStatus.PROCESSING, "开始克隆组合题", {
                "step": "克隆组合题",
                "progress": 30,
                "detail": "开始克隆组合题材料和子题目"
            })

        cloned = original.copy()

        # 克隆材料
        if "Elements" in cloned:
            cloned["Elements"] = await self._clone_elements_with_llm(cloned["Elements"], structure, status_callback, exam_subject_name, workflow_model_config)

            # 更新状态：材料克隆完成
            if status_callback:
                await status_callback(TaskStatus.PROCESSING, "组合题材料克隆完成", {
                    "step": "克隆材料",
                    "progress": 50,
                    "detail": "组合题材料克隆完成"
                })

        # 克隆子题目
        if "Childs" in cloned and cloned["Childs"]:
            cloned["Childs"] = await self._clone_children_with_llm(cloned["Childs"], cloned["Elements"], status_callback, exam_subject_name, workflow_model_config)

            # 更新状态：子题目克隆完成
            if status_callback:
                await status_callback(TaskStatus.PROCESSING, "组合题子题目克隆完成", {
                    "step": "克隆子题目",
                    "progress": 80,
                    "detail": "组合题子题目克隆完成"
                })

        # 组装组合题
        cloned["QuesStr"] = await self._assemble_composite_question(cloned)

        # 更新状态：组合题组装完成
        if status_callback:
            await status_callback(TaskStatus.PROCESSING, "组合题组装完成", {
                "step": "组装组合题",
                "progress": 90,
                "detail": "组合题组装完成"
            })

        return cloned

    async def _clone_children_with_llm(self, children: List[Dict[str, Any]], materials: List[Dict[str, Any]], status_callback: Optional[Callable] = None, exam_subject_name: str = "语文", workflow_model_config: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        total = len(children)
        material_context = ""
        for element in materials:
            element_type = element.get("ElementType", "")
            if element_type in parameter_mapper.PARAMETER_MAPPING:
                mapped_label = parameter_mapper.PARAMETER_MAPPING[element_type]
                if mapped_label == "【试题材料】":  # 使用映射表中的标准标签
                    material_texts = element.get("ElementText", [])
                    material_context += "\n".join(material_texts)

        async def clone_single_child(i, child):
            try:
                # 细化进度：开始克隆子题目
                if status_callback:
                    progress = 50 + int(30 * (i + 1) / total)  # 50-80%
                    await status_callback(TaskStatus.PROCESSING, f"正在克隆第{i + 1}个子题目，共{total}", {
                        "step": "clone_subquestion",
                        "progress": progress,
                        "detail": f"正在克隆第{i + 1}个子题目，共{total}"
                    })

                elements = child.get("Elements", [])
                child_content_parts = []
                for element in elements:
                    element_type = element.get("ElementType", "")
                    element_texts = element.get("ElementText", [])
                    if element_type in parameter_mapper.PARAMETER_MAPPING:
                        mapped_label = parameter_mapper.PARAMETER_MAPPING[element_type]
                        if element_texts:
                            if mapped_label:
                                child_content_parts.append(
                                    f"{mapped_label}" + "\n".join(element_texts))
                            else:
                                child_content_parts.append(
                                    "\n".join(element_texts))
                    else:
                        child_content_parts.append("\n".join(element_texts))
                child_ques_str = "\n\n".join(child_content_parts).strip()
                prompt = self._build_subquestion_clone_prompt(
                    child_ques_str,
                    child,
                    material_context,
                    exam_subject_name
                )
                if workflow_model_config:
                    model_name = workflow_model_config.get(
                        "name", "deepseek-r1")
                    api_key = workflow_model_config.get("api_key", "")
                    api_base = workflow_model_config.get("api_base", "")
                    additional_params = {
                        "top_p": workflow_model_config.get("top_p", 0.5),
                        "top_k": workflow_model_config.get("top_k", 5)
                    }
                    cloned_content = await generate_text(prompt, model_name=model_name, api_key=api_key, api_base=api_base, **additional_params)
                else:
                    cloned_content = await generate_text(prompt)
                child_cloned = child.copy()
                if cloned_content and cloned_content.strip():
                    child_cloned["QuesStr"] = cloned_content
                    if elements:
                        updated_elements = []
                        for element in elements:
                            cloned_element = element.copy()
                            element_type = element.get("ElementType", "")
                            if element_type in parameter_mapper.PARAMETER_MAPPING:
                                mapped_label = parameter_mapper.PARAMETER_MAPPING[element_type]
                                if mapped_label == "[试题题干]":
                                    cloned_element["ElementText"] = [
                                        cloned_content]
                            updated_elements.append(cloned_element)
                        child_cloned["Elements"] = updated_elements
                else:
                    logger.warning(f"子题目{i + 1}LLM生成失败，保持原内容")
                    child_cloned["QuesStr"] = child_ques_str or f"子题目{i + 1}：内容生成中..."
                # 细化进度：子题目克隆完成
                if status_callback:
                    progress = 50 + int(30 * (i + 1) / total)
                    await status_callback(TaskStatus.PROCESSING, f"子题目{i + 1}克隆完成", {
                        "step": f"clone_subquestion_{i + 1}_done",
                        "progress": progress,
                        "detail": f"第{i + 1}个子题目克隆完成"
                    })
                return child_cloned
            except Exception as e:
                logger.error(f"克隆子题目{i + 1}失败: {e}")
                if status_callback:
                    progress = 50 + int(30 * (i + 1) / total)
                    await status_callback(TaskStatus.PROCESSING, f"子题目{i + 1}克隆失败", {
                        "step": f"clone_subquestion_{i + 1}_fail",
                        "progress": progress,
                        "detail": f"第{i + 1}个子题目克隆失败: {str(e)}"
                    })
                return child

        tasks = [clone_single_child(i, child)
                 for i, child in enumerate(children)]
        return await asyncio.gather(*tasks)

    async def _clone_elements_with_llm(self, elements: List[Dict[str, Any]],
                                       structure: Dict[str, Any],
                                       status_callback: Optional[Callable] = None,
                                       exam_subject_name: str = "语文",
                                       workflow_model_config: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """使用LLM克隆题目元素"""
        cloned_elements = []

        for element in elements:
            cloned_element = element.copy()
            element_type = element.get("ElementType", "")

            # 严格使用参数映射表处理元素类型
            if element_type in parameter_mapper.PARAMETER_MAPPING:
                mapped_label = parameter_mapper.PARAMETER_MAPPING[element_type]

                # 使用映射表中的标准标签进行判断
                if mapped_label == "【试题材料】":  # 使用映射表中的标准标签
                    # 克隆语言材料
                    original_texts = element.get("ElementText", [])
                    cloned_texts = []

                    for text in original_texts:
                        try:
                            # 使用LLM生成新材料
                            prompt = self._build_material_clone_prompt(
                                text, structure, exam_subject_name)

                            # 更新状态：开始克隆材料
                            if status_callback:
                                await status_callback(TaskStatus.PROCESSING, f"开始克隆{mapped_label}：{text[:50]}...", {
                                    "step": "克隆材料",
                                    "progress": 0,
                                    "detail": f"正在克隆{mapped_label}，长度：{len(text)}"
                                })

                            # 使用workflow模型配置进行生成
                            if workflow_model_config:
                                model_name = workflow_model_config.get(
                                    "name", "deepseek-r1")
                                api_key = workflow_model_config.get(
                                    "api_key", "")
                                api_base = workflow_model_config.get(
                                    "api_base", "")
                                # 只传递非冲突的参数
                                additional_params = {
                                    "top_p": workflow_model_config.get("top_p", 0.5),
                                    "top_k": workflow_model_config.get("top_k", 5)
                                }
                                cloned_text = await generate_text(prompt, model_name=model_name, api_key=api_key, api_base=api_base, **additional_params)
                            else:
                                cloned_text = await generate_text(prompt)

                            cloned_texts.append(cloned_text)

                            # 更新状态：材料克隆完成
                            if status_callback:
                                await status_callback(TaskStatus.PROCESSING, f"{mapped_label}克隆完成，长度：{len(cloned_text)}", {
                                    "step": "克隆材料完成",
                                    "progress": 0,
                                    "detail": f"{mapped_label}克隆完成，长度：{len(cloned_text)}"
                                })

                        except Exception as e:
                            logger.error(f"{mapped_label}克隆失败: {e}")
                            cloned_texts.append(
                                f"克隆的{mapped_label}内容：{text[:30]}...")

                    cloned_element["ElementText"] = cloned_texts
                elif mapped_label in ["[试题题干]", "\n【答案】", "\n【解析】"]:
                    # 对于题目、答案、解析等元素，暂时保持原内容
                    # 这些内容会在子题目克隆时单独处理
                    # 使用映射表中的标准标签进行判断
                    pass
                else:
                    # 其他映射表中的元素类型，保持原内容
                    pass
            else:
                # 不在映射表中的元素类型，保持原内容
                pass

            cloned_elements.append(cloned_element)

        return cloned_elements

    async def _assemble_composite_question(self, cloned_question: Dict[str, Any]) -> str:
        """组装组合题：材料放在最外层QuesStr，子题放在Childs第一个元素的QuesStr中"""
        material_parts = []
        subquestion_parts = []

        # 提取材料部分，严格使用参数映射表
        if "Elements" in cloned_question:
            for element in cloned_question["Elements"]:
                element_type = element.get("ElementType", "")
                if element_type in parameter_mapper.PARAMETER_MAPPING:
                    mapped_label = parameter_mapper.PARAMETER_MAPPING[element_type]
                    texts = element.get("ElementText", [])
                    if texts:
                        if mapped_label:  # 如果映射标签不为空
                            material_parts.append(
                                f"{mapped_label}：\n" + "\n".join(texts))
                        else:  # 如果映射标签为空
                            material_parts.extend(texts)

        # 提取子题目
        if "Childs" in cloned_question:
            for i, child in enumerate(cloned_question["Childs"], 1):
                child_content = child.get('QuesStr', '')
                if not child_content:
                    # 如果QuesStr为空，尝试从Elements中获取内容，严格使用参数映射表
                    elements = child.get("Elements", [])
                    if elements:
                        child_content_parts = []
                        for element in elements:
                            element_type = element.get("ElementType", "")
                            element_texts = element.get("ElementText", [])

                            if element_type in parameter_mapper.PARAMETER_MAPPING:
                                mapped_label = parameter_mapper.PARAMETER_MAPPING[element_type]
                                if element_texts:
                                    if mapped_label:  # 如果映射标签不为空
                                        child_content_parts.append(
                                            f"{mapped_label}" + "\n".join(element_texts))
                                    else:  # 如果映射标签为空（如choice）
                                        child_content_parts.append(
                                            "\n".join(element_texts))
                            else:
                                # 其他类型，直接使用内容
                                child_content_parts.append(
                                    "\n".join(element_texts))
                        separator = f"{'-'*10}试题分割线{'-'*10}"
                        child_content = separator.join(
                            child_content_parts).strip()

                if child_content:
                    subquestion_parts.append(f"\n{i}. {child_content}")
                else:
                    subquestion_parts.append(f"\n{i}. 子题目{i}：内容生成中...")

        # 材料放在最外层QuesStr
        material_content = "\n".join(material_parts)
        qure_material_content = material_content.replace("[试题题干]", "")
        cloned_question["QuesStr"] = material_content

        # 子题内容放在Childs第一个元素的QuesStr中，删除其他元素
        if "Childs" in cloned_question and cloned_question["Childs"] and subquestion_parts:
            separator = f"{'-'*10}试题分割线{'-'*10}"
            subquestion_content = separator.join(
                subquestion_parts)  # .replace("【试题描述】", "")
            cloned_question["Childs"][0]["QuesStr"] = subquestion_content

            # 删除其他Childs元素，只保留第一个
            cloned_question["Childs"] = [cloned_question["Childs"][0]]

        return material_content

    def _build_material_clone_prompt(self, original_material: str,
                                     structure: Dict[str, Any],
                                     exam_subject_name: str = "语文") -> str:
        """构建材料克隆提示词"""
        template = config_manager.get_prompt_template(
            "composite_material_clone")

        # # 新增：结构化结构描述
        # original_content, structure_content = parameter_mapper.map_question_structure_to_prompt(
        #     structure, is_clone=True)
        # - 原题结构：{original_content}
        # - 材料结构要求：{mapped_structure}
        if not template:
            # 降级为默认提示词
            template = """
## 角色与任务

你是一个专业的**试题材料克隆专家**。请基于以下原题材料进行克隆，生成新的材料内容。

## 原材料信息

- **材料内容**：{original_material}
- **学科**：{subject}
- **受试人群**：{grade}
- **难度**：{difficulty}

## 克隆要求

1. **类型一致**：保持相同的材料类型和长度
2. **内容创新**：使用不同的具体内容
3. **难度对应**：保持相同的难度和知识点要求
4. **质量保证**：确保材料质量不低于原题
5. **结构严格**：严格按照结构要求生成内容

---

**请生成新的材料内容：**
                """

        # 获取题目属性
        ques_props = structure.get("QuesProps", {})
        subject = exam_subject_name or ques_props.get(
            "subject", {}).get("selected", "语文")
        grade = ques_props.get("grade", {}).get(
            "selected", config_manager.get("question_clone.default_grade", "高中"))
        difficulty = ques_props.get("difficulty", {}).get("selected", "中等")

        # 获取材料标签，使用映射表中的标准标签
        material_label = parameter_mapper.PARAMETER_MAPPING.get(
            "langMaterial", "【试题材料】")

        return template.format(
            original_material=original_material,
            subject=subject,
            grade=grade,
            difficulty=difficulty,
            # original_content=original_content,
            # mapped_structure=structure_content
        )

    def _build_subquestion_clone_prompt(self, original_subquestion: str,
                                        subquestion_props: Dict[str, Any],
                                        material_context: str,
                                        exam_subject_name: str = "语文") -> str:
        template = config_manager.get_prompt_template(
            "composite_subquestion_clone")
        # 新增：结构化结构描述
        original_content, structure_content = parameter_mapper.map_question_structure_to_prompt(
            subquestion_props, is_clone=True) if subquestion_props else ("", "")
        if not template:
            template = """
            你是一个专业的子题目克隆专家。请基于以下原题子题目进行克隆，生成新的子题目内容。
            
            原子题目信息：
            - 子题目内容：{original_subquestion}
            - 题目类型：{subquestion_type}
            
            - 学科：{subject}
            - 原子题结构：{original_content}
            
            
            克隆试题信息：
            - 材料背景：{material_context}
            - 子题结构要求：{mapped_structure}
            
            克隆要求：
            1. 保持相同的题目类型和分值
            2. 基于新的材料内容生成相应的子题目
            3. 保持相同的知识点要求
            4. 确保题目质量不低于原题
            5. 严格按照结构要求生成内容
            6. 生成的内容应该是完整的子题目，包含题目描述和要求
            
            请生成新的子题目内容：
            """

        # 获取子题目类型
        subquestion_type = "简答题"
        if subquestion_props and isinstance(subquestion_props, dict):
            subquestion_type = subquestion_props.get("QuesTypeName", "简答题")

        subject = exam_subject_name or "语文"
        return template.format(
            original_subquestion=original_subquestion,
            subquestion_type=subquestion_type,
            material_context=material_context,
            subject=subject,
            original_content=original_content,
            mapped_structure=structure_content
        )

    def _build_clone_prompt(self,
                            structure: Dict[str, Any],
                            exam_subject_name: str = "语文",
                            options: Optional[Dict[str, Any]] = None) -> str:
        # template = config_manager.get_prompt_template("basic_clone")
        # 新增：结构化结构描述
        original_content, structure_content = parameter_mapper.map_question_structure_to_prompt(
            structure, is_clone=True)
        ques_props = structure.get("properties", {})
        question_type = ques_props.get("ques_type_name", "未知")
        subject = exam_subject_name or "语文"
        grade = ques_props.get("grade", {}).get(
            "selected", config_manager.get("question_clone.default_grade", "高中"))
        difficulty = ques_props.get("difficulty", {}).get("selected", "中等")
        knowledge_points = ques_props.get(
            "knowledge_points", {}).get("selected", [])
        clone_count = structure.get(
            "QuesCount", 1)
        prompt = f"""
## 角色与任务

你是一个专业的**试题克隆专家**。请基于下面的原题进行克隆，生成{clone_count}道新的试题。

## 原题信息

- **题目内容**：{original_content}
- **题目类型**：{question_type}
- **学科**：{subject}
- **受试人群**：{grade}
- **难度**：{difficulty}
- **知识点**：{knowledge_points}

## 输出试题结构

```
{structure_content}
```

## 具体工作要求

1. **结构一致**：保持相同的题型结构和难度
2. **内容创新**：使用不同的具体内容（如不同的材料、不同的题目描述）
3. **知识对应**：保持相同的知识点要求
4. **质量保证**：确保试题质量不低于原题
5. **学科特色**：生成的内容要符合{subject}学科的特点

## 输出要求

1. **内容纯净**：不要输出任何与试题无关的内容，注意只需要输出试题，不要输出试题等文字内容
2. **符合要求**：确保试题符合克隆要求
3. **结构严格**：注意严格按照输出试题结构的克隆试题，不要将结构的内容作为试题内容
4. **格式简洁**：注意不要使用markdown格式输出
5. **分割清晰**：当克隆试题输出量大于1的时候，请使用"----------试题分割线----------"进行分割

---

**请生成新的试题内容：**
        """

        return prompt.strip()

    async def _generate_cloned_content(self,
                                       structure: Dict[str, Any],
                                       exam_subject_name: str = "语文",
                                       options: Optional[Dict[str,
                                                              Any]] = None,
                                       status_callback: Optional[Callable] = None,
                                       workflow_model_config: Optional[Dict[str, Any]] = None) -> str:
        try:
            prompt = self._build_clone_prompt(
                structure, exam_subject_name, options)
            # 更新状态：开始生成试题内容
            if status_callback:
                await status_callback(TaskStatus.PROCESSING, f"开始生成试题内容...",
                                      {
                                          "step": "生成试题内容",
                                          "progress": 0,
                                          "detail": f"正在生成试题内容..."
                                      })

            # 使用workflow模型配置进行生成
            if workflow_model_config:
                model_name = workflow_model_config.get(
                    "name", "deepseek-r1")
                api_key = workflow_model_config.get("api_key", "")
                api_base = workflow_model_config.get("api_base", "")
                # 只传递非冲突的参数
                additional_params = {
                    "top_p": workflow_model_config.get("top_p", 0.5),
                    "top_k": workflow_model_config.get("top_k", 5)
                }
                cloned_content = await generate_text(prompt, model_name=model_name, api_key=api_key, api_base=api_base, **additional_params)
            else:
                cloned_content = await generate_text(prompt)

            # 更新状态：试题内容生成完成
            if status_callback:
                await status_callback(TaskStatus.PROCESSING, f"试题内容生成完成，长度：{len(cloned_content)}", {
                    "step": "生成试题内容完成",
                    "progress": 100,
                    "detail": f"试题内容生成完成，长度：{len(cloned_content)}"
                })
            return cloned_content
        except Exception as e:
            logger.error(f"LLM生成内容失败: {e}")
            return "基于原题克隆的新试题内容：\n...\n\n这是克隆生成的试题，保持了原题的结构和要求。"

    async def _clone_children(self, children: List[Dict[str, Any]], status_callback: Optional[Callable] = None, exam_subject_name: str = "语文", workflow_model_config: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """克隆子题目（已废弃，使用_clone_children_with_llm替代）"""
        logger.warning("_clone_children方法已废弃，请使用_clone_children_with_llm")
        return children

    async def _clone_children_structure_only(self, children: List[Dict[str, Any]], exam_subject_name: str = "语文", workflow_model_config: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """递归克隆子题目结构"""
        cloned_children = []

        for child in children:
            # 递归克隆每个子题目的结构
            cloned_child = await self._clone_structure_only(child, {}, {}, None, exam_subject_name, workflow_model_config)
            cloned_children.append(cloned_child)

        return cloned_children

    def get_clone_progress(self, step: int, total_steps: int) -> Dict[str, Any]:
        """获取克隆进度信息"""
        return {
            "step": step,
            "total_steps": total_steps,
            "progress": f"{(step / total_steps) * 100:.1f}%",
            "message": f"正在克隆第 {step}/{total_steps} 步"
        }


# 全局实例
question_cloner = QuestionCloner()


async def clone_question(original_question: Dict[str, Any],
                         strategy: str = "full",
                         options: Optional[Dict[str, Any]] = None,
                         status_callback: Optional[Callable] = None,
                         exam_subject_name: str = None,
                         workflow_model_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    智能克隆主入口
    """
    # 获取学科和项目名
    exam_subject = exam_subject_name or original_question.get(
        "ExamSubjectName", "语文")
    exam_project = original_question.get("ExamProjectName", "")
    cloner = QuestionCloner()
    result = await cloner.clone_question(
        original_question=original_question,
        strategy=strategy,
        clone_options=options,
        status_callback=status_callback,
        exam_subject_name=exam_subject,
        workflow_model_config=workflow_model_config
    )
    # 返回QuesPostData结构
    return {
        "AiQuesTypePost": result,
        "ExamSubjectName": exam_subject,
        "ExamProjectName": exam_project
    }
