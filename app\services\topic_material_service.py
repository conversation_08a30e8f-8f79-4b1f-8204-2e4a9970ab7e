from typing import Optional
from loguru import logger
from app.services.llm_manager import generate_text
from app.core.state_manager import task_manager, TaskStatus


class TopicMaterialService:
    """主题命题材料生成服务"""
    
    def __init__(self):
        self.logger = logger
    
    async def generate_material(self, topic: str, subject: str, task_id: Optional[str] = None) -> str:
        """
        基于主题生成命题材料
        
        Args:
            topic: 主题或知识点
            subject: 学科
            task_id: 任务ID（可选）
            
        Returns:
            生成的命题材料
        """
        try:
            self.logger.info(f"开始生成主题材料: {topic} - {subject}")
            
            # 更新任务状态
            if task_id:
                await task_manager.update_task_status(
                    task_id,
                    TaskStatus.PROCESSING,
                    step_info={
                        "step": "building_prompt",
                        "progress": 10,
                        "detail": "构建生成提示词"
                    }
                )
            
            # 构建生成提示词
            prompt = self._build_material_generation_prompt(topic, subject)
            
            # 更新任务状态
            if task_id:
                await task_manager.update_task_status(
                    task_id,
                    TaskStatus.PROCESSING,
                    step_info={
                        "step": "calling_llm",
                        "progress": 30,
                        "detail": "调用LLM生成材料"
                    }
                )
            
            # 调用LLM生成材料
            material = await generate_text(
                prompt=prompt,
                model_name="deepseek-r1",
                temperature=0.7,
                max_tokens=2000
            )
            
            # 更新任务状态
            if task_id:
                await task_manager.update_task_status(
                    task_id,
                    TaskStatus.PROCESSING,
                    step_info={
                        "step": "processing_result",
                        "progress": 80,
                        "detail": "处理生成结果"
                    }
                )
            
            # 处理和优化生成的材料
            processed_material = self._process_generated_material(material, topic, subject)
            
            # 更新任务状态
            if task_id:
                await task_manager.update_task_status(
                    task_id,
                    TaskStatus.PROCESSING,
                    step_info={
                        "step": "completed",
                        "progress": 100,
                        "detail": "材料生成完成"
                    }
                )
            
            self.logger.info(f"主题材料生成成功: {topic}")
            return processed_material
            
        except Exception as e:
            self.logger.error(f"主题材料生成失败: {e}")
            
            # 更新任务状态为失败
            if task_id:
                await task_manager.update_task_status(
                    task_id,
                    TaskStatus.FAILED,
                    error_message=str(e)
                )
            
            raise
    
    def _build_material_generation_prompt(self, topic: str, subject: str) -> str:
        """构建材料生成提示词"""
        
        prompt = f"""你是一位专业的{subject}学科命题专家，请根据给定的主题生成适合命题的材料。

主题：{topic}
学科：{subject}

要求：
1. 生成的材料应该围绕主题展开，内容丰富、准确
2. 材料应该适合作为命题的基础，包含足够的信息点
3. 内容应该符合{subject}学科的特点和要求
4. 材料长度适中，一般在300-800字之间
5. 语言表达清晰、准确，符合学术规范

请生成命题材料："""
        
        return prompt
    
    def _process_generated_material(self, material: str, topic: str, subject: str) -> str:
        """处理和优化生成的材料"""
        
        # 基本清理
        processed = material.strip()
        
        # 移除可能的提示词残留
        if processed.startswith("根据"):
            lines = processed.split('\n')
            processed = '\n'.join(lines[1:]).strip()
        
        # 确保材料不为空
        if not processed:
            processed = f"关于{topic}的{subject}学科材料生成失败，请重试。"
        
        # 添加材料标识（如果需要）
        if not processed.startswith(f"【{subject}】"):
            processed = f"【{subject}】{processed}"
        
        return processed


# 创建全局服务实例
topic_material_service = TopicMaterialService()
