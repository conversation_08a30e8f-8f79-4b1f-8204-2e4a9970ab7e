
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named pyimod02_importers - imported by D:\Miniconda\envs\give_tag\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), D:\Miniconda\envs\give_tag\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), webbrowser (delayed), getpass (delayed), netrc (delayed, conditional), _pytest._py.path (delayed), distutils.util (delayed, conditional, optional), setuptools._vendor.backports.tarfile (optional), distutils.archive_util (optional), http.server (delayed, optional), setuptools._distutils.util (delayed, conditional, optional), setuptools._distutils.archive_util (optional)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), _pytest._py.path (delayed), setuptools._vendor.backports.tarfile (optional), distutils.archive_util (optional), setuptools._distutils.archive_util (optional)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named fcntl - imported by subprocess (optional), tqdm.utils (delayed, optional)
missing module named 'org.python' - imported by copy (optional), xml.sax (delayed, conditional)
missing module named org - imported by pickle (optional)
missing module named urllib.urlencode - imported by urllib (conditional), requests_toolbelt._compat (conditional)
missing module named posix - imported by os (conditional, optional), posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional)
missing module named resource - imported by posix (top-level)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), wheel.vendored.packaging._manylinux (delayed, optional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level), watchfiles.run (top-level), loguru._logger (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by click._termui_impl (conditional), tty (top-level), getpass (optional), tqdm.utils (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.cpu_count - imported by multiprocessing (top-level), langsmith._internal._background_thread (top-level)
missing module named multiprocessing.RLock - imported by multiprocessing (delayed, conditional, optional), tqdm.std (delayed, conditional, optional)
missing module named multiprocessing.current_process - imported by multiprocessing (top-level), loguru._logger (top-level)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named annotationlib - imported by typing_extensions (conditional), attr._compat (conditional)
missing module named '_typeshed.importlib' - imported by pkg_resources (conditional)
missing module named _typeshed - imported by anyio._core._fileio (conditional), pydantic_core._pydantic_core (top-level), pydantic._internal._dataclasses (conditional), httpx._transports.wsgi (conditional), pkg_resources (conditional), setuptools.glob (conditional), setuptools.compat.py311 (conditional), setuptools._distutils.dist (conditional)
missing module named jnius - imported by setuptools._vendor.platformdirs.android (delayed, conditional, optional)
missing module named android - imported by setuptools._vendor.platformdirs.android (delayed, conditional, optional)
missing module named 'distutils._modified' - imported by setuptools._distutils.file_util (delayed)
missing module named 'distutils._log' - imported by setuptools._distutils.command.bdist_dumb (top-level), setuptools._distutils.command.bdist_rpm (top-level), setuptools._distutils.command.build_clib (top-level), setuptools._distutils.command.build_ext (top-level), setuptools._distutils.command.build_py (top-level), setuptools._distutils.command.build_scripts (top-level), setuptools._distutils.command.clean (top-level), setuptools._distutils.command.config (top-level), setuptools._distutils.command.install (top-level), setuptools._distutils.command.install_scripts (top-level), setuptools._distutils.command.sdist (top-level)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named readline - imported by cmd (delayed, conditional, optional), code (delayed, conditional, optional), pdb (delayed, optional), websockets.cli (delayed, optional), site (delayed, optional), rlcompleter (optional)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named importlib_resources - imported by tqdm.cli (delayed, conditional, optional), setuptools._vendor.jaraco.text (optional)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional)
missing module named collections.MutableSequence - imported by collections (optional), jsonpatch (optional)
missing module named collections.Sequence - imported by collections (optional), jsonpatch (optional)
missing module named collections.MutableMapping - imported by collections (conditional), requests_toolbelt._compat (conditional), jsonpatch (optional)
missing module named collections.Mapping - imported by collections (conditional), requests_toolbelt._compat (conditional)
missing module named pydantic.BaseModel - imported by pydantic (conditional), pydantic._internal._typing_extra (conditional), pydantic._internal._import_utils (delayed, conditional), pydantic._internal._core_utils (delayed), pydantic.deprecated.copy_internals (delayed, conditional), fastapi.exceptions (top-level), fastapi.types (top-level), fastapi._compat (top-level), fastapi.openapi.models (top-level), fastapi.security.http (top-level), fastapi.utils (top-level), fastapi.encoders (top-level), fastapi.routing (top-level), app.models.schemas (top-level), openai.resources.beta.realtime.realtime (top-level), langchain_core.language_models.base (top-level), langchain_core.load.dump (top-level), langchain_core.load.serializable (top-level), langchain_core.utils.pydantic (top-level), langchain_core.prompts.base (top-level), langchain_core.runnables.base (top-level), langchain_core.documents.compressor (top-level), langsmith.schemas (optional), langsmith.evaluation.evaluator (optional), langsmith.evaluation.string_evaluator (top-level), langchain_core.prompts.structured (top-level), langchain_core.prompts.string (top-level), langchain_core.prompts.prompt (top-level), langchain_core.runnables.graph (conditional), langchain_core.runnables.fallbacks (top-level), langchain_core.tools.base (top-level), langchain_core.utils.function_calling (top-level), langchain_core.tools.convert (top-level), langchain_core.tools.retriever (top-level), langchain_core.runnables.passthrough (top-level), langchain_core.runnables.configurable (top-level), langchain_core.runnables.branch (top-level), langchain_core.runnables.history (top-level), langchain_core.chat_history (top-level), langchain_core.prompts.few_shot (top-level), langchain_core.example_selectors.length_based (top-level), langchain_core.example_selectors.semantic_similarity (top-level), langchain_core.embeddings.fake (top-level), langchain_core.outputs.chat_result (top-level), langchain_core.outputs.llm_result (top-level), langchain_core.outputs.run_info (top-level), langchain_core.language_models.chat_models (top-level), langchain_openai.chat_models.azure (top-level), langchain_openai.chat_models.base (top-level), langchain_openai.embeddings.base (top-level), langchain_deepseek.chat_models (top-level)
missing module named cython - imported by pydantic.v1.version (optional)
missing module named email_validator - imported by pydantic.networks (delayed, conditional, optional), pydantic.v1.networks (delayed, conditional, optional), fastapi.openapi.models (optional), pydantic.v1._hypothesis_plugin (optional)
missing module named toml - imported by pydantic.v1.mypy (delayed, conditional, optional)
missing module named 'mypy.version' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.util' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.typevars' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.types' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.server' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.semanal' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.plugins' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.plugin' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.options' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.nodes' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.errorcodes' - imported by pydantic.v1.mypy (top-level)
missing module named 'IPython.core' - imported by dotenv.ipython (top-level)
missing module named IPython - imported by dotenv.ipython (top-level), loguru._colorama (delayed, conditional, optional)
missing module named hypothesis - imported by pydantic.v1._hypothesis_plugin (top-level)
missing module named 'mypy.typeops' - imported by pydantic.mypy (top-level)
missing module named 'mypy.type_visitor' - imported by pydantic.mypy (top-level)
missing module named 'mypy.state' - imported by pydantic.mypy (top-level)
missing module named 'mypy.expandtype' - imported by pydantic.mypy (top-level)
missing module named mypy - imported by pydantic.mypy (top-level)
missing module named eval_type_backport - imported by pydantic._internal._typing_extra (delayed, optional)
missing module named 'rich.pretty' - imported by pydantic._internal._core_utils (delayed)
missing module named rich - imported by pydantic._internal._core_utils (conditional)
missing module named pydantic.validate_arguments - imported by pydantic (top-level), langchain_core.tools.base (top-level)
missing module named pydantic.PydanticUserError - imported by pydantic (top-level), pydantic.root_model (top-level)
missing module named pydantic.PydanticSchemaGenerationError - imported by pydantic (delayed), pydantic.functional_validators (delayed, conditional), fastapi._compat (conditional)
missing module named simplejson - imported by requests.compat (conditional, optional)
missing module named dummy_threading - imported by requests.cookies (optional)
missing module named zstandard.backend_rust - imported by zstandard (conditional)
missing module named compression - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named 'h2.events' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level), urllib3.http2.connection (top-level)
missing module named 'h2.connection' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level), urllib3.http2.connection (top-level)
missing module named 'h2.config' - imported by httpcore._async.http2 (top-level), urllib3.http2.connection (top-level)
missing module named brotli - imported by aiohttp.compression_utils (optional), httpx._decoders (optional), urllib3.util.request (optional), urllib3.response (optional)
missing module named brotlicffi - imported by aiohttp.compression_utils (optional), httpx._decoders (optional), urllib3.util.request (optional), urllib3.response (optional)
missing module named socks - imported by urllib3.contrib.socks (optional)
missing module named cryptography - imported by urllib3.contrib.pyopenssl (top-level), requests (conditional, optional)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed, conditional)
missing module named 'cryptography.x509' - imported by urllib3.contrib.pyopenssl (delayed, optional)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level)
missing module named chardet - imported by requests.compat (optional), requests (optional), requests.packages (optional)
missing module named 'pyodide.ffi' - imported by urllib3.contrib.emscripten.fetch (delayed, optional)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named pyppeteer - imported by langchain_core.runnables.graph_mermaid (delayed, optional)
missing module named pygraphviz - imported by langchain_core.runnables.graph_png (delayed, optional)
missing module named 'grandalf.routing' - imported by langchain_core.runnables.graph_ascii (delayed, optional)
missing module named 'grandalf.layouts' - imported by langchain_core.runnables.graph_ascii (delayed, optional)
missing module named grandalf - imported by langchain_core.runnables.graph_ascii (delayed, optional)
missing module named jinja2 - imported by langchain_core.prompts.string (delayed, optional)
missing module named defusedxml - imported by langchain_core.output_parsers.xml (delayed, conditional, optional)
missing module named langchain - imported by langsmith.evaluation.integrations._langchain (conditional), langsmith.env._runtime_env (delayed, optional), langchain_core.globals (delayed, optional)
missing module named 'tornado.concurrent' - imported by tenacity.tornadoweb (conditional)
missing module named curio - imported by sniffio._impl (delayed, conditional)
missing module named trio - imported by watchfiles.main (conditional), httpx._transports.asgi (delayed, conditional), httpcore._synchronization (optional), httpcore._backends.trio (top-level), openai.resources.vector_stores.file_batches (delayed, conditional), tenacity.asyncio (delayed, conditional), anyio._backends._trio (optional)
missing module named tornado - imported by tenacity (optional), tenacity.tornadoweb (top-level)
missing module named numpy - imported by openai._extras.numpy_proxy (delayed, conditional, optional), langsmith._internal._embedding_distance (delayed, conditional, optional), _pytest.python_api (delayed, conditional), langchain_core.embeddings.fake (delayed), langchain_core.vectorstores.utils (delayed, conditional, optional), langchain_core.vectorstores.in_memory (delayed, optional), tiktoken.core (delayed, conditional)
missing module named langchain_text_splitters - imported by langchain_core.document_loaders.base (delayed, conditional, optional), langchain_core.messages.utils (delayed, conditional, optional)
missing module named simsimd - imported by langsmith._internal._embedding_distance (delayed, optional), langchain_core.vectorstores.utils (delayed, optional)
missing module named transformers - imported by langchain_core.language_models.base (delayed, optional), langchain_openai.embeddings.base (delayed, conditional, optional)
missing module named 'langchain.evaluation' - imported by langsmith.evaluation.integrations._langchain (delayed, conditional)
missing module named 'IPython.display' - imported by tqdm.notebook (conditional, optional)
missing module named 'IPython.html' - imported by tqdm.notebook (conditional, optional)
missing module named ipywidgets - imported by tqdm.notebook (conditional, optional)
missing module named setuptools_scm - imported by tqdm.version (optional)
missing module named 'matplotlib.pyplot' - imported by tqdm.gui (delayed)
missing module named matplotlib - imported by tqdm.gui (delayed)
missing module named 'pandas.core' - imported by tqdm.std (delayed, optional)
missing module named pandas - imported by openai._extras.pandas_proxy (delayed, conditional, optional), langsmith.evaluation._runner (delayed, conditional, optional), tqdm.std (delayed, optional), langsmith.evaluation._arunner (conditional), langsmith.client (delayed, conditional)
missing module named orjson.loads - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named orjson.dumps - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named orjson.JSONDecodeError - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named orjson.Fragment - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named orjson.OPT_SERIALIZE_UUID - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named orjson.OPT_SERIALIZE_NUMPY - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named orjson.OPT_SERIALIZE_DATACLASS - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named orjson.OPT_NON_STR_KEYS - imported by orjson (optional), langsmith._internal._orjson (optional)
missing module named langchain_anthropic - imported by langsmith.client (delayed, optional)
missing module named 'langchain.smith' - imported by langsmith.client (delayed, optional)
missing module named langsmith_pyo3 - imported by langsmith.client (delayed, conditional, optional)
missing module named 'opentelemetry.sdk' - imported by langsmith._internal.otel._otel_client (conditional, optional), langsmith.client (optional)
missing module named 'opentelemetry.context' - imported by langsmith._internal._background_thread (conditional), langsmith._internal.otel._otel_exporter (conditional, optional)
missing module named 'opentelemetry.exporter' - imported by langsmith._internal.otel._otel_client (conditional, optional)
missing module named 'opentelemetry.trace' - imported by langsmith.run_helpers (delayed), langsmith.client (conditional, optional), langsmith._internal.otel._otel_exporter (conditional, optional)
missing module named opentelemetry - imported by langsmith.run_helpers (delayed, conditional, optional), langsmith._internal._background_thread (delayed, optional), langsmith.client (conditional, optional), langsmith._internal.otel._otel_exporter (conditional, optional)
missing module named urlparse - imported by requests_toolbelt._compat (conditional)
missing module named Queue - imported by requests_toolbelt._compat (conditional)
missing module named 'requests.packages.urllib3' - imported by requests_toolbelt._compat (conditional, optional)
missing module named psutil - imported by langsmith.env._runtime_env (optional)
missing module named vcr - imported by langsmith.utils (delayed, optional)
missing module named 'trio.testing' - imported by anyio._backends._trio (delayed)
missing module named 'trio.lowlevel' - imported by anyio._backends._trio (optional)
missing module named 'trio.hazmat' - imported by anyio._backends._trio (optional)
missing module named trio_typing - imported by anyio._backends._trio (conditional)
missing module named 'trio.to_thread' - imported by anyio._backends._trio (top-level)
missing module named 'trio.socket' - imported by anyio._backends._trio (top-level)
missing module named outcome - imported by anyio._backends._trio (top-level)
missing module named 'trio.from_thread' - imported by anyio._backends._trio (top-level)
missing module named uvloop - imported by aiohttp.worker (delayed), anyio._backends._asyncio (delayed, conditional, optional), uvicorn.loops.auto (delayed, optional), uvicorn.loops.uvloop (top-level)
missing module named socksio - imported by httpcore._sync.socks_proxy (top-level), httpcore._async.socks_proxy (top-level), httpx._transports.default (delayed, conditional, optional)
missing module named 'h2.settings' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.exceptions' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named h2 - imported by httpcore._sync.http2 (top-level), httpx._client (delayed, conditional, optional)
missing module named 'rich.table' - imported by httpx._main (top-level)
missing module named 'rich.syntax' - imported by httpx._main (top-level)
missing module named 'rich.progress' - imported by httpx._main (top-level)
missing module named 'rich.markup' - imported by httpx._main (top-level)
missing module named 'rich.console' - imported by httpx._main (top-level)
missing module named 'pygments.util' - imported by httpx._main (top-level), _pytest._io.terminalwriter (delayed, optional)
missing module named pygments - imported by httpx._main (top-level), _pytest._io.terminalwriter (delayed, optional)
missing module named '_typeshed.wsgi' - imported by httpx._transports.wsgi (conditional)
missing module named 'twisted.trial' - imported by _pytest.unittest (delayed)
missing module named zope - imported by _pytest.unittest (delayed)
missing module named twisted - imported by _pytest.unittest (conditional)
missing module named 'pygments.lexers' - imported by _pytest._io.terminalwriter (delayed, optional)
missing module named 'pygments.formatters' - imported by _pytest._io.terminalwriter (delayed, optional)
missing module named exceptiongroup - imported by loguru._better_exceptions (conditional, optional), _pytest.runner (conditional), _pytest._code.code (conditional)
missing module named argcomplete - imported by _pytest._argcomplete (conditional, optional)
missing module named pexpect - imported by _pytest.pytester (conditional), _pytest.legacypath (conditional)
missing module named jiter.from_json - imported by jiter (top-level), openai.lib.streaming.chat._completions (top-level)
missing module named sounddevice - imported by openai._extras.sounddevice_proxy (delayed, conditional, optional)
missing module named 'werkzeug.routing' - imported by websockets.asyncio.router (top-level), websockets.sync.router (top-level)
missing module named 'werkzeug.exceptions' - imported by websockets.sync.router (top-level)
missing module named werkzeug - imported by websockets.asyncio.router (top-level)
missing module named 'python_socks.async_' - imported by websockets.asyncio.client (optional)
missing module named python_socks - imported by websockets.asyncio.client (optional), websockets.sync.client (optional)
missing module named 'python_socks.sync' - imported by websockets.sync.client (optional)
missing module named httpx_aiohttp - imported by openai._base_client (optional)
missing module named rapidfuzz - imported by langsmith._internal._edit_distance (delayed, optional)
missing module named PIL - imported by langchain_openai.chat_models.base (delayed, optional)
missing module named 'numpy.typing' - imported by tiktoken.core (conditional)
missing module named regex.DEFAULT_VERSION - imported by regex (delayed, optional), regex.regex (delayed, optional)
missing module named async_timeout - imported by aiohttp.helpers (conditional), aiohttp.web_ws (conditional), aiohttp.client_ws (conditional)
missing module named 'gunicorn.workers' - imported by aiohttp.worker (top-level), uvicorn.workers (top-level)
missing module named gunicorn - imported by aiohttp.worker (top-level)
missing module named aiodns - imported by aiohttp.resolver (optional)
missing module named aiocontextvars - imported by loguru._contextvars (delayed, conditional)
missing module named ipykernel - imported by loguru._colorama (delayed, conditional, optional)
missing module named ujson - imported by fastapi.responses (optional)
missing module named dirty_equals - imported by fastapi.utils (delayed)
missing module named 'gunicorn.arbiter' - imported by uvicorn.workers (top-level)
missing module named watchgod - imported by uvicorn.supervisors.watchgodreload (top-level)
missing module named 'wsproto.utilities' - imported by uvicorn.protocols.websockets.wsproto_impl (top-level)
missing module named 'wsproto.extensions' - imported by uvicorn.protocols.websockets.wsproto_impl (top-level)
missing module named 'wsproto.connection' - imported by uvicorn.protocols.websockets.wsproto_impl (top-level)
missing module named wsproto - imported by uvicorn.protocols.websockets.wsproto_impl (top-level), uvicorn.protocols.websockets.auto (optional)
missing module named a2wsgi - imported by uvicorn.middleware.wsgi (optional)
missing module named win32evtlog - imported by logging.handlers (delayed, optional)
missing module named win32evtlogutil - imported by logging.handlers (delayed, optional)
