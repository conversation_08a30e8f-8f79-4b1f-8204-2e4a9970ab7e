#!/usr/bin/env python3
"""
模拟测试异步修复的脚本
使用模拟的LLM调用来验证异步功能
"""

import asyncio
import sys
import os
import time
from unittest.mock import AsyncMock, patch

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.intelligent_question_service import IntelligentQuestionService
from app.core.state_manager import TaskStatus


async def mock_llm_call(prompt, *args, **kwargs):
    """模拟LLM调用，返回固定结果"""
    # 模拟网络延迟
    await asyncio.sleep(0.5)
    return f"模拟生成的试题内容：{prompt[:50]}..."


async def test_async_vs_sync_performance():
    """测试异步vs同步性能对比"""
    print("=" * 60)
    print("异步vs同步性能对比测试")
    print("=" * 60)
    
    service = IntelligentQuestionService()
    
    # 测试数据
    test_ques_type_post = {
        "QuesCount": 5,
        "QuesTypeName": "单选题",
        "QuesTypeId": 1,
        "Difficulty": "中等",
        "KnowledgePoints": "测试知识点"
    }
    
    test_planning_tasks = {
        "subquestion_tasks": [
            {"question_index": i, "difficulty": "中等", "knowledge_points": f"测试知识点{i}"}
            for i in range(1, 6)
        ],
        "planning_type": "basic"
    }
    
    async def status_callback(status, message, details=None):
        print(f"状态: {message}")
    
    # 使用mock替换LLM调用
    with patch.object(service, '_call_llm_non_stream', side_effect=mock_llm_call):
        print("\n测试异步版本...")
        start_time = time.time()
        
        results = await service._generate_questions_async(
            test_ques_type_post,
            "语文",
            test_planning_tasks,
            None,
            None,
            status_callback
        )
        
        async_time = time.time() - start_time
        print(f"异步版本耗时: {async_time:.2f}秒")
        print(f"生成试题数量: {len(results)}")
        
        # 模拟同步版本（串行执行）
        print("\n模拟同步版本（串行执行）...")
        start_time = time.time()
        
        sync_results = []
        for i, task in enumerate(test_planning_tasks["subquestion_tasks"]):
            prompt = f"生成试题{i+1}"
            result = await mock_llm_call(prompt)
            sync_results.append(result)
        
        sync_time = time.time() - start_time
        print(f"同步版本耗时: {sync_time:.2f}秒")
        print(f"生成试题数量: {len(sync_results)}")
        
        print(f"\n性能提升: {sync_time/async_time:.2f}倍")
        print(f"时间节省: {sync_time - async_time:.2f}秒")


async def test_concurrent_requests():
    """测试并发请求处理"""
    print("\n" + "=" * 60)
    print("并发请求处理测试")
    print("=" * 60)
    
    service = IntelligentQuestionService()
    
    async def simulate_request(request_id):
        """模拟一个请求"""
        test_ques_type_post = {
            "QuesCount": 3,
            "QuesTypeName": "单选题",
            "QuesTypeId": 1,
            "Difficulty": "中等",
            "KnowledgePoints": f"请求{request_id}的知识点"
        }
        
        test_planning_tasks = {
            "subquestion_tasks": [
                {"question_index": i, "difficulty": "中等", "knowledge_points": f"请求{request_id}-知识点{i}"}
                for i in range(1, 4)
            ],
            "planning_type": "basic"
        }
        
        async def status_callback(status, message, details=None):
            print(f"请求{request_id}: {message}")
        
        with patch.object(service, '_call_llm_non_stream', side_effect=mock_llm_call):
            start_time = time.time()
            results = await service._generate_questions_async(
                test_ques_type_post,
                "语文",
                test_planning_tasks,
                None,
                None,
                status_callback
            )
            duration = time.time() - start_time
            return f"请求{request_id}完成，耗时{duration:.2f}秒，生成{len(results)}个试题"
    
    # 创建5个并发请求
    print("启动5个并发请求...")
    start_time = time.time()
    
    tasks = [simulate_request(i) for i in range(1, 6)]
    results = await asyncio.gather(*tasks)
    
    total_time = time.time() - start_time
    
    print(f"\n并发处理完成，总耗时: {total_time:.2f}秒")
    for result in results:
        print(result)
    
    # 计算理论串行时间
    theoretical_serial_time = 5 * 3 * 0.5  # 5个请求 * 3个试题 * 0.5秒延迟
    print(f"\n理论串行时间: {theoretical_serial_time:.2f}秒")
    print(f"实际并发时间: {total_time:.2f}秒")
    print(f"并发效率: {theoretical_serial_time/total_time:.2f}倍提升")


async def test_non_blocking_behavior():
    """测试非阻塞行为"""
    print("\n" + "=" * 60)
    print("非阻塞行为测试")
    print("=" * 60)
    
    service = IntelligentQuestionService()
    
    # 模拟长时间运行的任务
    async def long_running_task():
        print("开始长时间运行的任务...")
        with patch.object(service, '_call_llm_non_stream', side_effect=mock_llm_call):
            test_ques_type_post = {
                "QuesCount": 3,
                "QuesTypeName": "单选题",
                "QuesTypeId": 1
            }
            test_planning_tasks = {
                "subquestion_tasks": [{"question_index": i} for i in range(1, 4)],
                "planning_type": "basic"
            }
            
            results = await service._generate_questions_async(
                test_ques_type_post, "语文", test_planning_tasks
            )
            print("长时间任务完成")
            return results
    
    # 模拟状态查询任务
    async def status_query_task():
        for i in range(10):
            await asyncio.sleep(0.2)
            print(f"状态查询 {i+1}: 系统正常运行")
        return "状态查询完成"
    
    print("同时运行长时间任务和状态查询...")
    start_time = time.time()
    
    # 并发运行两个任务
    long_task = asyncio.create_task(long_running_task())
    status_task = asyncio.create_task(status_query_task())
    
    # 等待两个任务完成
    long_result, status_result = await asyncio.gather(long_task, status_task)
    
    total_time = time.time() - start_time
    
    print(f"\n两个任务都完成，总耗时: {total_time:.2f}秒")
    print(f"长时间任务结果: 生成了{len(long_result)}个试题")
    print(f"状态查询结果: {status_result}")
    print("\n✅ 验证：状态查询没有被长时间任务阻塞！")


async def main():
    """主测试函数"""
    print("智能命题服务异步功能验证测试")
    
    # 测试1: 性能对比
    await test_async_vs_sync_performance()
    
    # 测试2: 并发处理
    await test_concurrent_requests()
    
    # 测试3: 非阻塞行为
    await test_non_blocking_behavior()
    
    print("\n" + "=" * 60)
    print("✅ 所有测试完成！异步修复验证成功！")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
