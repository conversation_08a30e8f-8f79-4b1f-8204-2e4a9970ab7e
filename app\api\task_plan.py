from fastapi import APIRouter, HTTPException
from app.models.schemas import (
    TaskPlanRequest, TaskPlanResponse,
    StandardResponse, ErrorResponse
)
from app.services.task_plan_service import task_plan_service
from app.core.state_manager import task_manager, TaskStatus
import uuid
from datetime import datetime
from loguru import logger

router = APIRouter()


@router.post("/CreateTaskPlan", response_model=TaskPlanResponse)
async def create_task_plan(request: TaskPlanRequest):
    """
    创建智能任务规划
    
    功能：
    - 基于材料和命题要求生成任务规划
    - 智能分解复杂命题任务
    - 估算完成时间和资源需求
    - 返回结构化的任务规划
    """
    try:
        # 生成任务ID
        task_id = request.task_id or str(uuid.uuid4())
        
        # 创建任务
        await task_manager.create_task(task_id)
        
        # 更新任务状态
        await task_manager.update_task_status(
            task_id,
            TaskStatus.PROCESSING,
            step_info={
                "step": "creating_plan",
                "progress": 0,
                "detail": "开始创建任务规划"
            }
        )
        
        logger.info(f"开始创建任务规划: {task_id}")
        
        # 调用服务创建任务规划
        result = await task_plan_service.create_task_plan(
            material=request.material,
            question_tasks=request.question_tasks,
            task_id=task_id
        )
        
        # 更新任务状态为完成
        await task_manager.update_task_status(
            task_id,
            TaskStatus.COMPLETED,
            result=result
        )
        
        # 构建响应
        response = TaskPlanResponse(
            task_plan=result["task_plan"],
            total_questions=result["total_questions"],
            estimated_time=result["estimated_time"],
            task_id=task_id,
            created_at=datetime.now()
        )
        
        logger.info(f"任务规划创建完成: {task_id}")
        return response
        
    except Exception as e:
        logger.error(f"任务规划创建失败: {e}")
        
        # 更新任务状态为失败
        if 'task_id' in locals():
            await task_manager.update_task_status(
                task_id,
                TaskStatus.FAILED,
                error_message=str(e)
            )
        
        raise HTTPException(
            status_code=500,
            detail=f"任务规划创建失败: {str(e)}"
        )


@router.post("/OptimizeTaskPlan")
async def optimize_task_plan(
    task_plan: list,
    optimization_goals: list = None
):
    """
    优化任务规划
    
    功能：
    - 分析现有任务规划的效率
    - 提供优化建议
    - 重新排序和组织任务
    """
    try:
        task_id = str(uuid.uuid4())
        
        # 创建任务
        await task_manager.create_task(task_id)
        
        logger.info(f"开始优化任务规划: {task_id}")
        
        # 调用服务优化任务规划
        result = await task_plan_service.optimize_task_plan(
            task_plan=task_plan,
            optimization_goals=optimization_goals or [],
            task_id=task_id
        )
        
        # 更新任务状态为完成
        await task_manager.update_task_status(
            task_id,
            TaskStatus.COMPLETED,
            result=result
        )
        
        return StandardResponse(
            code=200,
            msg="任务规划优化完成",
            data={
                "task_id": task_id,
                "optimized_plan": result["optimized_plan"],
                "optimization_summary": result["optimization_summary"],
                "improvement_percentage": result.get("improvement_percentage", 0)
            }
        )
        
    except Exception as e:
        logger.error(f"任务规划优化失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"任务规划优化失败: {str(e)}"
        )


@router.get("/GetTaskPlanStatus/{task_id}")
async def get_task_plan_status(task_id: str):
    """
    查询任务规划状态
    """
    try:
        task_info = await task_manager.get_task_info(task_id)
        
        if not task_info:
            raise HTTPException(
                status_code=404,
                detail=f"任务不存在: {task_id}"
            )
        
        return StandardResponse(
            code=200,
            msg="查询成功",
            data={
                "task_id": task_id,
                "status": task_info.status.value,
                "created_at": task_info.created_at,
                "updated_at": task_info.updated_at,
                "result": task_info.result,
                "error_message": task_info.error_message
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询任务状态失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"查询任务状态失败: {str(e)}"
        )


@router.post("/ValidateTaskPlan")
async def validate_task_plan(task_plan: list):
    """
    验证任务规划的合理性
    
    功能：
    - 检查任务规划的逻辑性
    - 验证资源分配的合理性
    - 识别潜在的问题和冲突
    """
    try:
        task_id = str(uuid.uuid4())
        
        logger.info(f"开始验证任务规划: {task_id}")
        
        # 调用服务验证任务规划
        result = await task_plan_service.validate_task_plan(
            task_plan=task_plan,
            task_id=task_id
        )
        
        return StandardResponse(
            code=200,
            msg="任务规划验证完成",
            data={
                "task_id": task_id,
                "is_valid": result["is_valid"],
                "validation_report": result["validation_report"],
                "suggestions": result.get("suggestions", [])
            }
        )
        
    except Exception as e:
        logger.error(f"任务规划验证失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"任务规划验证失败: {str(e)}"
        )
