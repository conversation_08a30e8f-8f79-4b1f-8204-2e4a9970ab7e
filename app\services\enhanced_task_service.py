from typing import List, Dict, Any, Optional
import time
from loguru import logger
from app.core.state_manager import task_manager, TaskStatus


class EnhancedTaskService:
    """增强任务管理服务"""
    
    def __init__(self):
        self.logger = logger
        # 简化实现，使用内存存储
        self.task_logs = {}
        self.task_priorities = {}
        self.scheduled_tasks = {}
    
    async def get_task_details(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务详细信息"""
        
        task_info = await task_manager.get_task_info(task_id)
        
        if not task_info:
            return None
        
        # 获取任务日志
        logs = self.task_logs.get(task_id, [])
        
        # 获取任务优先级
        priority = self.task_priorities.get(task_id, 5)
        
        # 计算执行时间
        execution_time = 0
        if task_info.updated_at and task_info.created_at:
            execution_time = task_info.updated_at - task_info.created_at
        
        return {
            "task_id": task_id,
            "status": task_info.status.value,
            "created_at": task_info.created_at,
            "updated_at": task_info.updated_at,
            "execution_time": execution_time,
            "priority": priority,
            "result": task_info.result,
            "error_message": task_info.error_message,
            "step_info": task_info.step_info,
            "logs_count": len(logs),
            "performance_metrics": {
                "memory_usage": "N/A",
                "cpu_usage": "N/A",
                "network_io": "N/A"
            }
        }
    
    async def list_tasks(
        self,
        status: Optional[str] = None,
        task_type: Optional[str] = None,
        page: int = 1,
        page_size: int = 20
    ) -> Dict[str, Any]:
        """列出任务列表"""
        
        # 获取所有任务（简化实现）
        all_tasks = []
        
        # 这里应该从task_manager获取所有任务
        # 为简化，返回示例数据
        for i in range(50):  # 模拟50个任务
            task_id = f"task_{i+1}"
            all_tasks.append({
                "task_id": task_id,
                "status": "COMPLETED" if i % 3 == 0 else "PROCESSING",
                "task_type": "question_generation",
                "created_at": time.time() - (i * 3600),
                "priority": self.task_priorities.get(task_id, 5)
            })
        
        # 应用筛选
        filtered_tasks = all_tasks
        if status:
            filtered_tasks = [t for t in filtered_tasks if t["status"] == status]
        if task_type:
            filtered_tasks = [t for t in filtered_tasks if t["task_type"] == task_type]
        
        # 分页
        total_count = len(filtered_tasks)
        total_pages = (total_count + page_size - 1) // page_size
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        
        tasks = filtered_tasks[start_idx:end_idx]
        
        return {
            "tasks": tasks,
            "total_count": total_count,
            "page": page,
            "page_size": page_size,
            "total_pages": total_pages
        }
    
    async def monitor_tasks(self, task_ids: Optional[List[str]] = None) -> Dict[str, Any]:
        """监控任务状态"""
        
        if not task_ids:
            # 监控所有活跃任务
            task_ids = ["task_1", "task_2", "task_3"]  # 简化实现
        
        monitoring_results = []
        
        for task_id in task_ids:
            task_info = await task_manager.get_task_info(task_id)
            
            if task_info:
                # 检测异常情况
                is_timeout = False
                is_stuck = False
                
                if task_info.status == TaskStatus.PROCESSING:
                    # 检查是否超时（简化逻辑）
                    if time.time() - task_info.created_at > 3600:  # 1小时超时
                        is_timeout = True
                    
                    # 检查是否卡住（简化逻辑）
                    if time.time() - task_info.updated_at > 600:  # 10分钟无更新
                        is_stuck = True
                
                monitoring_results.append({
                    "task_id": task_id,
                    "status": task_info.status.value,
                    "is_timeout": is_timeout,
                    "is_stuck": is_stuck,
                    "execution_time": time.time() - task_info.created_at,
                    "last_update": task_info.updated_at
                })
        
        # 统计信息
        total_tasks = len(monitoring_results)
        running_tasks = len([t for t in monitoring_results if t["status"] == "PROCESSING"])
        timeout_tasks = len([t for t in monitoring_results if t["is_timeout"]])
        stuck_tasks = len([t for t in monitoring_results if t["is_stuck"]])
        
        return {
            "monitoring_results": monitoring_results,
            "statistics": {
                "total_tasks": total_tasks,
                "running_tasks": running_tasks,
                "timeout_tasks": timeout_tasks,
                "stuck_tasks": stuck_tasks
            },
            "alerts": [
                f"发现 {timeout_tasks} 个超时任务" if timeout_tasks > 0 else None,
                f"发现 {stuck_tasks} 个卡住任务" if stuck_tasks > 0 else None
            ]
        }
    
    async def retry_task(self, task_id: str) -> Dict[str, Any]:
        """重试失败的任务"""
        
        task_info = await task_manager.get_task_info(task_id)
        
        if not task_info:
            raise ValueError(f"任务不存在: {task_id}")
        
        if task_info.status != TaskStatus.FAILED:
            raise ValueError(f"只能重试失败的任务，当前状态: {task_info.status.value}")
        
        # 重置任务状态
        await task_manager.update_task_status(
            task_id,
            TaskStatus.PENDING,
            step_info={
                "step": "retrying",
                "progress": 0,
                "detail": "任务重试中"
            }
        )
        
        # 记录重试日志
        self._add_task_log(task_id, "INFO", "任务开始重试")
        
        return {
            "task_id": task_id,
            "retry_status": "initiated",
            "retry_time": time.time()
        }
    
    async def cancel_task(self, task_id: str, reason: str = "用户取消") -> Dict[str, Any]:
        """取消任务"""
        
        task_info = await task_manager.get_task_info(task_id)
        
        if not task_info:
            raise ValueError(f"任务不存在: {task_id}")
        
        if task_info.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
            raise ValueError(f"任务已结束，无法取消，当前状态: {task_info.status.value}")
        
        # 更新任务状态为取消
        await task_manager.update_task_status(
            task_id,
            TaskStatus.CANCELLED,
            step_info={
                "step": "cancelled",
                "progress": 0,
                "detail": f"任务已取消: {reason}"
            }
        )
        
        # 记录取消日志
        self._add_task_log(task_id, "INFO", f"任务已取消: {reason}")
        
        return {
            "task_id": task_id,
            "cancel_status": "success",
            "cancel_reason": reason,
            "cancel_time": time.time()
        }
    
    async def get_task_statistics(
        self,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取任务统计信息"""
        
        # 简化实现，返回模拟统计数据
        return {
            "total_tasks": 150,
            "completed_tasks": 120,
            "failed_tasks": 15,
            "cancelled_tasks": 10,
            "running_tasks": 5,
            "success_rate": 0.8,
            "average_execution_time": 45.5,
            "task_types": {
                "question_generation": 80,
                "material_processing": 30,
                "task_planning": 25,
                "optimization": 15
            },
            "daily_statistics": [
                {"date": "2025-07-29", "completed": 25, "failed": 3},
                {"date": "2025-07-28", "completed": 30, "failed": 2},
                {"date": "2025-07-27", "completed": 28, "failed": 4}
            ]
        }
    
    async def schedule_task(
        self,
        task_config: Dict[str, Any],
        schedule_time: Optional[str] = None,
        recurring: bool = False
    ) -> Dict[str, Any]:
        """调度任务"""
        
        schedule_id = f"schedule_{int(time.time())}"
        
        # 存储调度配置
        self.scheduled_tasks[schedule_id] = {
            "schedule_id": schedule_id,
            "task_config": task_config,
            "schedule_time": schedule_time,
            "recurring": recurring,
            "created_at": time.time(),
            "status": "scheduled"
        }
        
        return {
            "schedule_id": schedule_id,
            "status": "scheduled",
            "next_execution": schedule_time or "立即执行"
        }
    
    async def get_task_logs(
        self,
        task_id: str,
        log_level: str = "INFO",
        limit: int = 100
    ) -> Dict[str, Any]:
        """获取任务日志"""
        
        logs = self.task_logs.get(task_id, [])
        
        # 按级别筛选
        if log_level != "ALL":
            logs = [log for log in logs if log["level"] == log_level]
        
        # 限制数量
        logs = logs[-limit:] if len(logs) > limit else logs
        
        return {
            "task_id": task_id,
            "logs": logs,
            "total_count": len(self.task_logs.get(task_id, [])),
            "filtered_count": len(logs)
        }
    
    async def set_task_priority(self, task_id: str, priority: int) -> Dict[str, Any]:
        """设置任务优先级"""
        
        if priority < 1 or priority > 10:
            raise ValueError("优先级必须在1-10之间")
        
        self.task_priorities[task_id] = priority
        
        # 记录日志
        self._add_task_log(task_id, "INFO", f"任务优先级设置为: {priority}")
        
        return {
            "task_id": task_id,
            "priority": priority,
            "updated_at": time.time()
        }
    
    async def batch_operate_tasks(
        self,
        task_ids: List[str],
        operation: str,
        parameters: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """批量操作任务"""
        
        results = []
        successful_operations = 0
        failed_operations = 0
        
        for task_id in task_ids:
            try:
                if operation == "cancel":
                    result = await self.cancel_task(task_id, parameters.get("reason", "批量取消"))
                elif operation == "retry":
                    result = await self.retry_task(task_id)
                elif operation == "set_priority":
                    result = await self.set_task_priority(task_id, parameters.get("priority", 5))
                else:
                    raise ValueError(f"不支持的操作: {operation}")
                
                results.append({
                    "task_id": task_id,
                    "status": "success",
                    "result": result
                })
                successful_operations += 1
                
            except Exception as e:
                results.append({
                    "task_id": task_id,
                    "status": "failed",
                    "error": str(e)
                })
                failed_operations += 1
        
        return {
            "operation": operation,
            "total_tasks": len(task_ids),
            "successful_operations": successful_operations,
            "failed_operations": failed_operations,
            "results": results
        }
    
    def _add_task_log(self, task_id: str, level: str, message: str):
        """添加任务日志"""
        
        if task_id not in self.task_logs:
            self.task_logs[task_id] = []
        
        self.task_logs[task_id].append({
            "timestamp": time.time(),
            "level": level,
            "message": message
        })


# 创建全局服务实例
enhanced_task_service = EnhancedTaskService()
